# Conversation Storage System

## Overview

The MiCA Therapy Application now includes a comprehensive conversation storage system that automatically saves all conversation data to a PostgreSQL database in Supabase. This system captures messages, AI analysis data, therapeutic approaches, and session analytics for both single-therapist and multi-therapist comparative study sessions.

## Features

### 1. Message Storage
- **Complete Message Data**: Stores message content, timestamps, sender information, and metadata
- **AI Analysis Integration**: Automatically saves patient sentiment, motivation, and engagement analysis
- **Therapeutic Approach Tracking**: Records therapeutic techniques and approaches used by AI therapists
- **Processing Metrics**: Captures response times and confidence scores

### 2. AI Analysis Storage
- **Patient Analysis**: Sentiment, motivation level, engagement patterns, and readiness scores
- **Therapeutic Insights**: AI-generated analysis of patient responses and therapeutic effectiveness
- **Mode-Specific Analysis**: Specialized analysis for CBT, DBT, and psychodynamic therapeutic modes

### 3. Session Analytics
- **Conversation Metrics**: Total messages, session duration, response times
- **Progress Tracking**: Sentiment, engagement, and motivation progression over time
- **Technique Effectiveness**: Analysis of which therapeutic techniques were most effective

### 4. Multi-Therapist Support
- **Comparative Data**: Stores data for all three therapist personas (CBT-Only, MI Fixed, Dynamic Adaptive)
- **Performance Metrics**: Tracks effectiveness and engagement for each persona
- **Strategy State**: Records therapeutic strategy changes and decision points

## Database Schema

### Core Tables

#### `conversations`
- Stores conversation metadata, session information, and configuration
- Enhanced with session metadata, patient persona ID, and therapist mode

#### `messages`
- Stores individual messages with content, thinking processes, and metadata
- Includes processing time and confidence scores

#### `ai_analysis`
- Stores AI-generated analysis data for patient responses
- Supports multiple analysis types (patient_analysis, therapeutic_approach, mode_specific)

#### `therapeutic_approaches`
- Tracks therapeutic approaches and techniques used in each response
- Includes effectiveness predictions and rationale

#### `session_analytics`
- Aggregated session data including progression metrics
- Stores arrays of sentiment, engagement, and motivation data over time

#### `multi_therapist_sessions`
- Specialized data for multi-therapist comparative studies
- Tracks persona-specific performance and strategy states

## API Endpoints

### Conversation Management
- `GET /api/conversations` - List conversations with filtering and pagination
- `GET /api/conversations/:id` - Get specific conversation with optional related data
- `GET /api/conversations/:id/complete` - Get complete conversation data
- `DELETE /api/conversations/:id` - Mark conversation as completed

### Message and Analysis Data
- `GET /api/conversations/:id/messages` - Get conversation messages with filtering
- `GET /api/conversations/:id/analysis` - Get AI analysis data
- `GET /api/conversations/:id/analytics` - Get session analytics

### Multi-Therapist Data
- `GET /api/conversations/:id/multi-therapist` - Get all persona session data
- `GET /api/conversations/:id/multi-therapist/:persona` - Get specific persona data

## Usage

### Automatic Storage
The system automatically stores data during conversations:

```typescript
// Single therapist conversation
const orchestrator = new ConversationOrchestrator(conversationId, config, patientPersonaId);
await orchestrator.startConversation(); // Automatically creates DB record

// Multi-therapist conversation  
const multiOrchestrator = new MultiTherapistConversationOrchestrator(conversationId, config, patientPersonaId);
await multiOrchestrator.startConversation(); // Creates DB record with persona data
```

### Manual Data Access
Access stored data through the database service:

```typescript
import { databaseService } from './services/database/index.js';

// Get complete conversation data
const conversationData = await databaseService.getCompleteConversationData(conversationId);

// Get specific analysis
const patientAnalysis = await databaseService.getConversationAnalysis(conversationId, {
  analysisType: 'patient_analysis'
});

// Get session analytics
const analytics = await databaseService.getSessionAnalytics(conversationId);
```

### API Usage
Retrieve data via REST API:

```bash
# Get conversation with messages and analysis
curl "http://localhost:3001/api/conversations/123?includeMessages=true&includeAnalysis=true"

# Get session analytics
curl "http://localhost:3001/api/conversations/123/analytics"

# Get multi-therapist comparison data
curl "http://localhost:3001/api/conversations/123/multi-therapist"
```

## Data Validation

The system includes comprehensive validation to ensure data consistency:

- **Schema Validation**: Ensures all required fields are present and correctly typed
- **Business Logic Validation**: Validates relationships and constraints
- **Frontend/Backend Consistency**: Maintains consistency with existing message schemas

## Database Migration

### Running Migrations
Apply the enhanced schema to your Supabase database:

```bash
# Apply all pending migrations
npm run migrate

# Rollback last migration (development only)
npm run migrate:rollback

# Check migration status
npm run migrate:status
```

### Migration Files
- `001_initial_schema.sql` - Original conversation and message tables
- `002_cbt_evaluations.sql` - CBT evaluation system
- `003_enhanced_conversation_storage.sql` - Enhanced storage system (new)

## Configuration

### Environment Variables
Ensure these environment variables are set:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key
```

### Database Setup
1. Run migrations to create the enhanced schema
2. Verify Row Level Security (RLS) policies are appropriate for your use case
3. Configure any additional indexes for performance optimization

## Performance Considerations

### Indexing
The system includes optimized indexes for:
- Message retrieval by conversation and timestamp
- Analysis data queries by type and date
- Multi-therapist session lookups

### Data Retention
Consider implementing data retention policies for:
- Old conversation data
- Detailed analysis records
- Session analytics aggregations

## Security

### Row Level Security (RLS)
- All tables have RLS enabled
- Current policies allow all operations (customize for production)
- Consider implementing user-based access controls

### Data Privacy
- Patient data is stored with appropriate safeguards
- AI analysis data includes only therapeutic insights
- Session metadata excludes personally identifiable information

## Monitoring and Maintenance

### Health Checks
- Database connection testing on startup
- Migration status verification
- Data consistency validation

### Backup and Recovery
- Regular database backups through Supabase
- Migration rollback capabilities for development
- Data export functionality for research purposes

## Future Enhancements

### Planned Features
- Real-time analytics dashboard
- Advanced querying and filtering
- Data export for research analysis
- Integration with evaluation systems
- Performance optimization based on usage patterns

### Extensibility
The system is designed to be extensible:
- Additional analysis types can be easily added
- New therapeutic modes can be integrated
- Custom session metrics can be implemented
- API endpoints can be extended for specific use cases
