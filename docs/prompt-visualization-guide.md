# Prompt Construction Pipeline Visualization

## Overview

The Prompt Construction Pipeline Visualization is a new web page in the MiCA therapy simulation system that provides an educational and interactive view of how therapeutic conversation prompts are built step-by-step. This feature helps therapists, developers, and researchers understand the sophisticated prompt engineering that goes into creating realistic patient personas and effective therapist AI responses.

## Features

### 1. **Interactive Pipeline Visualization**
- **Patient Persona Pipeline**: Shows how base templates are combined with persona data to create final patient prompts
- **Therapist AI Pipeline**: Demonstrates how therapeutic approaches and techniques are layered to build therapist prompts
- **Side-by-Side Comparison**: View both pipelines simultaneously to understand the complete conversation setup

### 2. **Dynamic Configuration**
- **Persona Selection**: Choose from 5 distinct patient personas (<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>)
- **Therapeutic Approach Selection**: Switch between different therapeutic approaches (CBT, MI, etc.)
- **Technique Selection**: Select specific therapeutic techniques to see how they modify prompts

### 3. **Educational Components**
- **Step-by-Step Breakdown**: Each pipeline step shows template, variables, and output
- **Variable Inspector**: Detailed view of all template variables and their values
- **Prompt Preview**: Full-screen view of final prompts with search and syntax highlighting
- **Metadata Display**: Statistics about prompt construction (character counts, variable counts, etc.)

## How to Access

1. Navigate to the MiCA application at `http://localhost:5173`
2. Click on "Prompt Pipeline" in the sidebar navigation
3. The page will load with the default patient persona (Emma) and therapeutic approach (CBT)

## Using the Visualization

### Patient Persona Pipeline Tab

1. **Select a Persona**: Use the dropdown to choose from available patient personas
2. **View Pipeline Steps**:
   - **Step 1**: Base Patient Template - Generic template with placeholders
   - **Step 2**: Persona Data Injection - Specific psychological profile data
   - **Step 3**: Template Variable Substitution - Replace placeholders with actual data
   - **Step 4**: Final Patient Prompt - Complete prompt ready for AI model

3. **Interactive Features**:
   - Click on any step to expand and see detailed information
   - Use "Show Variables" to inspect all template variables
   - Use "Show Final Prompt" to view the complete rendered prompt

### Therapist AI Pipeline Tab

1. **Select Therapeutic Approach**: Choose from available approaches (CBT, MI, etc.)
2. **Select Specific Technique**: Pick a specific therapeutic technique
3. **View Pipeline Steps**:
   - **Step 1**: Base System Prompt - Core therapist identity
   - **Step 2**: Therapeutic Approach Addition - Approach-specific context
   - **Step 3**: Technique-Specific Prompt - Technique guidance
   - **Step 4**: Final Assembly - Complete therapist prompt

### Side-by-Side Comparison Tab

View both patient and therapist pipelines simultaneously to understand how the complete therapeutic conversation system is constructed.

## Technical Implementation

### Backend API Endpoints

- `GET /api/prompt-visualization/config` - Get all prompt configuration data
- `GET /api/prompt-visualization/therapist-pipeline` - Get therapist prompt construction pipeline
- `GET /api/prompt-visualization/patient-pipeline/:personaId` - Get patient prompt construction pipeline
- `GET /api/prompt-visualization/template-variables/:personaId` - Get template variables for a persona

### Frontend Components

- `PromptPipelineVisualization.svelte` - Main visualization component
- `PipelineStep.svelte` - Individual pipeline step component
- `VariableInspector.svelte` - Template variable inspector modal
- `PromptPreview.svelte` - Full prompt preview modal

## Educational Value

This visualization helps users understand:

1. **Prompt Engineering Complexity**: How sophisticated prompts are built from multiple layers
2. **Persona Consistency**: How patient psychological profiles are systematically incorporated
3. **Therapeutic Approach Integration**: How different therapeutic methods are embedded in AI behavior
4. **Template System**: How reusable templates enable consistent prompt generation
5. **Variable Substitution**: How dynamic data is injected into static templates

## Use Cases

### For Therapists
- Understand how AI patient personas are constructed
- See how therapeutic approaches are implemented in AI responses
- Learn about the psychological frameworks underlying the simulation

### For Developers
- Understand the prompt construction architecture
- See how templates and variables work together
- Debug and optimize prompt generation

### For Researchers
- Analyze the prompt engineering methodology
- Study how psychological theories are translated into AI prompts
- Evaluate the consistency and completeness of persona construction

## Future Enhancements

Potential improvements to the visualization:
- Export functionality for prompts and configurations
- Prompt comparison tools
- Performance metrics for different prompt variations
- Integration with conversation history to show prompt effectiveness
- Real-time prompt editing and testing capabilities

## Troubleshooting

If the visualization doesn't load:
1. Ensure the backend server is running on port 3000
2. Check that the frontend proxy configuration is correct
3. Verify that all prompt configuration files are present in `backend/src/config/prompts/`
4. Check browser console for any JavaScript errors

## Related Documentation

- [Centralized Prompt Configuration System](../backend/src/config/prompts/README.md)
- [Patient Personas Documentation](../backend/src/data/patient-personas.ts)
- [Template Rendering System](../backend/src/services/template-renderer.ts)
