# Workflow Dashboard Integration Guide
## MiCA Therapy Application - Visual Workflow System

### Overview
This guide provides step-by-step instructions for integrating the new visual workflow dashboard into the existing MiCA therapy application. The workflow dashboard provides real-time visualization of the LangGraph therapeutic workflow execution.

### Prerequisites
- LangGraph Phase 1 integration completed
- Existing multi-therapist conversation system functional
- WebSocket communication established

### Integration Steps

#### Step 1: Import Workflow Components

Add the workflow dashboard to your main conversation interface:

```typescript
// In your main conversation component (e.g., +page.svelte)
import WorkflowDashboard from '../components/workflow/WorkflowDashboard.svelte';

// Component state
let showWorkflowDashboard = true;
let workflowState = null;
let workflowHistory = [];
let dashboardPosition = 'overlay'; // 'overlay', 'sidebar', or 'bottom'
```

#### Step 2: WebSocket Message Handling

Enhance your WebSocket message handler to process workflow events:

```typescript
function handleWebSocketMessage(data: any) {
  console.log(`🔄 Handling message type: ${data.type}`);

  switch (data.type) {
    // Existing cases...
    
    case 'workflow_progress':
      workflowState = data.data;
      break;
      
    case 'patient_analysis_complete':
      // Update UI with patient analysis results
      console.log('Patient analysis completed:', data.data.analysis);
      break;
      
    case 'approaches_selected':
      // Update UI with selected therapeutic approaches
      console.log('Therapeutic approaches selected:', data.data.approaches);
      break;
      
    case 'persona_response_ready':
      // Handle individual persona responses
      console.log(`${data.data.persona} response ready:`, data.data.response);
      break;
      
    case 'workflow_complete':
      // Workflow completed successfully
      workflowHistory = [...workflowHistory, workflowState];
      workflowState = data.data;
      break;
      
    case 'workflow_error':
      // Handle workflow errors
      console.error('Workflow error:', data.data.error);
      if (workflowState) {
        workflowState.workflowError = {
          step: data.data.step,
          error: data.data.error,
          timestamp: data.data.timestamp
        };
      }
      break;
  }
}
```

#### Step 3: Dashboard Integration

Add the workflow dashboard to your template:

```svelte
<!-- Workflow Dashboard -->
{#if showWorkflowDashboard && isMultiTherapistMode}
  <WorkflowDashboard
    {workflowState}
    {workflowHistory}
    {therapeuticApproaches}
    isVisible={showWorkflowDashboard}
    position={dashboardPosition}
    on:close={() => showWorkflowDashboard = false}
    on:minimize={(e) => console.log('Dashboard minimized:', e.detail.minimized)}
    on:workflowAction={handleWorkflowAction}
    on:nodeClick={handleNodeClick}
    on:selectHistoryItem={handleHistorySelection}
  />
{/if}
```

#### Step 4: Dashboard Controls

Add controls to show/hide and position the dashboard:

```svelte
<!-- Dashboard Controls -->
<div class="workflow-controls">
  <button
    class="btn btn-sm"
    on:click={() => showWorkflowDashboard = !showWorkflowDashboard}
  >
    {showWorkflowDashboard ? '🔽' : '🔼'} Workflow
  </button>
  
  {#if showWorkflowDashboard}
    <select bind:value={dashboardPosition} class="select select-sm">
      <option value="overlay">Overlay</option>
      <option value="sidebar">Sidebar</option>
      <option value="bottom">Bottom</option>
    </select>
  {/if}
</div>
```

#### Step 5: Event Handlers

Implement handlers for dashboard events:

```typescript
function handleWorkflowAction(event: CustomEvent) {
  const { action } = event.detail;
  
  switch (action) {
    case 'pause':
      // Send pause command to backend
      if (wsConnection) {
        wsConnection.send(JSON.stringify({
          type: 'pause_workflow',
          conversationId
        }));
      }
      break;
      
    case 'resume':
      // Send resume command to backend
      if (wsConnection) {
        wsConnection.send(JSON.stringify({
          type: 'resume_workflow',
          conversationId
        }));
      }
      break;
      
    case 'reset':
      // Reset workflow state
      workflowState = null;
      break;
      
    case 'step_forward':
      // Step through workflow (if in step mode)
      if (wsConnection) {
        wsConnection.send(JSON.stringify({
          type: 'step_workflow',
          conversationId
        }));
      }
      break;
  }
}

function handleNodeClick(event: CustomEvent) {
  const { nodeId, node } = event.detail;
  console.log('Workflow node clicked:', nodeId, node);
  
  // You can implement additional logic here, such as:
  // - Showing detailed node information in a modal
  // - Highlighting related UI elements
  // - Providing node-specific actions
}

function handleHistorySelection(event: CustomEvent) {
  const { workflow } = event.detail;
  console.log('Historical workflow selected:', workflow);
  
  // You can implement logic to:
  // - Load historical workflow state for comparison
  // - Show detailed analysis of past workflows
  // - Export workflow data for research
}
```

### Styling Integration

#### CSS Variables
The workflow components use CSS custom properties that integrate with your existing theme:

```css
:root {
  /* Workflow-specific colors */
  --workflow-primary: #3B82F6;
  --workflow-success: #10B981;
  --workflow-warning: #F59E0B;
  --workflow-error: #EF4444;
  
  /* Node type colors */
  --workflow-analysis: #EFF6FF;
  --workflow-decision: #F0FDF4;
  --workflow-generation: #FEF3F2;
  --workflow-synthesis: #F5F3FF;
}

/* Dark mode support */
[data-theme="dark"] {
  --workflow-analysis: #1E3A8A;
  --workflow-decision: #065F46;
  --workflow-generation: #991B1B;
  --workflow-synthesis: #581C87;
}
```

#### Responsive Design
The dashboard automatically adapts to different screen sizes:

```css
/* Mobile adjustments */
@media (max-width: 768px) {
  .workflow-dashboard.overlay {
    width: calc(100vw - 1rem);
    right: 0.5rem;
    top: 0.5rem;
  }
}
```

### Backend Integration

#### Enhanced WebSocket Events
Ensure your backend sends the new workflow events:

```typescript
// In your WebSocket service
function broadcastWorkflowUpdate(conversationId: string, workflowData: any) {
  const message = {
    type: 'workflow_progress',
    data: workflowData,
    timestamp: new Date().toISOString(),
    conversationId
  };
  
  broadcastToConversation(conversationId, message);
}
```

#### Workflow Control Endpoints
Add endpoints for workflow control (optional):

```typescript
// REST endpoints for workflow control
app.post('/api/conversations/:id/workflow/pause', async (req, res) => {
  // Pause workflow logic
});

app.post('/api/conversations/:id/workflow/resume', async (req, res) => {
  // Resume workflow logic
});

app.post('/api/conversations/:id/workflow/step', async (req, res) => {
  // Step through workflow logic
});
```

### Testing the Integration

#### 1. Basic Functionality Test
1. Start a multi-therapist conversation
2. Verify the workflow dashboard appears
3. Check that workflow progress updates in real-time
4. Test dashboard controls (minimize, close, position changes)

#### 2. Workflow Visualization Test
1. Send a patient message
2. Verify each workflow step is visualized correctly
3. Check that node statuses update (pending → active → completed)
4. Verify therapeutic approaches are displayed correctly

#### 3. Error Handling Test
1. Simulate a workflow error
2. Verify error visualization in the dashboard
3. Check that fallback mechanisms work correctly
4. Ensure error information is displayed clearly

#### 4. Performance Test
1. Run multiple conversations simultaneously
2. Verify dashboard performance remains smooth
3. Check memory usage with workflow history
4. Test with different dashboard positions

### Troubleshooting

#### Common Issues

**Dashboard not appearing:**
- Check that `isMultiTherapistMode` is true
- Verify `showWorkflowDashboard` state
- Ensure WebSocket connection is established

**Workflow updates not showing:**
- Verify WebSocket message handling for new event types
- Check browser console for JavaScript errors
- Ensure backend is sending enhanced workflow events

**Performance issues:**
- Limit workflow history size (implement pagination)
- Use `compact` mode for smaller screens
- Consider debouncing rapid workflow updates

**Styling conflicts:**
- Check CSS specificity issues
- Verify dark mode theme variables
- Ensure responsive breakpoints work correctly

### Advanced Configuration

#### Custom Therapeutic Approaches
You can customize the therapeutic approach visualization:

```typescript
const therapeuticApproaches = {
  'cbt-only': {
    name: 'CBT-Only',
    color: '#3B82F6',
    description: 'Cognitive Behavioral Therapy approach'
  },
  'mi-fixed-pretreatment': {
    name: 'MI → CBT',
    color: '#10B981',
    description: 'Motivational Interviewing followed by CBT'
  },
  'dynamic-adaptive': {
    name: 'Adaptive',
    color: '#8B5CF6',
    description: 'Dynamic switching between approaches'
  }
};
```

#### Dashboard Positioning
Customize dashboard behavior for different use cases:

```typescript
// Research/supervision mode - always visible sidebar
const researchMode = {
  position: 'sidebar',
  showControls: true,
  compact: false
};

// Clinical mode - minimalist overlay
const clinicalMode = {
  position: 'overlay',
  showControls: false,
  compact: true
};
```

This integration guide provides everything needed to successfully implement the visual workflow dashboard in your therapy application. The dashboard enhances transparency, provides valuable insights, and creates an engaging user experience while maintaining the robust functionality of the existing system.
