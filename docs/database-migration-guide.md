# PostgreSQL Migration Guide

This guide covers the migration from Supabase to a local PostgreSQL database running in Docker for the MiCA project.

## Overview

The MiCA project has been migrated from Supabase (cloud PostgreSQL) to a local PostgreSQL database running in Docker containers. This provides:

- **Full control** over the database environment
- **No external dependencies** for development
- **Consistent development environment** across team members
- **Cost savings** by eliminating cloud database costs
- **Better performance** for local development

## Prerequisites

- Docker and Docker Compose installed
- Node.js 18+ installed
- Git repository cloned

## Quick Start

### 1. Start the Database

```bash
# Start only the PostgreSQL container
npm run db:up

# Or start all services including the database
npm run docker:up
```

### 2. Run Database Migrations

```bash
# Run all pending migrations
npm run db:migrate

# Check migration status
npm run db:migrate:status

# Test database connection
npm run db:migrate:test
```

### 3. Start the Application

```bash
# Development mode (with hot reload)
npm run dev

# Or with Docker (recommended)
npm run docker:up
```

## Database Configuration

### Environment Variables

The following environment variables are used for PostgreSQL configuration:

```env
# PostgreSQL Database Configuration
POSTGRES_DB=mica_dev
POSTGRES_USER=mica_user
POSTGRES_PASSWORD=mica_password
POSTGRES_PORT=5432
DATABASE_URL=postgresql://mica_user:mica_password@localhost:5432/mica_dev
```

### Docker Configuration

The PostgreSQL service is configured in `docker-compose.yml`:

```yaml
postgres:
  image: postgres:15-alpine
  container_name: mica-postgres
  restart: unless-stopped
  environment:
    POSTGRES_DB: ${POSTGRES_DB:-mica_dev}
    POSTGRES_USER: ${POSTGRES_USER:-mica_user}
    POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-mica_password}
  ports:
    - "${POSTGRES_PORT:-5432}:5432"
  volumes:
    - postgres_data:/var/lib/postgresql/data
    - ./database/init:/docker-entrypoint-initdb.d:ro
  healthcheck:
    test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mica_user} -d ${POSTGRES_DB:-mica_dev}"]
    interval: 10s
    timeout: 5s
    retries: 5
    start_period: 30s
```

## Database Operations

### Starting/Stopping the Database

```bash
# Start PostgreSQL container only
npm run db:up

# Stop PostgreSQL container
npm run db:down

# View PostgreSQL logs
npm run db:logs

# Start all services
npm run docker:up

# Stop all services
npm run docker:down
```

### Running Migrations

```bash
# Run all pending migrations
npm run db:migrate

# Check which migrations have been applied
npm run db:migrate:status

# Test database connection
npm run db:migrate:test
```

### Direct Database Access

```bash
# Connect to PostgreSQL using Docker
docker exec -it mica-postgres psql -U mica_user -d mica_dev

# Or using local psql client (if installed)
psql postgresql://mica_user:mica_password@localhost:5432/mica_dev
```

## Migration Details

### What Changed

1. **Database Client**: Replaced `@supabase/supabase-js` with native `pg` (node-postgres)
2. **Connection Management**: Implemented connection pooling for better performance
3. **Query Interface**: Converted Supabase query builder calls to raw SQL
4. **Environment Variables**: Updated to use PostgreSQL connection strings
5. **Docker Configuration**: Enabled PostgreSQL service with proper health checks

### Database Schema

The database schema remains identical to the Supabase version:

- `conversations` - Main conversation records
- `messages` - Individual messages in conversations
- `ai_analysis` - AI analysis data for messages
- `therapeutic_approaches` - Therapeutic techniques used
- `session_analytics` - Session-level analytics
- `multi_therapist_sessions` - Multi-therapist comparison data
- `cbt_evaluations` - CBT effectiveness evaluations
- `evaluation_dimensions` - Detailed evaluation metrics

### Migration Scripts

- `scripts/migrate-postgres.ts` - Main migration runner
- `database/migrations/` - SQL migration files
- `database/init/` - Database initialization scripts

## Troubleshooting

### Database Connection Issues

1. **Check if PostgreSQL is running**:
   ```bash
   docker ps | grep postgres
   ```

2. **Check PostgreSQL logs**:
   ```bash
   npm run db:logs
   ```

3. **Test connection**:
   ```bash
   npm run db:migrate:test
   ```

### Migration Issues

1. **Check migration status**:
   ```bash
   npm run db:migrate:status
   ```

2. **Reset database** (⚠️ This will delete all data):
   ```bash
   docker-compose down -v
   docker-compose up -d postgres
   npm run db:migrate
   ```

### Port Conflicts

If port 5432 is already in use:

1. Change `POSTGRES_PORT` in `.env` file
2. Update the port mapping in `docker-compose.yml`
3. Update `DATABASE_URL` accordingly

### Performance Issues

1. **Check connection pool settings** in `backend/src/config/database.ts`
2. **Monitor database performance**:
   ```sql
   SELECT * FROM pg_stat_activity;
   ```

## Development Workflow

### Daily Development

1. Start the database: `npm run db:up`
2. Run any new migrations: `npm run db:migrate`
3. Start the application: `npm run dev`

### Adding New Migrations

1. Create a new SQL file in `database/migrations/`
2. Name it with a timestamp prefix: `004_new_feature.sql`
3. Run the migration: `npm run db:migrate`

### Backup and Restore

```bash
# Backup
docker exec mica-postgres pg_dump -U mica_user mica_dev > backup.sql

# Restore
docker exec -i mica-postgres psql -U mica_user mica_dev < backup.sql
```

## Production Considerations

For production deployment:

1. Use a managed PostgreSQL service (AWS RDS, Google Cloud SQL, etc.)
2. Update `DATABASE_URL` to point to the production database
3. Ensure proper security groups and firewall rules
4. Set up automated backups
5. Monitor database performance and connections

## Support

If you encounter issues:

1. Check this documentation first
2. Review the application logs
3. Check PostgreSQL logs: `npm run db:logs`
4. Test database connectivity: `npm run db:migrate:test`
5. Create an issue in the project repository with detailed error information
