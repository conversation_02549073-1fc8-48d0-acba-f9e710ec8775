To build single-page chat interface for two simulated bot agents talking to each other.
- One agent act as simulated therapist.
- The other agent act as simulated patient.
- Both agents use gpt-5-nano model from OpenAI.
- User interface is built with Svelte.
- Backend uses Supabase.
- Deploy locally with docker-compose.
- User interface:
  - 3 panes
  - Left pane is displaying thinking process and decision of simulated therapist.
  - Middle pane is displaying conversation between simulated therapist and patient.
  - Right pane is displaying thinking process and decision of simulated patient.
  - After each patient's utterance, the simulated therapist will respond with a message. 
  - Each patient's utterance is labelled with Sentiment, Motivation Level and Engagement Level by the simulated therapist.
  - Clean and minimal design
  - At the top, there is a configuration panel e.g., maximum number of turns, etc., button to start the conversation, and button to clear the conversation.
- Tech stack:
  - Svelte
  - Supabase
  - Docker
  - OpenAI
  - Node.js
  - TypeScript
  - Poetry
  - Tailwind