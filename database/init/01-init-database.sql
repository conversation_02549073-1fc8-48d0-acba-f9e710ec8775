-- PostgreSQL Database Initialization Script for MiCA
-- This script runs automatically when the PostgreSQL container starts for the first time

-- Create the main database if it doesn't exist (handled by POSTGRES_DB env var)
-- Create additional databases if needed
-- CREATE DATABASE mica_test;

-- Create extensions that might be needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create a schema migrations table to track applied migrations
CREATE TABLE IF NOT EXISTS schema_migrations (
  id SERIAL PRIMARY KEY,
  filename VARCHAR(255) NOT NULL UNIQUE,
  applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  checksum VARCHAR(64)
);

-- Insert a comment to indicate initialization
COMMENT ON TABLE schema_migrations IS 'Tracks database migrations applied to this PostgreSQL instance';

-- Log successful initialization
DO $$
BEGIN
  RAISE NOTICE 'MiCA PostgreSQL database initialized successfully';
END $$;
