-- Enhanced Conversation Storage System
-- Migration for comprehensive conversation data storage with AI analysis

-- Add session metadata to conversations table
ALTER TABLE conversations ADD COLUMN IF NOT EXISTS session_metadata JSONB DEFAULT '{}';
ALTER TABLE conversations ADD COLUMN IF NOT EXISTS patient_persona_id VARCHAR(100);
ALTER TABLE conversations ADD COLUMN IF NOT EXISTS therapist_mode VARCHAR(20) CHECK (therapist_mode IN ('single', 'multi-therapist'));
ALTER TABLE conversations ADD COLUMN IF NOT EXISTS session_type VARCHAR(50) DEFAULT 'therapy';

-- Enhance messages table with additional metadata
ALTER TABLE messages ADD COLUMN IF NOT EXISTS message_type VARCHAR(20) DEFAULT 'standard' CHECK (message_type IN ('standard', 'greeting', 'closing', 'intervention'));
ALTER TABLE messages ADD COLUMN IF NOT EXISTS thinking TEXT;
ALTER TABLE messages ADD COLUMN IF NOT EXISTS processing_time INTEGER DEFAULT 0;
ALTER TABLE messages ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1);

-- Create AI analysis storage table
CREATE TABLE IF NOT EXISTS ai_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    analysis_type VARCHAR(30) NOT NULL CHECK (analysis_type IN ('patient_analysis', 'therapeutic_approach', 'mode_specific')),
    analysis_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create therapeutic approaches tracking table
CREATE TABLE IF NOT EXISTS therapeutic_approaches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    approach_id VARCHAR(100) NOT NULL,
    approach_name VARCHAR(200) NOT NULL,
    technique_id VARCHAR(100),
    technique_name VARCHAR(200),
    rationale TEXT,
    effectiveness_prediction DECIMAL(3,2) CHECK (effectiveness_prediction >= 0 AND effectiveness_prediction <= 1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create session analytics table for aggregated insights
CREATE TABLE IF NOT EXISTS session_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    total_messages INTEGER NOT NULL DEFAULT 0,
    patient_messages INTEGER NOT NULL DEFAULT 0,
    therapist_messages INTEGER NOT NULL DEFAULT 0,
    session_duration INTEGER, -- in seconds
    avg_response_time DECIMAL(8,2), -- in milliseconds
    sentiment_progression JSONB, -- array of sentiment scores over time
    engagement_progression JSONB, -- array of engagement scores over time
    motivation_progression JSONB, -- array of motivation scores over time
    therapeutic_techniques_used JSONB, -- array of technique IDs used
    readiness_score_progression JSONB, -- array of readiness scores over time
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create multi-therapist session data table
CREATE TABLE IF NOT EXISTS multi_therapist_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    persona_type VARCHAR(30) NOT NULL CHECK (persona_type IN ('cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive')),
    persona_name VARCHAR(100) NOT NULL,
    strategy_state JSONB,
    performance_metrics JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_ai_analysis_message_id ON ai_analysis(message_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_conversation_id ON ai_analysis(conversation_id);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_type ON ai_analysis(analysis_type);
CREATE INDEX IF NOT EXISTS idx_ai_analysis_created_at ON ai_analysis(created_at);

CREATE INDEX IF NOT EXISTS idx_therapeutic_approaches_message_id ON therapeutic_approaches(message_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_approaches_conversation_id ON therapeutic_approaches(conversation_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_approaches_approach_id ON therapeutic_approaches(approach_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_approaches_technique_id ON therapeutic_approaches(technique_id);

CREATE INDEX IF NOT EXISTS idx_session_analytics_conversation_id ON session_analytics(conversation_id);
CREATE INDEX IF NOT EXISTS idx_session_analytics_created_at ON session_analytics(created_at);

CREATE INDEX IF NOT EXISTS idx_multi_therapist_sessions_conversation_id ON multi_therapist_sessions(conversation_id);
CREATE INDEX IF NOT EXISTS idx_multi_therapist_sessions_persona_type ON multi_therapist_sessions(persona_type);

-- Enhanced indexes for existing tables
CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender);
CREATE INDEX IF NOT EXISTS idx_messages_message_type ON messages(message_type);
CREATE INDEX IF NOT EXISTS idx_conversations_patient_persona_id ON conversations(patient_persona_id);
CREATE INDEX IF NOT EXISTS idx_conversations_therapist_mode ON conversations(therapist_mode);
CREATE INDEX IF NOT EXISTS idx_conversations_session_type ON conversations(session_type);

-- Add updated_at triggers for new tables
CREATE TRIGGER update_session_analytics_updated_at 
    BEFORE UPDATE ON session_analytics 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_multi_therapist_sessions_updated_at 
    BEFORE UPDATE ON multi_therapist_sessions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies for new tables
ALTER TABLE ai_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE therapeutic_approaches ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE multi_therapist_sessions ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized based on authentication needs)
CREATE POLICY "Allow all operations for now" ON ai_analysis FOR ALL USING (true);
CREATE POLICY "Allow all operations for now" ON therapeutic_approaches FOR ALL USING (true);
CREATE POLICY "Allow all operations for now" ON session_analytics FOR ALL USING (true);
CREATE POLICY "Allow all operations for now" ON multi_therapist_sessions FOR ALL USING (true);

-- Comments for documentation
COMMENT ON TABLE ai_analysis IS 'Stores AI-generated analysis data for patient responses and therapeutic insights';
COMMENT ON COLUMN ai_analysis.analysis_type IS 'Type of analysis: patient_analysis, therapeutic_approach, or mode_specific';
COMMENT ON COLUMN ai_analysis.analysis_data IS 'Complete analysis result as JSON including sentiment, motivation, engagement, etc.';

COMMENT ON TABLE therapeutic_approaches IS 'Tracks therapeutic approaches and techniques used in each therapist response';
COMMENT ON COLUMN therapeutic_approaches.approach_id IS 'Identifier for the therapeutic approach (e.g., cognitive-behavioral-therapy)';
COMMENT ON COLUMN therapeutic_approaches.technique_id IS 'Identifier for the specific technique used within the approach';
COMMENT ON COLUMN therapeutic_approaches.effectiveness_prediction IS 'AI prediction of technique effectiveness (0-1 scale)';

COMMENT ON TABLE session_analytics IS 'Aggregated analytics and insights for completed therapy sessions';
COMMENT ON COLUMN session_analytics.sentiment_progression IS 'Array of sentiment analysis results over the course of the session';
COMMENT ON COLUMN session_analytics.readiness_score_progression IS 'Array of readiness scores showing patient progress';

COMMENT ON TABLE multi_therapist_sessions IS 'Stores data specific to multi-therapist comparative study sessions';
COMMENT ON COLUMN multi_therapist_sessions.persona_type IS 'Type of therapist persona used in the session';
COMMENT ON COLUMN multi_therapist_sessions.strategy_state IS 'Current state of the therapeutic strategy for this persona';
