-- CBT Evaluation System Database Schema
-- Migration for storing CBT effectiveness evaluation results

-- CBT Evaluations table
CREATE TABLE cbt_evaluations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    evaluation_data JSONB NOT NULL,
    overall_score DECIMAL(3,1) NOT NULL CHECK (overall_score >= 0 AND overall_score <= 6),
    overall_assessment VARCHAR(20) NOT NULL CHECK (overall_assessment IN ('poor', 'fair', 'good', 'excellent')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Evaluation dimensions table for structured querying
CREATE TABLE evaluation_dimensions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    evaluation_id UUID NOT NULL REFERENCES cbt_evaluations(id) ON DELETE CASCADE,
    dimension_name VARCHAR(50) NOT NULL CHECK (dimension_name IN ('cbtValidity', 'cbtAppropriateness', 'cbtAccuracy', 'esAppropriateness', 'stability')),
    score INTEGER NOT NULL CHECK (score IN (0, 2, 4, 6)),
    criteria TEXT NOT NULL,
    rationale TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_cbt_evaluations_conversation_id ON cbt_evaluations(conversation_id);
CREATE INDEX idx_cbt_evaluations_overall_score ON cbt_evaluations(overall_score);
CREATE INDEX idx_cbt_evaluations_overall_assessment ON cbt_evaluations(overall_assessment);
CREATE INDEX idx_cbt_evaluations_created_at ON cbt_evaluations(created_at);
CREATE INDEX idx_evaluation_dimensions_evaluation_id ON evaluation_dimensions(evaluation_id);
CREATE INDEX idx_evaluation_dimensions_dimension_name ON evaluation_dimensions(dimension_name);
CREATE INDEX idx_evaluation_dimensions_score ON evaluation_dimensions(score);

-- Unique constraint to ensure one evaluation per conversation
CREATE UNIQUE INDEX idx_unique_evaluation_per_conversation ON cbt_evaluations(conversation_id);

-- Apply updated_at trigger to cbt_evaluations
CREATE TRIGGER update_cbt_evaluations_updated_at 
    BEFORE UPDATE ON cbt_evaluations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE cbt_evaluations ENABLE ROW LEVEL SECURITY;
ALTER TABLE evaluation_dimensions ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized based on authentication needs)
CREATE POLICY "Allow all operations for now" ON cbt_evaluations FOR ALL USING (true);
CREATE POLICY "Allow all operations for now" ON evaluation_dimensions FOR ALL USING (true);

-- Comments for documentation
COMMENT ON TABLE cbt_evaluations IS 'Stores CBT effectiveness evaluation results for completed therapy sessions';
COMMENT ON COLUMN cbt_evaluations.evaluation_data IS 'Complete evaluation result as JSON including all dimensions, scores, and metadata';
COMMENT ON COLUMN cbt_evaluations.overall_score IS 'Average score across all evaluation dimensions (0-6 scale)';
COMMENT ON COLUMN cbt_evaluations.overall_assessment IS 'Qualitative assessment based on overall score';

COMMENT ON TABLE evaluation_dimensions IS 'Normalized storage of individual evaluation dimension scores for efficient querying';
COMMENT ON COLUMN evaluation_dimensions.dimension_name IS 'Name of the evaluation dimension (cbtValidity, cbtAppropriateness, etc.)';
COMMENT ON COLUMN evaluation_dimensions.score IS 'Score for this dimension on 0-6 scale (0, 2, 4, 6)';
COMMENT ON COLUMN evaluation_dimensions.criteria IS 'Scoring criteria description for this dimension';
COMMENT ON COLUMN evaluation_dimensions.rationale IS 'AI-generated rationale for the assigned score';
