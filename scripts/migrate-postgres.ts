#!/usr/bin/env ts-node

// PostgreSQL Migration Helper for MiCA Therapy Application
// Runs SQL migrations against local PostgreSQL database

import { Client } from 'pg';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import * as crypto from 'crypto';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

const databaseUrl = process.env['DATABASE_URL'];

if (!databaseUrl) {
  console.error('❌ Missing required environment variable: DATABASE_URL');
  console.error('   Example: DATABASE_URL=postgresql://mica_user:mica_password@localhost:5432/mica_dev');
  process.exit(1);
}

interface MigrationFile {
  filename: string;
  path: string;
  content: string;
  checksum: string;
}

class PostgreSQLMigrator {
  private client: Client;
  private migrationsPath: string;

  constructor() {
    this.client = new Client({ connectionString: databaseUrl });
    this.migrationsPath = path.resolve(__dirname, '../database/migrations');
  }

  /**
   * Connect to PostgreSQL database
   */
  async connect(): Promise<void> {
    try {
      await this.client.connect();
      console.log('✅ Connected to PostgreSQL database');
    } catch (error) {
      console.error('❌ Failed to connect to PostgreSQL:', error);
      throw error;
    }
  }

  /**
   * Disconnect from PostgreSQL database
   */
  async disconnect(): Promise<void> {
    try {
      await this.client.end();
      console.log('✅ Disconnected from PostgreSQL database');
    } catch (error) {
      console.error('❌ Error disconnecting from PostgreSQL:', error);
    }
  }

  /**
   * Get all migration files in order
   */
  private getMigrationFiles(): MigrationFile[] {
    try {
      const files = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort(); // Ensure migrations run in order

      return files.map(filename => {
        const filePath = path.join(this.migrationsPath, filename);
        const content = fs.readFileSync(filePath, 'utf8');
        const checksum = crypto.createHash('md5').update(content).digest('hex');
        
        return {
          filename,
          path: filePath,
          content,
          checksum
        };
      });
    } catch (error) {
      console.error('❌ Error reading migration files:', error);
      throw error;
    }
  }

  /**
   * Ensure migrations table exists
   */
  private async ensureMigrationsTable(): Promise<void> {
    try {
      const createTableQuery = `
        CREATE TABLE IF NOT EXISTS schema_migrations (
          id SERIAL PRIMARY KEY,
          filename VARCHAR(255) NOT NULL UNIQUE,
          applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          checksum VARCHAR(64)
        );
      `;
      
      await this.client.query(createTableQuery);
      console.log('✅ Schema migrations table ready');
    } catch (error) {
      console.error('❌ Error creating migrations table:', error);
      throw error;
    }
  }

  /**
   * Get list of applied migrations
   */
  private async getAppliedMigrations(): Promise<Set<string>> {
    try {
      const result = await this.client.query(
        'SELECT filename FROM schema_migrations ORDER BY applied_at'
      );
      
      return new Set(result.rows.map(row => row.filename));
    } catch (error) {
      console.error('❌ Error fetching applied migrations:', error);
      throw error;
    }
  }

  /**
   * Apply a single migration
   */
  private async applyMigration(migration: MigrationFile): Promise<void> {
    try {
      console.log(`📝 Applying migration: ${migration.filename}`);
      
      // Start transaction
      await this.client.query('BEGIN');
      
      // Execute migration SQL
      await this.client.query(migration.content);
      
      // Record migration as applied
      await this.client.query(
        'INSERT INTO schema_migrations (filename, checksum) VALUES ($1, $2)',
        [migration.filename, migration.checksum]
      );
      
      // Commit transaction
      await this.client.query('COMMIT');
      
      console.log(`✅ Applied migration: ${migration.filename}`);
    } catch (error) {
      // Rollback transaction on error
      await this.client.query('ROLLBACK');
      console.error(`❌ Error applying migration ${migration.filename}:`, error);
      throw error;
    }
  }

  /**
   * Run all pending migrations
   */
  async migrate(): Promise<void> {
    try {
      await this.ensureMigrationsTable();
      
      const migrationFiles = this.getMigrationFiles();
      const appliedMigrations = await this.getAppliedMigrations();
      
      const pendingMigrations = migrationFiles.filter(
        migration => !appliedMigrations.has(migration.filename)
      );
      
      if (pendingMigrations.length === 0) {
        console.log('✅ No pending migrations to apply');
        return;
      }
      
      console.log(`📋 Found ${pendingMigrations.length} pending migration(s)`);
      
      for (const migration of pendingMigrations) {
        await this.applyMigration(migration);
      }
      
      console.log('🎉 All migrations applied successfully!');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Show migration status
   */
  async status(): Promise<void> {
    try {
      await this.ensureMigrationsTable();
      
      const migrationFiles = this.getMigrationFiles();
      const appliedMigrations = await this.getAppliedMigrations();
      
      console.log('📊 Migration Status');
      console.log('=' .repeat(80));
      
      for (const migration of migrationFiles) {
        const status = appliedMigrations.has(migration.filename) ? '✅ Applied' : '⏳ Pending';
        console.log(`${status} - ${migration.filename}`);
      }
      
      const pendingCount = migrationFiles.length - appliedMigrations.size;
      console.log('');
      console.log(`Total migrations: ${migrationFiles.length}`);
      console.log(`Applied: ${appliedMigrations.size}`);
      console.log(`Pending: ${pendingCount}`);
    } catch (error) {
      console.error('❌ Error checking migration status:', error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  async testConnection(): Promise<void> {
    try {
      const result = await this.client.query('SELECT NOW() as current_time, version() as pg_version');
      console.log('✅ Database connection test successful');
      console.log(`   Current time: ${result.rows[0].current_time}`);
      console.log(`   PostgreSQL version: ${result.rows[0].pg_version.split(' ')[0]} ${result.rows[0].pg_version.split(' ')[1]}`);
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const command = process.argv[2] || 'migrate';
  const migrator = new PostgreSQLMigrator();
  
  try {
    await migrator.connect();
    
    switch (command) {
      case 'migrate':
        await migrator.migrate();
        break;
      case 'status':
        await migrator.status();
        break;
      case 'test':
        await migrator.testConnection();
        break;
      case 'help':
        console.log('PostgreSQL Migration Helper for MiCA');
        console.log('');
        console.log('Commands:');
        console.log('  migrate (default) - Run all pending migrations');
        console.log('  status           - Show migration status');
        console.log('  test             - Test database connection');
        console.log('  help             - Show this help');
        break;
      default:
        console.log('Unknown command. Use "help" to see available commands.');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  } finally {
    await migrator.disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { PostgreSQLMigrator };
