#!/usr/bin/env ts-node

// Database Setup Helper for MiCA Therapy Application
// Displays SQL commands for manual execution in Supabase dashboard

import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

interface MigrationFile {
  filename: string;
  path: string;
  content: string;
}

class DatabaseSetupHelper {
  private migrationsPath: string;

  constructor() {
    this.migrationsPath = path.resolve(__dirname, '../database/migrations');
  }

  /**
   * Get all migration files in order
   */
  private getMigrationFiles(): MigrationFile[] {
    try {
      const files = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort(); // Ensure migrations run in order

      return files.map(filename => ({
        filename,
        path: path.join(this.migrationsPath, filename),
        content: fs.readFileSync(path.join(this.migrationsPath, filename), 'utf8')
      }));
    } catch (error) {
      console.error('❌ Error reading migration files:', error);
      throw error;
    }
  }

  /**
   * Display setup instructions
   */
  displaySetupInstructions(): void {
    console.log('🚀 MiCA Therapy Application - Database Setup');
    console.log('=' .repeat(80));
    console.log();
    console.log('This script will help you set up the enhanced conversation storage system');
    console.log('in your Supabase database. Please follow these steps:');
    console.log();
    console.log('1. Open your Supabase dashboard');
    console.log('2. Navigate to the SQL Editor');
    console.log('3. Execute the SQL commands shown below in order');
    console.log('4. Verify the tables are created successfully');
    console.log();
    console.log('=' .repeat(80));
    console.log();
  }

  /**
   * Display migration SQL
   */
  displayMigrations(): void {
    try {
      const migrationFiles = this.getMigrationFiles();
      
      console.log(`📁 Found ${migrationFiles.length} migration files to execute:`);
      console.log();

      migrationFiles.forEach((migration, index) => {
        console.log(`📄 Migration ${index + 1}: ${migration.filename}`);
        console.log('─'.repeat(80));
        console.log();
        console.log('Copy and paste the following SQL into your Supabase SQL Editor:');
        console.log();
        console.log('```sql');
        console.log(migration.content);
        console.log('```');
        console.log();
        console.log('─'.repeat(80));
        console.log();
      });

      console.log('✅ After executing all migrations, your database will be ready!');
      console.log();
      console.log('Next steps:');
      console.log('1. Test the conversation storage system');
      console.log('2. Verify data is being stored correctly');
      console.log('3. Check the API endpoints work as expected');
      console.log();
    } catch (error) {
      console.error('❌ Error displaying migrations:', error);
      throw error;
    }
  }

  /**
   * Display specific migration
   */
  displayMigration(filename: string): void {
    try {
      const migrationFiles = this.getMigrationFiles();
      const migration = migrationFiles.find(m => m.filename === filename);

      if (!migration) {
        console.error(`❌ Migration file not found: ${filename}`);
        console.log('Available migrations:');
        migrationFiles.forEach(m => console.log(`  - ${m.filename}`));
        return;
      }

      console.log(`📄 Migration: ${migration.filename}`);
      console.log('=' .repeat(80));
      console.log();
      console.log('```sql');
      console.log(migration.content);
      console.log('```');
      console.log();
    } catch (error) {
      console.error('❌ Error displaying migration:', error);
      throw error;
    }
  }

  /**
   * Display verification queries
   */
  displayVerificationQueries(): void {
    console.log('🔍 Database Verification Queries');
    console.log('=' .repeat(80));
    console.log();
    console.log('After running the migrations, execute these queries to verify everything is set up correctly:');
    console.log();
    
    const verificationQueries = [
      {
        name: 'Check all tables exist',
        sql: `
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'conversations', 
    'messages', 
    'ai_analysis', 
    'therapeutic_approaches', 
    'session_analytics', 
    'multi_therapist_sessions'
  )
ORDER BY table_name;`
      },
      {
        name: 'Check conversations table structure',
        sql: `
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'conversations' 
ORDER BY ordinal_position;`
      },
      {
        name: 'Check indexes',
        sql: `
SELECT indexname, tablename, indexdef 
FROM pg_indexes 
WHERE tablename IN (
  'conversations', 
  'messages', 
  'ai_analysis', 
  'therapeutic_approaches', 
  'session_analytics', 
  'multi_therapist_sessions'
)
ORDER BY tablename, indexname;`
      },
      {
        name: 'Test basic functionality',
        sql: `
-- Insert a test conversation
INSERT INTO conversations (id, status, config) 
VALUES ('test-conversation-id', 'active', '{"test": true}');

-- Insert a test message
INSERT INTO messages (id, conversation_id, sender, content) 
VALUES ('test-message-id', 'test-conversation-id', 'patient', 'Hello, this is a test message');

-- Verify the data
SELECT c.id as conversation_id, c.status, m.id as message_id, m.content 
FROM conversations c 
JOIN messages m ON c.id = m.conversation_id 
WHERE c.id = 'test-conversation-id';

-- Clean up test data
DELETE FROM messages WHERE conversation_id = 'test-conversation-id';
DELETE FROM conversations WHERE id = 'test-conversation-id';`
      }
    ];

    verificationQueries.forEach((query, index) => {
      console.log(`${index + 1}. ${query.name}:`);
      console.log('```sql');
      console.log(query.sql.trim());
      console.log('```');
      console.log();
    });
  }

  /**
   * Display RLS setup instructions
   */
  displayRLSInstructions(): void {
    console.log('🔒 Row Level Security (RLS) Setup');
    console.log('=' .repeat(80));
    console.log();
    console.log('The migration includes basic RLS policies. For production, you may want to customize these:');
    console.log();
    
    const rlsQueries = [
      {
        name: 'View current RLS policies',
        sql: `
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';`
      },
      {
        name: 'Example: Restrict access by user ID (customize as needed)',
        sql: `
-- Example policy to restrict conversations to authenticated users
-- Modify this based on your authentication setup
DROP POLICY IF EXISTS "Users can access their own conversations" ON conversations;
CREATE POLICY "Users can access their own conversations" ON conversations
  FOR ALL USING (auth.uid()::text = (session_metadata->>'userId'));`
      }
    ];

    rlsQueries.forEach((query, index) => {
      console.log(`${index + 1}. ${query.name}:`);
      console.log('```sql');
      console.log(query.sql.trim());
      console.log('```');
      console.log();
    });
  }
}

// Main execution
async function main() {
  const setupHelper = new DatabaseSetupHelper();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'show':
      case undefined:
        setupHelper.displaySetupInstructions();
        setupHelper.displayMigrations();
        break;
      case 'migration':
        const filename = process.argv[3];
        if (!filename) {
          console.error('❌ Please specify a migration filename');
          console.log('Usage: npm run db:setup migration <filename>');
          process.exit(1);
        }
        setupHelper.displayMigration(filename);
        break;
      case 'verify':
        setupHelper.displayVerificationQueries();
        break;
      case 'rls':
        setupHelper.displayRLSInstructions();
        break;
      case 'help':
        console.log('MiCA Database Setup Helper');
        console.log('');
        console.log('Commands:');
        console.log('  show (default)     - Show all migration SQL');
        console.log('  migration <file>   - Show specific migration');
        console.log('  verify            - Show verification queries');
        console.log('  rls               - Show RLS setup instructions');
        console.log('  help              - Show this help');
        break;
      default:
        console.log('Unknown command. Use "help" to see available commands.');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Setup helper failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { DatabaseSetupHelper };
