#!/bin/bash

# MiCA Project Setup Script
# This script sets up the development environment for the MiCA therapy simulation project

set -e

echo "🚀 Setting up MiCA - AI Therapy Simulation"
echo "=========================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "⚠️  Docker is not installed. Docker is recommended for easy setup."
    echo "   You can still run the project locally without Docker."
else
    echo "✅ Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1) detected"
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "⚠️  Docker Compose is not installed."
else
    echo "✅ Docker Compose detected"
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please edit it with your API keys and configuration."
else
    echo "✅ .env file already exists"
fi

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
npm install
cd ..

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit the .env file with your API keys:"
echo "   - OPENAI_API_KEY: Your OpenAI API key"
echo "   - SUPABASE_URL: Your Supabase project URL"
echo "   - SUPABASE_ANON_KEY: Your Supabase anonymous key"
echo "   - SUPABASE_SERVICE_KEY: Your Supabase service key"
echo ""
echo "2. Set up your Supabase database:"
echo "   - Run the SQL migration in database/migrations/001_initial_schema.sql"
echo ""
echo "3. Start the development servers:"
echo "   - With Docker: npm run docker:up"
echo "   - Without Docker: npm run dev"
echo ""
echo "4. Access the application:"
echo "   - Frontend: http://localhost:5173"
echo "   - Backend API: http://localhost:3000"
echo ""
echo "For more information, see README.md and IMPLEMENTATION_PLAN.md"
