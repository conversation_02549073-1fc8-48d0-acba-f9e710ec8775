{"name": "mica-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"build": "vite build", "dev": "vite dev", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .js,.ts,.svelte", "lint:fix": "eslint . --ext .js,.ts,.svelte --fix", "format": "prettier --write \"src/**/*.{js,ts,svelte}\"", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^2.1.0", "@sveltejs/kit": "^1.20.4", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.16", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-svelte": "^2.33.2", "postcss": "^8.4.31", "prettier": "^3.0.3", "prettier-plugin-svelte": "^3.0.3", "svelte": "^4.2.7", "svelte-check": "^3.5.2", "tailwindcss": "^3.3.3", "tslib": "^2.6.2", "typescript": "^5.2.2", "vite": "^4.4.9", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/svelte": "^4.0.3", "@testing-library/jest-dom": "^6.1.3"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "lucide-svelte": "^0.284.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "ws": "^8.14.2"}}