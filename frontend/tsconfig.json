{"extends": "./.svelte-kit/tsconfig.json", "compilerOptions": {"ignoreDeprecations": "5.0", "allowJs": true, "checkJs": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "moduleResolution": "bundler", "verbatimModuleSyntax": true, "baseUrl": ".", "paths": {"$lib": ["./src/lib"], "$lib/*": ["./src/lib/*"], "$components": ["./src/components"], "$components/*": ["./src/components/*"], "$stores": ["./src/stores"], "$stores/*": ["./src/stores/*"], "$types": ["./src/types"], "$types/*": ["./src/types/*"]}}}