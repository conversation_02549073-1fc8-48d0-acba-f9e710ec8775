<script lang="ts">
  import { onMount } from 'svelte';
  import { themeStore } from '$stores/theme';
  import Sidebar from '$components/ui/Sidebar.svelte';
  import '../app.css';

  // Initialize theme system on app load
  onMount(() => {
    themeStore.init();
  });
</script>

<div class="h-screen theme-transition flex">
  <!-- Sidebar Navigation -->
  <Sidebar />

  <!-- Main Content Area -->
  <main class="flex-1 min-h-screen">
    <slot />
  </main>
</div>
