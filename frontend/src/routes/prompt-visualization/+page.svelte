<script lang="ts">
  import { onMount } from 'svelte';
  import PromptPipelineVisualization from '$components/prompt-visualization/PromptPipelineVisualization.svelte';
  import PersonaSelector from '$components/PersonaSelector.svelte';
  import { themeStore } from '$stores/theme';

  // Component state
  let selectedPersonaId: string = 'emma-anxiety';
  let selectedTherapeuticApproach: string = 'cognitive_behavioral_therapy';
  let selectedTechnique: string = 'cognitive_restructuring';
  let activeTab: 'therapist' | 'patient' | 'comparison' = 'patient';
  let loading = true;
  let error: string | null = null;

  // Data
  let promptConfig: any = null;
  let therapistPipeline: any = null;
  let patientPipeline: any = null;
  let personas: any[] = [];

  onMount(async () => {
    await loadInitialData();
  });

  async function loadInitialData() {
    try {
      loading = true;
      error = null;

      // Load prompt configuration
      const configResponse = await fetch('/api/prompt-visualization/config');
      const configResult = await configResponse.json();
      
      if (configResult.success) {
        promptConfig = configResult.data;
        personas = configResult.data.personas;
      } else {
        throw new Error(configResult.error || 'Failed to load configuration');
      }

      // Load initial pipelines
      await Promise.all([
        loadTherapistPipeline(),
        loadPatientPipeline()
      ]);

    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load data';
      console.error('Error loading initial data:', err);
    } finally {
      loading = false;
    }
  }

  async function loadTherapistPipeline() {
    try {
      const response = await fetch(`/api/prompt-visualization/therapist-pipeline?approach=${selectedTherapeuticApproach}&technique=${selectedTechnique}`);
      const result = await response.json();
      
      if (result.success) {
        therapistPipeline = result.data;
      } else {
        throw new Error(result.error || 'Failed to load therapist pipeline');
      }
    } catch (err) {
      console.error('Error loading therapist pipeline:', err);
    }
  }

  async function loadPatientPipeline() {
    try {
      const response = await fetch(`/api/prompt-visualization/patient-pipeline/${selectedPersonaId}`);
      const result = await response.json();
      
      if (result.success) {
        patientPipeline = result.data;
      } else {
        throw new Error(result.error || 'Failed to load patient pipeline');
      }
    } catch (err) {
      console.error('Error loading patient pipeline:', err);
    }
  }

  // Event handlers
  async function handlePersonaChange(personaId: string) {
    selectedPersonaId = personaId;
    await loadPatientPipeline();
  }

  async function handleTherapeuticApproachChange(approach: string) {
    selectedTherapeuticApproach = approach;
    await loadTherapistPipeline();
  }

  async function handleTechniqueChange(technique: string) {
    selectedTechnique = technique;
    await loadTherapistPipeline();
  }

  function handleTabChange(tab: 'therapist' | 'patient' | 'comparison') {
    activeTab = tab;
  }

  // Reactive statements
  $: isDarkMode = $themeStore === 'dark';
</script>

<svelte:head>
  <title>Prompt Construction Pipeline - MiCA</title>
  <meta name="description" content="Visualize the step-by-step process of generating therapeutic conversation prompts" />
</svelte:head>

<div class="min-h-screen bg-neutral-50 dark:bg-neutral-900 theme-transition">
  <!-- Header -->
  <div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-neutral-900 dark:text-neutral-100 theme-transition">
            Prompt Construction Pipeline
          </h1>
          <p class="mt-2 text-neutral-600 dark:text-neutral-400 theme-transition">
            Visualize how therapeutic conversation prompts are built step-by-step
          </p>
        </div>
        
        <!-- Quick Stats -->
        {#if promptConfig}
          <div class="flex space-x-6 text-sm">
            <div class="text-center">
              <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                {promptConfig.metadata.totalPatientPersonas}
              </div>
              <div class="text-neutral-500 dark:text-neutral-400">Personas</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                {promptConfig.metadata.totalTherapistPrompts}
              </div>
              <div class="text-neutral-500 dark:text-neutral-400">Therapist Prompts</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
                {promptConfig.metadata.totalSharedPrompts}
              </div>
              <div class="text-neutral-500 dark:text-neutral-400">Shared Prompts</div>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    {#if loading}
      <!-- Loading State -->
      <div class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-neutral-600 dark:text-neutral-400">Loading prompt configuration...</span>
      </div>
    {:else if error}
      <!-- Error State -->
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
        <div class="flex items-center">
          <svg class="w-6 h-6 text-red-600 dark:text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <h3 class="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Data</h3>
            <p class="text-red-600 dark:text-red-400 mt-1">{error}</p>
          </div>
        </div>
        <button 
          on:click={loadInitialData}
          class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 theme-transition"
        >
          Retry
        </button>
      </div>
    {:else}
      <!-- Tab Navigation -->
      <div class="mb-8">
        <nav class="flex space-x-8" aria-label="Tabs">
          <button
            on:click={() => handleTabChange('patient')}
            class="py-2 px-1 border-b-2 font-medium text-sm theme-transition {
              activeTab === 'patient'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 hover:border-neutral-300 dark:hover:border-neutral-600'
            }"
          >
            Patient Persona Pipeline
          </button>
          <button
            on:click={() => handleTabChange('therapist')}
            class="py-2 px-1 border-b-2 font-medium text-sm theme-transition {
              activeTab === 'therapist'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 hover:border-neutral-300 dark:hover:border-neutral-600'
            }"
          >
            Therapist AI Pipeline
          </button>
          <button
            on:click={() => handleTabChange('comparison')}
            class="py-2 px-1 border-b-2 font-medium text-sm theme-transition {
              activeTab === 'comparison'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 hover:border-neutral-300 dark:hover:border-neutral-600'
            }"
          >
            Side-by-Side Comparison
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      {#if activeTab === 'patient' && patientPipeline}
        <PromptPipelineVisualization
          type="patient"
          pipeline={patientPipeline}
          {personas}
          {selectedPersonaId}
          on:personaChange={(e) => handlePersonaChange(e.detail)}
        />
      {:else if activeTab === 'therapist' && therapistPipeline}
        <PromptPipelineVisualization
          type="therapist"
          pipeline={therapistPipeline}
          therapeuticApproaches={therapistPipeline.availableApproaches || []}
          availableTechniques={therapistPipeline.availableTechniques || []}
          {selectedTherapeuticApproach}
          {selectedTechnique}
          on:approachChange={(e) => handleTherapeuticApproachChange(e.detail)}
          on:techniqueChange={(e) => handleTechniqueChange(e.detail)}
        />
      {:else if activeTab === 'comparison' && patientPipeline && therapistPipeline}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
              Patient Persona Pipeline
            </h3>
            <PromptPipelineVisualization
              type="patient"
              pipeline={patientPipeline}
              {personas}
              {selectedPersonaId}
              compact={true}
              on:personaChange={(e) => handlePersonaChange(e.detail)}
            />
          </div>
          <div>
            <h3 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
              Therapist AI Pipeline
            </h3>
            <PromptPipelineVisualization
              type="therapist"
              pipeline={therapistPipeline}
              therapeuticApproaches={therapistPipeline.availableApproaches || []}
              availableTechniques={therapistPipeline.availableTechniques || []}
              {selectedTherapeuticApproach}
              {selectedTechnique}
              compact={true}
              on:approachChange={(e) => handleTherapeuticApproachChange(e.detail)}
              on:techniqueChange={(e) => handleTechniqueChange(e.detail)}
            />
          </div>
        </div>
      {/if}
    {/if}
  </div>
</div>

<style>
  /* Custom scrollbar for better UX */
  :global(.prompt-content) {
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--color-border-secondary)) transparent;
  }

  :global(.prompt-content::-webkit-scrollbar) {
    width: 6px;
  }

  :global(.prompt-content::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(.prompt-content::-webkit-scrollbar-thumb) {
    background-color: rgb(var(--color-border-secondary));
    border-radius: 3px;
  }
</style>
