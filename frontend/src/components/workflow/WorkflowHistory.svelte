<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let workflowHistory: Array<any> = [];
  export let compact: boolean = false;
  export let maxItems: number = 20;

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Component state
  let selectedWorkflowId: string | null = null;
  let filterStatus: 'all' | 'completed' | 'error' = 'all';
  let sortBy: 'timestamp' | 'duration' | 'status' = 'timestamp';

  // Reactive statements
  $: filteredHistory = filterAndSortHistory(workflowHistory, filterStatus, sortBy);
  $: displayHistory = filteredHistory.slice(0, maxItems);

  function filterAndSortHistory(history: Array<any>, filter: string, sort: string) {
    let filtered = [...history];

    // Apply filter
    if (filter === 'completed') {
      filtered = filtered.filter(w => w.workflowStep === 'complete' && !w.workflowError);
    } else if (filter === 'error') {
      filtered = filtered.filter(w => w.workflowError);
    }

    // Apply sort
    filtered.sort((a, b) => {
      switch (sort) {
        case 'timestamp':
          return new Date(b.processingMetadata?.startTime || 0).getTime() - 
                 new Date(a.processingMetadata?.startTime || 0).getTime();
        case 'duration':
          return (b.processingMetadata?.totalProcessingTime || 0) - 
                 (a.processingMetadata?.totalProcessingTime || 0);
        case 'status':
          if (a.workflowError && !b.workflowError) return -1;
          if (!a.workflowError && b.workflowError) return 1;
          return 0;
        default:
          return 0;
      }
    });

    return filtered;
  }

  function handleWorkflowSelect(workflow: any) {
    selectedWorkflowId = selectedWorkflowId === workflow.id ? null : workflow.id;
    dispatch('selectWorkflow', { workflow });
  }

  function getStatusIcon(workflow: any) {
    if (workflow.workflowError) return '❌';
    if (workflow.workflowStep === 'complete') return '✅';
    return '⏳';
  }

  function getStatusText(workflow: any) {
    if (workflow.workflowError) return 'Error';
    if (workflow.workflowStep === 'complete') return 'Completed';
    return 'In Progress';
  }

  function formatTime(ms: number): string {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  }

  function formatTimestamp(timestamp: string): string {
    if (!timestamp) return 'N/A';
    return new Date(timestamp).toLocaleString();
  }

  function getWorkflowSummary(workflow: any): string {
    const parts = [];
    if (workflow.patientAnalysis?.sentiment) {
      parts.push(`${workflow.patientAnalysis.sentiment} sentiment`);
    }
    if (workflow.therapeuticApproaches) {
      const approaches = Object.values(workflow.therapeuticApproaches).map((a: any) => a.name);
      parts.push(`${approaches.length} approaches`);
    }
    return parts.join(', ') || 'No details';
  }
</script>

<div class="workflow-history {compact ? 'compact' : ''}">
  <!-- Header with Filters -->
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
      Workflow History ({workflowHistory.length})
    </h3>
    
    <div class="flex items-center space-x-2">
      <!-- Status Filter -->
      <select
        bind:value={filterStatus}
        class="text-xs border border-neutral-300 dark:border-neutral-600 rounded px-2 py-1 bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
      >
        <option value="all">All</option>
        <option value="completed">Completed</option>
        <option value="error">Errors</option>
      </select>

      <!-- Sort By -->
      <select
        bind:value={sortBy}
        class="text-xs border border-neutral-300 dark:border-neutral-600 rounded px-2 py-1 bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
      >
        <option value="timestamp">Time</option>
        <option value="duration">Duration</option>
        <option value="status">Status</option>
      </select>
    </div>
  </div>

  <!-- History List -->
  {#if displayHistory.length > 0}
    <div class="space-y-2 max-h-96 overflow-y-auto">
      {#each displayHistory as workflow, index}
        <div
          class="history-item {selectedWorkflowId === workflow.id ? 'selected' : ''}"
          on:click={() => handleWorkflowSelect(workflow)}
          role="button"
          tabindex="0"
        >
          <!-- Header -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-sm">{getStatusIcon(workflow)}</span>
              <span class="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                Workflow #{index + 1}
              </span>
              <span class="text-xs text-neutral-500 dark:text-neutral-400">
                {getStatusText(workflow)}
              </span>
            </div>
            
            <div class="text-xs text-neutral-500 dark:text-neutral-400">
              {formatTime(workflow.processingMetadata?.totalProcessingTime)}
            </div>
          </div>

          <!-- Summary -->
          <div class="mt-1 text-xs text-neutral-600 dark:text-neutral-400">
            {getWorkflowSummary(workflow)}
          </div>

          <!-- Timestamp -->
          <div class="mt-1 text-xs text-neutral-500 dark:text-neutral-500">
            {formatTimestamp(workflow.processingMetadata?.startTime)}
          </div>

          <!-- Error Details -->
          {#if workflow.workflowError}
            <div class="mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded text-xs">
              <div class="font-medium text-red-700 dark:text-red-300">
                Error in {workflow.workflowError.step}
              </div>
              <div class="text-red-600 dark:text-red-400 mt-1">
                {workflow.workflowError.error}
              </div>
            </div>
          {/if}

          <!-- Expanded Details -->
          {#if selectedWorkflowId === workflow.id}
            <div class="mt-3 pt-3 border-t border-neutral-200 dark:border-neutral-700">
              <!-- Step Timings -->
              {#if workflow.processingMetadata?.stepTimings}
                <div class="mb-3">
                  <h5 class="text-xs font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Step Timings
                  </h5>
                  <div class="space-y-1">
                    {#each Object.entries(workflow.processingMetadata.stepTimings) as [step, time]}
                      <div class="flex justify-between text-xs">
                        <span class="text-neutral-600 dark:text-neutral-400 capitalize">
                          {step.replace('_', ' ')}
                        </span>
                        <span class="text-neutral-900 dark:text-neutral-100">
                          {formatTime(time)}
                        </span>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              <!-- Patient Analysis -->
              {#if workflow.patientAnalysis}
                <div class="mb-3">
                  <h5 class="text-xs font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Patient Analysis
                  </h5>
                  <div class="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span class="text-neutral-600 dark:text-neutral-400">Sentiment:</span>
                      <span class="text-neutral-900 dark:text-neutral-100 ml-1">
                        {workflow.patientAnalysis.sentiment}
                      </span>
                    </div>
                    <div>
                      <span class="text-neutral-600 dark:text-neutral-400">Readiness:</span>
                      <span class="text-neutral-900 dark:text-neutral-100 ml-1">
                        {workflow.patientAnalysis.readinessScore?.score || 'N/A'}/10
                      </span>
                    </div>
                    <div>
                      <span class="text-neutral-600 dark:text-neutral-400">Motivation:</span>
                      <span class="text-neutral-900 dark:text-neutral-100 ml-1">
                        {workflow.patientAnalysis.motivationLevel}
                      </span>
                    </div>
                    <div>
                      <span class="text-neutral-600 dark:text-neutral-400">Engagement:</span>
                      <span class="text-neutral-900 dark:text-neutral-100 ml-1">
                        {workflow.patientAnalysis.engagementLevel}
                      </span>
                    </div>
                  </div>
                </div>
              {/if}

              <!-- Therapeutic Approaches -->
              {#if workflow.therapeuticApproaches}
                <div>
                  <h5 class="text-xs font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                    Therapeutic Approaches
                  </h5>
                  <div class="space-y-1">
                    {#each Object.entries(workflow.therapeuticApproaches) as [persona, approach]}
                      <div class="text-xs">
                        <span class="text-neutral-600 dark:text-neutral-400 capitalize">
                          {persona.replace(/([A-Z])/g, ' $1').trim()}:
                        </span>
                        <span class="text-neutral-900 dark:text-neutral-100 ml-1">
                          {approach.name}
                        </span>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {/each}
    </div>

    <!-- Show More Button -->
    {#if filteredHistory.length > maxItems}
      <div class="mt-4 text-center">
        <button
          class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
          on:click={() => maxItems += 10}
        >
          Show More ({filteredHistory.length - maxItems} remaining)
        </button>
      </div>
    {/if}
  {:else}
    <!-- Empty State -->
    <div class="text-center py-8 text-neutral-500 dark:text-neutral-400">
      <div class="text-4xl mb-2">📜</div>
      <p>No workflow history available</p>
      <p class="text-sm">
        {#if filterStatus !== 'all'}
          Try changing the filter or start a new conversation
        {:else}
          Start a conversation to see workflow history
        {/if}
      </p>
    </div>
  {/if}
</div>

<style>
  .workflow-history.compact {
    @apply text-sm;
  }

  .history-item {
    @apply p-3 border border-neutral-200 dark:border-neutral-700 rounded-lg cursor-pointer transition-all duration-200 hover:bg-neutral-50 dark:hover:bg-neutral-800;
  }

  .history-item.selected {
    @apply bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600;
  }

  .history-item:hover {
    @apply shadow-sm;
  }
</style>
