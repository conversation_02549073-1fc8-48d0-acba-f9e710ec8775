<script lang="ts">
  // Props
  export let edge: { from: string; to: string };
  export let nodes: Array<{ id: string; position: { x: number; y: number } }>;
  export let isActive: boolean = false;

  // Find node positions
  $: fromNode = nodes.find(n => n.id === edge.from);
  $: toNode = nodes.find(n => n.id === edge.to);
  
  // Calculate edge path
  $: edgePath = calculateEdgePath(fromNode, toNode);
  $: edgeStyle = getEdgeStyle(isActive);

  function calculateEdgePath(from: any, to: any) {
    if (!from || !to) return '';
    
    const startX = from.position.x + 60; // Node width/2
    const startY = from.position.y;
    const endX = to.position.x - 60; // Node width/2
    const endY = to.position.y;
    
    // Create a smooth curve
    const midX = (startX + endX) / 2;
    const controlPoint1X = startX + (midX - startX) * 0.5;
    const controlPoint2X = endX - (endX - midX) * 0.5;
    
    return `M ${startX} ${startY} C ${controlPoint1X} ${startY} ${controlPoint2X} ${endY} ${endX} ${endY}`;
  }

  function getEdgeStyle(active: boolean) {
    return {
      stroke: active ? '#3B82F6' : '#D1D5DB',
      strokeWidth: active ? '2' : '1.5',
      opacity: active ? '1' : '0.6'
    };
  }
</script>

<!-- Edge Path -->
<path
  d={edgePath}
  fill="none"
  stroke={edgeStyle.stroke}
  stroke-width={edgeStyle.strokeWidth}
  opacity={edgeStyle.opacity}
  class="transition-all duration-300"
  marker-end="url(#arrowhead)"
/>

<!-- Arrow Marker -->
<defs>
  <marker
    id="arrowhead"
    markerWidth="10"
    markerHeight="7"
    refX="9"
    refY="3.5"
    orient="auto"
  >
    <polygon
      points="0 0, 10 3.5, 0 7"
      fill={edgeStyle.stroke}
      opacity={edgeStyle.opacity}
      class="transition-all duration-300"
    />
  </marker>
</defs>

<!-- Animated Flow Indicator for Active Edges -->
{#if isActive}
  <circle r="3" fill="#3B82F6" opacity="0.8">
    <animateMotion dur="2s" repeatCount="indefinite" path={edgePath} />
  </circle>
{/if}
