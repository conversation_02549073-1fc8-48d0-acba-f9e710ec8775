<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let node: {
    id: string;
    name: string;
    type: string;
    position: { x: number; y: number };
    description: string;
  };
  export let status: 'pending' | 'active' | 'completed' | 'error' = 'pending';
  export let isSelected: boolean = false;
  export let isHovered: boolean = false;
  export let therapeuticApproaches: any = {};

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Reactive statements
  $: nodeStyle = getNodeStyle(status, isSelected, isHovered);
  $: nodeIcon = getNodeIcon(node.type, status);
  $: nodeColor = getNodeColor(node.type, status);

  function getNodeStyle(status: string, selected: boolean, hovered: boolean) {
    let baseClasses = 'transition-all duration-300 cursor-pointer';
    
    if (selected) {
      baseClasses += ' ring-2 ring-blue-500 ring-offset-2';
    }
    
    if (hovered) {
      baseClasses += ' transform scale-105';
    }

    return baseClasses;
  }

  function getNodeIcon(type: string, status: string) {
    if (status === 'error') return '❌';
    if (status === 'completed') return '✅';
    if (status === 'active') return '⚡';

    switch (type) {
      case 'analysis': return '🔍';
      case 'decision': return '🎯';
      case 'generation': return '💬';
      case 'synthesis': return '⚡';
      default: return '⭕';
    }
  }

  function getNodeColor(type: string, status: string) {
    if (status === 'error') return { bg: '#FEE2E2', border: '#F87171', text: '#DC2626' };
    if (status === 'completed') return { bg: '#D1FAE5', border: '#34D399', text: '#059669' };
    if (status === 'active') return { bg: '#DBEAFE', border: '#3B82F6', text: '#1D4ED8' };
    
    // Default pending colors based on type
    switch (type) {
      case 'analysis': return { bg: '#F3F4F6', border: '#9CA3AF', text: '#4B5563' };
      case 'decision': return { bg: '#FEF3C7', border: '#F59E0B', text: '#D97706' };
      case 'generation': return { bg: '#E0E7FF', border: '#8B5CF6', text: '#7C3AED' };
      case 'synthesis': return { bg: '#ECFDF5', border: '#10B981', text: '#047857' };
      default: return { bg: '#F3F4F6', border: '#9CA3AF', text: '#4B5563' };
    }
  }

  function handleClick() {
    dispatch('click', { nodeId: node.id, node });
  }

  function handleMouseEnter() {
    dispatch('mouseenter', { nodeId: node.id, node });
  }

  function handleMouseLeave() {
    dispatch('mouseleave', { nodeId: node.id, node });
  }
</script>

<!-- Node Container -->
<g
  class={nodeStyle}
  transform="translate({node.position.x - 60}, {node.position.y - 30})"
  on:click={handleClick}
  on:mouseenter={handleMouseEnter}
  on:mouseleave={handleMouseLeave}
  role="button"
  tabindex="0"
>
  <!-- Node Background -->
  <rect
    width="120"
    height="60"
    rx="8"
    fill={nodeColor.bg}
    stroke={nodeColor.border}
    stroke-width="2"
    class="transition-all duration-300"
  />

  <!-- Status Indicator -->
  <circle
    cx="15"
    cy="15"
    r="6"
    fill={nodeColor.border}
    class="transition-all duration-300"
  />

  <!-- Node Icon -->
  <text
    x="15"
    y="20"
    text-anchor="middle"
    class="text-xs"
    fill="white"
  >
    {nodeIcon}
  </text>

  <!-- Node Title -->
  <text
    x="60"
    y="25"
    text-anchor="middle"
    class="text-sm font-medium"
    fill={nodeColor.text}
  >
    {node.name}
  </text>

  <!-- Node Type -->
  <text
    x="60"
    y="40"
    text-anchor="middle"
    class="text-xs"
    fill={nodeColor.text}
    opacity="0.7"
  >
    {node.type}
  </text>

  <!-- Progress Bar for Active Nodes -->
  {#if status === 'active'}
    <rect
      x="10"
      y="50"
      width="100"
      height="4"
      rx="2"
      fill="#E5E7EB"
    />
    <rect
      x="10"
      y="50"
      width="100"
      height="4"
      rx="2"
      fill="#3B82F6"
      class="animate-pulse"
    />
  {/if}

  <!-- Error Indicator -->
  {#if status === 'error'}
    <rect
      x="5"
      y="5"
      width="110"
      height="50"
      rx="6"
      fill="none"
      stroke="#EF4444"
      stroke-width="2"
      stroke-dasharray="4,4"
      class="animate-pulse"
    />
  {/if}
</g>

<!-- Node Tooltip (shown on hover) -->
{#if isHovered}
  <g transform="translate({node.position.x - 100}, {node.position.y - 80})">
    <!-- Tooltip Background -->
    <rect
      width="200"
      height="40"
      rx="4"
      fill="#1F2937"
      opacity="0.9"
    />
    
    <!-- Tooltip Text -->
    <text
      x="100"
      y="15"
      text-anchor="middle"
      class="text-xs font-medium"
      fill="white"
    >
      {node.name}
    </text>
    
    <text
      x="100"
      y="30"
      text-anchor="middle"
      class="text-xs"
      fill="#D1D5DB"
    >
      {node.description}
    </text>
  </g>
{/if}

<style>
  g {
    cursor: pointer;
  }
  
  g:hover rect {
    filter: brightness(1.05);
  }
  
  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
</style>
