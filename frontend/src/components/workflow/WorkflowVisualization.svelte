<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import WorkflowNode from './WorkflowNode.svelte';
  import WorkflowEdge from './WorkflowEdge.svelte';
  import WorkflowControls from './WorkflowControls.svelte';
  
  // Props
  export let workflowState: any = null;
  export let therapeuticApproaches: any = {};
  export let isActive: boolean = false;
  export let showControls: boolean = true;
  export let compact: boolean = false;

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Component state
  let selectedNodeId: string | null = null;
  let hoveredNodeId: string | null = null;
  let svgElement: SVGElement;
  let containerWidth = 800;
  let containerHeight = 400;

  // Workflow definition
  const workflowNodes = [
    {
      id: 'analysis',
      name: 'Patient Analysis',
      type: 'analysis',
      position: { x: 150, y: 100 },
      description: 'Analyze patient message for sentiment, motivation, and readiness'
    },
    {
      id: 'approach_selection',
      name: 'Approach Selection',
      type: 'decision',
      position: { x: 350, y: 100 },
      description: 'Select therapeutic approaches for each persona based on analysis'
    },
    {
      id: 'response_generation',
      name: 'Response Generation',
      type: 'generation',
      position: { x: 550, y: 100 },
      description: 'Generate responses from all three therapeutic personas'
    },
    {
      id: 'synthesis',
      name: 'Synthesis',
      type: 'synthesis',
      position: { x: 750, y: 100 },
      description: 'Combine responses and prepare final output'
    }
  ];

  const workflowEdges = [
    { from: 'analysis', to: 'approach_selection' },
    { from: 'approach_selection', to: 'response_generation' },
    { from: 'response_generation', to: 'synthesis' }
  ];

  // Reactive statements
  $: currentStep = workflowState?.workflowStep || 'analysis';
  $: nodeStatuses = getNodeStatuses(currentStep, workflowState);
  $: progressPercentage = getProgressPercentage(currentStep);

  function getNodeStatuses(step: string, state: any) {
    const statuses = {
      analysis: 'pending',
      approach_selection: 'pending',
      response_generation: 'pending',
      synthesis: 'pending'
    };

    if (!state) return statuses;

    // Set status based on current step and state
    switch (step) {
      case 'analysis':
        statuses.analysis = 'active';
        break;
      case 'approach_selection':
        statuses.analysis = 'completed';
        statuses.approach_selection = 'active';
        break;
      case 'response_generation':
        statuses.analysis = 'completed';
        statuses.approach_selection = 'completed';
        statuses.response_generation = 'active';
        break;
      case 'synthesis':
        statuses.analysis = 'completed';
        statuses.approach_selection = 'completed';
        statuses.response_generation = 'completed';
        statuses.synthesis = 'active';
        break;
      case 'complete':
        statuses.analysis = 'completed';
        statuses.approach_selection = 'completed';
        statuses.response_generation = 'completed';
        statuses.synthesis = 'completed';
        break;
    }

    // Handle errors
    if (state.workflowError) {
      const errorStep = state.workflowError.step;
      if (statuses[errorStep]) {
        statuses[errorStep] = 'error';
      }
    }

    return statuses;
  }

  function getProgressPercentage(step: string): number {
    const stepProgress = {
      analysis: 25,
      approach_selection: 50,
      response_generation: 75,
      synthesis: 90,
      complete: 100
    };
    return stepProgress[step] || 0;
  }

  function handleNodeClick(nodeId: string) {
    selectedNodeId = selectedNodeId === nodeId ? null : nodeId;
    dispatch('nodeClick', { nodeId, node: workflowNodes.find(n => n.id === nodeId) });
  }

  function handleNodeMouseEnter(nodeId: string) {
    hoveredNodeId = nodeId;
  }

  function handleNodeMouseLeave() {
    hoveredNodeId = null;
  }

  function handleControlAction(action: string) {
    dispatch('controlAction', { action });
  }

  onMount(() => {
    // Set up resize observer for responsive design
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        containerWidth = entry.contentRect.width;
        containerHeight = entry.contentRect.height;
      }
    });

    if (svgElement?.parentElement) {
      resizeObserver.observe(svgElement.parentElement);
    }

    return () => {
      resizeObserver.disconnect();
    };
  });
</script>

<div class="workflow-visualization {compact ? 'compact' : ''}" class:active={isActive}>
  <!-- Header -->
  <div class="flex items-center justify-between mb-4">
    <div>
      <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
        Therapeutic Workflow
      </h3>
      <p class="text-sm text-neutral-600 dark:text-neutral-400">
        Current Step: <span class="font-medium capitalize">{currentStep.replace('_', ' ')}</span>
        {#if workflowState?.processingMetadata?.totalProcessingTime}
          • Processing Time: {Math.round(workflowState.processingMetadata.totalProcessingTime)}ms
        {/if}
      </p>
    </div>
    
    <!-- Progress Bar -->
    <div class="flex items-center space-x-3">
      <div class="w-32 bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
        <div 
          class="bg-blue-500 h-2 rounded-full transition-all duration-300"
          style="width: {progressPercentage}%"
        ></div>
      </div>
      <span class="text-sm font-medium text-neutral-600 dark:text-neutral-400">
        {progressPercentage}%
      </span>
    </div>
  </div>

  <!-- Workflow Graph -->
  <div class="workflow-graph-container bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
    <svg
      bind:this={svgElement}
      width="100%"
      height={compact ? "200" : "300"}
      viewBox="0 0 900 200"
      class="workflow-svg"
    >
      <!-- Background Grid -->
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f3f4f6" stroke-width="0.5"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#grid)" opacity="0.3"/>

      <!-- Workflow Edges -->
      {#each workflowEdges as edge}
        <WorkflowEdge
          {edge}
          nodes={workflowNodes}
          isActive={nodeStatuses[edge.from] === 'completed' && nodeStatuses[edge.to] !== 'pending'}
        />
      {/each}

      <!-- Workflow Nodes -->
      {#each workflowNodes as node}
        <WorkflowNode
          {node}
          status={nodeStatuses[node.id]}
          isSelected={selectedNodeId === node.id}
          isHovered={hoveredNodeId === node.id}
          {therapeuticApproaches}
          on:click={() => handleNodeClick(node.id)}
          on:mouseenter={() => handleNodeMouseEnter(node.id)}
          on:mouseleave={handleNodeMouseLeave}
        />
      {/each}
    </svg>
  </div>

  <!-- Controls -->
  {#if showControls}
    <WorkflowControls
      {isActive}
      currentStep={currentStep}
      on:action={(e) => handleControlAction(e.detail.action)}
    />
  {/if}

  <!-- Node Details Panel -->
  {#if selectedNodeId}
    {@const selectedNode = workflowNodes.find(n => n.id === selectedNodeId)}
    {#if selectedNode}
      <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h4 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          {selectedNode.name}
        </h4>
        <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
          {selectedNode.description}
        </p>
        
        <!-- Node-specific details -->
        {#if selectedNodeId === 'analysis' && workflowState?.patientAnalysis}
          <div class="space-y-2 text-sm">
            <div><strong>Sentiment:</strong> {workflowState.patientAnalysis.sentiment}</div>
            <div><strong>Readiness Score:</strong> {workflowState.patientAnalysis.readinessScore?.score}/10</div>
            <div><strong>Motivation:</strong> {workflowState.patientAnalysis.motivationLevel}</div>
            <div><strong>Engagement:</strong> {workflowState.patientAnalysis.engagementLevel}</div>
          </div>
        {:else if selectedNodeId === 'approach_selection' && workflowState?.therapeuticApproaches}
          <div class="space-y-2 text-sm">
            {#each Object.entries(workflowState.therapeuticApproaches) as [persona, approach]}
              <div><strong>{persona}:</strong> {approach.name}</div>
            {/each}
          </div>
        {:else if selectedNodeId === 'response_generation' && workflowState?.therapistResponses}
          <div class="space-y-2 text-sm">
            {#each Object.entries(workflowState.therapistResponses) as [persona, response]}
              <div><strong>{persona}:</strong> {response.message.substring(0, 100)}...</div>
            {/each}
          </div>
        {/if}
      </div>
    {/if}
  {/if}
</div>

<style>
  .workflow-visualization {
    @apply w-full;
  }

  .workflow-visualization.compact {
    @apply text-sm;
  }

  .workflow-graph-container {
    min-height: 200px;
  }

  .workflow-svg {
    @apply w-full h-full;
  }

  .workflow-visualization.active .workflow-graph-container {
    @apply ring-2 ring-blue-500 ring-opacity-50;
  }
</style>
