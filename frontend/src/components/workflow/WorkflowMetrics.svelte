<script lang="ts">
  // Props
  export let workflowState: any = null;
  export let workflowHistory: Array<any> = [];
  export let compact: boolean = false;

  // Reactive statements
  $: currentMetrics = extractCurrentMetrics(workflowState);
  $: historicalMetrics = calculateHistoricalMetrics(workflowHistory);
  $: performanceInsights = generatePerformanceInsights(currentMetrics, historicalMetrics);

  function extractCurrentMetrics(state: any) {
    if (!state) return null;

    const stepTimings = state.processingMetadata?.stepTimings || {};
    const totalTime = state.processingMetadata?.totalProcessingTime || 0;

    return {
      totalProcessingTime: totalTime,
      stepTimings,
      currentStep: state.workflowStep,
      hasError: !!state.workflowError,
      errorStep: state.workflowError?.step,
      patientAnalysis: state.patientAnalysis,
      therapeuticApproaches: state.therapeuticApproaches,
      responsesGenerated: state.therapistResponses ? Object.keys(state.therapistResponses).length : 0
    };
  }

  function calculateHistoricalMetrics(history: Array<any>) {
    if (!history.length) return null;

    const completedWorkflows = history.filter(w => w.workflowStep === 'complete');
    const errorWorkflows = history.filter(w => w.workflowError);

    const avgProcessingTime = completedWorkflows.length > 0
      ? completedWorkflows.reduce((sum, w) => sum + (w.processingMetadata?.totalProcessingTime || 0), 0) / completedWorkflows.length
      : 0;

    const stepAverages = {};
    completedWorkflows.forEach(workflow => {
      const stepTimings = workflow.processingMetadata?.stepTimings || {};
      Object.entries(stepTimings).forEach(([step, time]) => {
        if (!stepAverages[step]) stepAverages[step] = [];
        stepAverages[step].push(time);
      });
    });

    Object.keys(stepAverages).forEach(step => {
      const times = stepAverages[step];
      stepAverages[step] = times.reduce((sum, time) => sum + time, 0) / times.length;
    });

    return {
      totalWorkflows: history.length,
      completedWorkflows: completedWorkflows.length,
      errorWorkflows: errorWorkflows.length,
      successRate: history.length > 0 ? (completedWorkflows.length / history.length) * 100 : 0,
      avgProcessingTime,
      stepAverages
    };
  }

  function generatePerformanceInsights(current: any, historical: any) {
    if (!current || !historical) return [];

    const insights = [];

    // Processing time comparison
    if (current.totalProcessingTime && historical.avgProcessingTime) {
      const diff = ((current.totalProcessingTime - historical.avgProcessingTime) / historical.avgProcessingTime) * 100;
      if (Math.abs(diff) > 20) {
        insights.push({
          type: diff > 0 ? 'warning' : 'success',
          message: `Current processing time is ${Math.abs(diff).toFixed(1)}% ${diff > 0 ? 'slower' : 'faster'} than average`
        });
      }
    }

    // Error rate
    if (historical.errorWorkflows > 0) {
      const errorRate = (historical.errorWorkflows / historical.totalWorkflows) * 100;
      if (errorRate > 10) {
        insights.push({
          type: 'warning',
          message: `Error rate is ${errorRate.toFixed(1)}% - consider reviewing workflow stability`
        });
      }
    }

    // Step performance
    if (current.stepTimings && historical.stepAverages) {
      Object.entries(current.stepTimings).forEach(([step, time]) => {
        const avgTime = historical.stepAverages[step];
        if (avgTime && time > avgTime * 1.5) {
          insights.push({
            type: 'info',
            message: `${step.replace('_', ' ')} step took longer than usual`
          });
        }
      });
    }

    return insights;
  }

  function formatTime(ms: number): string {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  }

  function formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }
</script>

<div class="workflow-metrics {compact ? 'compact' : ''}">
  <!-- Current Workflow Metrics -->
  {#if currentMetrics}
    <div class="metrics-section mb-6">
      <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
        Current Workflow
      </h3>
      
      <div class="grid grid-cols-2 {compact ? 'gap-3' : 'gap-4'}">
        <!-- Total Processing Time -->
        <div class="metric-card">
          <div class="metric-label">Processing Time</div>
          <div class="metric-value">{formatTime(currentMetrics.totalProcessingTime)}</div>
        </div>

        <!-- Current Step -->
        <div class="metric-card">
          <div class="metric-label">Current Step</div>
          <div class="metric-value capitalize">{currentMetrics.currentStep?.replace('_', ' ') || 'N/A'}</div>
        </div>

        <!-- Responses Generated -->
        <div class="metric-card">
          <div class="metric-label">Responses</div>
          <div class="metric-value">{currentMetrics.responsesGenerated}/3</div>
        </div>

        <!-- Status -->
        <div class="metric-card">
          <div class="metric-label">Status</div>
          <div class="metric-value {currentMetrics.hasError ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">
            {currentMetrics.hasError ? 'Error' : 'Success'}
          </div>
        </div>
      </div>

      <!-- Step Timings -->
      {#if Object.keys(currentMetrics.stepTimings).length > 0}
        <div class="mt-4">
          <h4 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">Step Timings</h4>
          <div class="space-y-2">
            {#each Object.entries(currentMetrics.stepTimings) as [step, time]}
              <div class="flex justify-between items-center text-sm">
                <span class="text-neutral-600 dark:text-neutral-400 capitalize">
                  {step.replace('_', ' ')}
                </span>
                <span class="font-medium text-neutral-900 dark:text-neutral-100">
                  {formatTime(time)}
                </span>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  {/if}

  <!-- Historical Metrics -->
  {#if historicalMetrics}
    <div class="metrics-section mb-6">
      <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
        Historical Performance
      </h3>
      
      <div class="grid grid-cols-2 {compact ? 'gap-3' : 'gap-4'}">
        <!-- Total Workflows -->
        <div class="metric-card">
          <div class="metric-label">Total Workflows</div>
          <div class="metric-value">{historicalMetrics.totalWorkflows}</div>
        </div>

        <!-- Success Rate -->
        <div class="metric-card">
          <div class="metric-label">Success Rate</div>
          <div class="metric-value {historicalMetrics.successRate >= 90 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}">
            {formatPercentage(historicalMetrics.successRate)}
          </div>
        </div>

        <!-- Average Processing Time -->
        <div class="metric-card">
          <div class="metric-label">Avg Processing Time</div>
          <div class="metric-value">{formatTime(historicalMetrics.avgProcessingTime)}</div>
        </div>

        <!-- Error Count -->
        <div class="metric-card">
          <div class="metric-label">Errors</div>
          <div class="metric-value {historicalMetrics.errorWorkflows > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}">
            {historicalMetrics.errorWorkflows}
          </div>
        </div>
      </div>

      <!-- Step Averages -->
      {#if Object.keys(historicalMetrics.stepAverages).length > 0}
        <div class="mt-4">
          <h4 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">Average Step Times</h4>
          <div class="space-y-2">
            {#each Object.entries(historicalMetrics.stepAverages) as [step, avgTime]}
              <div class="flex justify-between items-center text-sm">
                <span class="text-neutral-600 dark:text-neutral-400 capitalize">
                  {step.replace('_', ' ')}
                </span>
                <span class="font-medium text-neutral-900 dark:text-neutral-100">
                  {formatTime(avgTime)}
                </span>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </div>
  {/if}

  <!-- Performance Insights -->
  {#if performanceInsights.length > 0}
    <div class="metrics-section">
      <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-3">
        Performance Insights
      </h3>
      
      <div class="space-y-2">
        {#each performanceInsights as insight}
          <div class="insight-card insight-{insight.type}">
            <div class="insight-icon">
              {#if insight.type === 'success'}
                ✅
              {:else if insight.type === 'warning'}
                ⚠️
              {:else}
                ℹ️
              {/if}
            </div>
            <div class="insight-message">{insight.message}</div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- No Data State -->
  {#if !currentMetrics && !historicalMetrics}
    <div class="text-center py-8 text-neutral-500 dark:text-neutral-400">
      <div class="text-4xl mb-2">📊</div>
      <p>No workflow metrics available</p>
      <p class="text-sm">Start a conversation to see performance data</p>
    </div>
  {/if}
</div>

<style>
  .workflow-metrics.compact {
    @apply text-sm;
  }

  .metrics-section {
    @apply border-b border-neutral-200 dark:border-neutral-700 pb-4 last:border-b-0;
  }

  .metric-card {
    @apply bg-neutral-50 dark:bg-neutral-800 rounded-lg p-3 border border-neutral-200 dark:border-neutral-700;
  }

  .metric-label {
    @apply text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1;
  }

  .metric-value {
    @apply text-lg font-semibold text-neutral-900 dark:text-neutral-100;
  }

  .workflow-metrics.compact .metric-value {
    @apply text-base;
  }

  .insight-card {
    @apply flex items-start space-x-2 p-3 rounded-lg border;
  }

  .insight-card.insight-success {
    @apply bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700;
  }

  .insight-card.insight-warning {
    @apply bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700;
  }

  .insight-card.insight-info {
    @apply bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700;
  }

  .insight-icon {
    @apply flex-shrink-0 text-sm;
  }

  .insight-message {
    @apply text-sm text-neutral-700 dark:text-neutral-300;
  }
</style>
