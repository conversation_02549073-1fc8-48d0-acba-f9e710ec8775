<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let isActive: boolean = false;
  export let currentStep: string = 'analysis';
  export let canPause: boolean = true;
  export let canReset: boolean = true;
  export let canStepThrough: boolean = true;

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Component state
  let isStepMode: boolean = false;

  function handleAction(action: string) {
    dispatch('action', { action });
  }

  function toggleStepMode() {
    isStepMode = !isStepMode;
    handleAction(isStepMode ? 'enable_step_mode' : 'disable_step_mode');
  }
</script>

<div class="workflow-controls flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg mt-4">
  <!-- Status Indicator -->
  <div class="flex items-center space-x-3">
    <div class="flex items-center space-x-2">
      <div class="w-2 h-2 rounded-full {isActive ? 'bg-green-500 animate-pulse' : 'bg-neutral-400'}"></div>
      <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">
        {isActive ? 'Active' : 'Idle'}
      </span>
    </div>
    
    {#if currentStep !== 'complete'}
      <div class="text-sm text-neutral-600 dark:text-neutral-400">
        Processing: <span class="font-medium capitalize">{currentStep.replace('_', ' ')}</span>
      </div>
    {/if}
  </div>

  <!-- Control Buttons -->
  <div class="flex items-center space-x-2">
    <!-- Step Mode Toggle -->
    {#if canStepThrough}
      <button
        class="px-3 py-1 text-xs font-medium rounded-md border transition-colors
               {isStepMode 
                 ? 'bg-blue-100 text-blue-700 border-blue-300 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-600' 
                 : 'bg-white text-neutral-700 border-neutral-300 hover:bg-neutral-50 dark:bg-neutral-700 dark:text-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-600'}"
        on:click={toggleStepMode}
        disabled={isActive}
      >
        {isStepMode ? '📍 Step Mode' : '⚡ Auto Mode'}
      </button>
    {/if}

    <!-- Pause/Resume Button -->
    {#if canPause}
      <button
        class="px-3 py-1 text-xs font-medium rounded-md border transition-colors
               bg-white text-neutral-700 border-neutral-300 hover:bg-neutral-50 
               dark:bg-neutral-700 dark:text-neutral-300 dark:border-neutral-600 dark:hover:bg-neutral-600
               disabled:opacity-50 disabled:cursor-not-allowed"
        on:click={() => handleAction(isActive ? 'pause' : 'resume')}
        disabled={currentStep === 'complete'}
      >
        {isActive ? '⏸️ Pause' : '▶️ Resume'}
      </button>
    {/if}

    <!-- Reset Button -->
    {#if canReset}
      <button
        class="px-3 py-1 text-xs font-medium rounded-md border transition-colors
               bg-white text-red-700 border-red-300 hover:bg-red-50 
               dark:bg-neutral-700 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900/30
               disabled:opacity-50 disabled:cursor-not-allowed"
        on:click={() => handleAction('reset')}
        disabled={isActive}
      >
        🔄 Reset
      </button>
    {/if}

    <!-- Step Forward Button (only in step mode) -->
    {#if isStepMode && !isActive}
      <button
        class="px-3 py-1 text-xs font-medium rounded-md border transition-colors
               bg-blue-100 text-blue-700 border-blue-300 hover:bg-blue-200 
               dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-600 dark:hover:bg-blue-900/50
               disabled:opacity-50 disabled:cursor-not-allowed"
        on:click={() => handleAction('step_forward')}
        disabled={currentStep === 'complete'}
      >
        ⏭️ Next Step
      </button>
    {/if}
  </div>
</div>

<!-- Step Mode Info -->
{#if isStepMode}
  <div class="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded text-xs text-blue-700 dark:text-blue-300">
    <strong>Step Mode:</strong> Workflow will pause at each step. Use "Next Step" to continue.
  </div>
{/if}

<style>
  .workflow-controls button:disabled {
    @apply cursor-not-allowed opacity-50;
  }
  
  .workflow-controls button:not(:disabled):hover {
    @apply transform scale-105;
  }
</style>
