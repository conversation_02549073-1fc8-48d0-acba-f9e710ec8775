<script lang="ts">
  import { onMount } from 'svelte';
  import type { Conversation } from '../../types/index.js';

  export let conversation: Conversation;

  let evaluationData: any = null;
  let loading = false;
  let error: string | null = null;

  onMount(async () => {
    await loadEvaluationData();
  });

  async function loadEvaluationData() {
    try {
      loading = true;
      error = null;

      // Try to fetch CBT evaluation data
      const response = await fetch(`/api/evaluations/conversation/${conversation.id}`);
      if (response.ok) {
        const data = await response.json();
        evaluationData = data.evaluation;
      } else if (response.status === 404) {
        // No evaluation data available yet
        evaluationData = null;
      } else {
        throw new Error(`Failed to load evaluation: ${response.statusText}`);
      }
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to load evaluation data';
      console.error('Error loading evaluation:', err);
    } finally {
      loading = false;
    }
  }

  function getScoreColor(score: number): string {
    if (score >= 5) return 'text-green-600 dark:text-green-400';
    if (score >= 3) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  }

  function getScoreBackground(score: number): string {
    if (score >= 5) return 'bg-green-100 dark:bg-green-900/50';
    if (score >= 3) return 'bg-yellow-100 dark:bg-yellow-900/50';
    return 'bg-red-100 dark:bg-red-900/50';
  }

  function getAssessmentColor(assessment: string): string {
    switch (assessment.toLowerCase()) {
      case 'excellent': return 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300';
      case 'good': return 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300';
      case 'fair': return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300';
      case 'poor': return 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300';
      default: return 'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300';
    }
  }

  function formatDimensionName(key: string): string {
    const names: { [key: string]: string } = {
      cbtValidity: 'CBT Validity',
      cbtAppropriateness: 'CBT Appropriateness',
      cbtAccuracy: 'CBT Accuracy',
      esAppropriateness: 'Emotional Support Appropriateness',
      stability: 'Therapeutic Stability'
    };
    return names[key] || key;
  }

  // Calculate session metrics from conversation data
  $: sessionMetrics = {
    duration: conversation.endTime ? 
      Math.round((new Date(conversation.endTime).getTime() - new Date(conversation.startTime).getTime()) / 60000) : 
      null,
    messageCount: conversation.messages?.length || 0,
    therapistMessages: conversation.messages?.filter(m => m.sender === 'therapist').length || 0,
    patientMessages: conversation.messages?.filter(m => m.sender === 'patient').length || 0,
    avgResponseTime: conversation.analytics?.averageResponseTime || null
  };

  $: progressMetrics = {
    sentimentImprovement: calculateSentimentImprovement(),
    engagementTrend: calculateEngagementTrend(),
    motivationTrend: calculateMotivationTrend()
  };

  function calculateSentimentImprovement(): string {
    if (!conversation.messages) return 'N/A';
    
    const analyses = conversation.messages
      .filter(m => m.sender === 'therapist' && m.metadata?.patientAnalysis)
      .map(m => m.metadata?.patientAnalysis?.sentiment)
      .filter(s => s != null);

    if (analyses.length < 2) return 'Insufficient data';

    const first = analyses[0];
    const last = analyses[analyses.length - 1];
    
    if (first === last) return 'Stable';
    if ((first === 'negative' && last === 'neutral') || 
        (first === 'negative' && last === 'positive') ||
        (first === 'neutral' && last === 'positive')) {
      return 'Improved';
    }
    if ((first === 'positive' && last === 'neutral') ||
        (first === 'positive' && last === 'negative') ||
        (first === 'neutral' && last === 'negative')) {
      return 'Declined';
    }
    return 'Stable';
  }

  function calculateEngagementTrend(): string {
    if (!conversation.messages) return 'N/A';
    
    const analyses = conversation.messages
      .filter(m => m.sender === 'therapist' && m.metadata?.patientAnalysis)
      .map(m => m.metadata?.patientAnalysis?.engagementLevel)
      .filter(e => e != null);

    if (analyses.length < 2) return 'Insufficient data';

    const first = analyses[0];
    const last = analyses[analyses.length - 1];
    
    const levels = { low: 1, medium: 2, high: 3 };
    const firstLevel = levels[first as keyof typeof levels] || 1;
    const lastLevel = levels[last as keyof typeof levels] || 1;
    
    if (lastLevel > firstLevel) return 'Increased';
    if (lastLevel < firstLevel) return 'Decreased';
    return 'Stable';
  }

  function calculateMotivationTrend(): string {
    if (!conversation.messages) return 'N/A';
    
    const analyses = conversation.messages
      .filter(m => m.sender === 'therapist' && m.metadata?.patientAnalysis)
      .map(m => m.metadata?.patientAnalysis?.motivationLevel)
      .filter(m => m != null);

    if (analyses.length < 2) return 'Insufficient data';

    const first = analyses[0];
    const last = analyses[analyses.length - 1];
    
    const levels = { low: 1, medium: 2, high: 3 };
    const firstLevel = levels[first as keyof typeof levels] || 1;
    const lastLevel = levels[last as keyof typeof levels] || 1;
    
    if (lastLevel > firstLevel) return 'Increased';
    if (lastLevel < firstLevel) return 'Decreased';
    return 'Stable';
  }
</script>

<div class="space-y-6">
  <!-- Session Metrics Overview -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
      <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400">Session Duration</div>
      <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
        {sessionMetrics.duration ? `${sessionMetrics.duration} min` : 'Ongoing'}
      </div>
    </div>
    
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
      <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400">Total Messages</div>
      <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
        {sessionMetrics.messageCount}
      </div>
    </div>
    
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
      <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400">Therapist/Patient</div>
      <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
        {sessionMetrics.therapistMessages}/{sessionMetrics.patientMessages}
      </div>
    </div>
    
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
      <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400">Avg Response Time</div>
      <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
        {sessionMetrics.avgResponseTime ? `${sessionMetrics.avgResponseTime}ms` : 'N/A'}
      </div>
    </div>
  </div>

  <!-- Progress Indicators -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
    <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
      Patient Progress Indicators
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="text-center p-4 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
        <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400 mb-2">
          Sentiment Trend
        </div>
        <div class="text-lg font-bold {
          progressMetrics.sentimentImprovement === 'Improved' ? 'text-green-600 dark:text-green-400' :
          progressMetrics.sentimentImprovement === 'Declined' ? 'text-red-600 dark:text-red-400' :
          'text-neutral-600 dark:text-neutral-400'
        }">
          {progressMetrics.sentimentImprovement}
        </div>
      </div>
      
      <div class="text-center p-4 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
        <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400 mb-2">
          Engagement Trend
        </div>
        <div class="text-lg font-bold {
          progressMetrics.engagementTrend === 'Increased' ? 'text-green-600 dark:text-green-400' :
          progressMetrics.engagementTrend === 'Decreased' ? 'text-red-600 dark:text-red-400' :
          'text-neutral-600 dark:text-neutral-400'
        }">
          {progressMetrics.engagementTrend}
        </div>
      </div>
      
      <div class="text-center p-4 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
        <div class="text-sm font-medium text-neutral-600 dark:text-neutral-400 mb-2">
          Motivation Trend
        </div>
        <div class="text-lg font-bold {
          progressMetrics.motivationTrend === 'Increased' ? 'text-green-600 dark:text-green-400' :
          progressMetrics.motivationTrend === 'Decreased' ? 'text-red-600 dark:text-red-400' :
          'text-neutral-600 dark:text-neutral-400'
        }">
          {progressMetrics.motivationTrend}
        </div>
      </div>
    </div>
  </div>

  <!-- CBT Evaluation Results -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
    <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
      CBT Effectiveness Evaluation
    </h3>
    
    {#if loading}
      <div class="flex items-center justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        <span class="ml-3 text-neutral-600 dark:text-neutral-400">Loading evaluation...</span>
      </div>
    {:else if error}
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-center">
          <span class="text-red-600 dark:text-red-400 text-xl mr-2">⚠️</span>
          <div>
            <h4 class="text-red-800 dark:text-red-200 font-medium">Evaluation Error</h4>
            <p class="text-red-700 dark:text-red-300 text-sm mt-1">{error}</p>
          </div>
        </div>
      </div>
    {:else if evaluationData}
      <!-- Evaluation Results Display -->
      <div class="space-y-4">
        <!-- Overall Score -->
        <div class="text-center">
          <div class="flex items-center justify-center space-x-4">
            <div class="text-3xl font-bold {getScoreColor(evaluationData.overallScore)}">
              {evaluationData.overallScore.toFixed(1)}/6.0
            </div>
            <div class="px-3 py-1 rounded-full text-sm font-medium {getAssessmentColor(evaluationData.overallAssessment)}">
              {evaluationData.overallAssessment.toUpperCase()}
            </div>
          </div>
        </div>

        <!-- Dimension Scores -->
        <div class="space-y-3">
          <h4 class="font-medium text-neutral-800 dark:text-neutral-200">Evaluation Dimensions</h4>
          {#each Object.entries(evaluationData.dimensions) as [key, dimension]}
            <div class="flex items-center justify-between p-3 rounded-lg border border-neutral-200 dark:border-neutral-700">
              <span class="font-medium text-neutral-900 dark:text-neutral-100">
                {formatDimensionName(key)}
              </span>
              <span class="text-sm px-2 py-1 rounded {getScoreBackground(dimension.score)} {getScoreColor(dimension.score)} font-medium">
                {dimension.score}/6
              </span>
            </div>
          {/each}
        </div>
      </div>
    {:else}
      <div class="text-center py-8">
        <div class="text-neutral-400 dark:text-neutral-500 text-4xl mb-3">📊</div>
        <h4 class="text-neutral-600 dark:text-neutral-400 font-medium mb-2">No Evaluation Available</h4>
        <p class="text-neutral-500 dark:text-neutral-500 text-sm">
          CBT evaluation will be available after the session is completed and processed.
        </p>
      </div>
    {/if}
  </div>
</div>
