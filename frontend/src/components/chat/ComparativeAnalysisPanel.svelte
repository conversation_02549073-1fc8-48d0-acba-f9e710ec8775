<script lang="ts">
  import type { Conversation } from '../../types/index.js';

  export let conversation: Conversation;
  export let multiTherapistData: any = null;

  // Color utility functions
  function getSentimentColor(sentiment: string): string {
    switch (sentiment) {
      case 'positive': return 'text-green-600 dark:text-green-400';
      case 'negative': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getEngagementColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getMotivationColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getModeColor(mode: string): string {
    switch (mode.toLowerCase()) {
      case 'cbt-only': return 'bg-blue-100 dark:bg-blue-900/30';
      case 'mi-fixed-pretreatment': return 'bg-green-100 dark:bg-green-900/30';
      case 'dynamic-adaptive': return 'bg-purple-100 dark:bg-purple-900/30';
      default: return 'bg-gray-100 dark:bg-gray-900/30';
    }
  }

  function getModeTextColor(mode: string): string {
    switch (mode.toLowerCase()) {
      case 'cbt-only': return 'text-blue-800 dark:text-blue-200';
      case 'mi-fixed-pretreatment': return 'text-green-800 dark:text-green-200';
      case 'dynamic-adaptive': return 'text-purple-800 dark:text-purple-200';
      default: return 'text-gray-800 dark:text-gray-200';
    }
  }

  // Extract comparative data
  $: comparativeData = extractComparativeData();

  function extractComparativeData() {
    if (!conversation.messages) return null;

    const modes = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];
    const comparison: any = {
      modes: {},
      summary: {
        totalMessages: 0,
        totalAnalysisPoints: 0,
        bestPerformingMode: null,
        overallTrends: {}
      }
    };

    modes.forEach(mode => {
      const modeMessages = conversation.messages.filter(m => 
        m.sender === 'therapist' && 
        (m.metadata?.therapeuticApproach?.approachName?.toLowerCase().includes(mode.split('-')[0]) ||
         m.metadata?.patientAnalysis)
      );

      const patientAnalyses = modeMessages
        .filter(m => m.metadata?.patientAnalysis)
        .map(m => m.metadata?.patientAnalysis)
        .filter(analysis => analysis.sentiment && analysis.engagementLevel && analysis.motivationLevel);

      // Calculate improvement scores
      const sentimentImprovement = calculateImprovement(patientAnalyses, 'sentiment');
      const engagementImprovement = calculateImprovement(patientAnalyses, 'engagementLevel');
      const motivationImprovement = calculateImprovement(patientAnalyses, 'motivationLevel');

      comparison.modes[mode] = {
        messageCount: modeMessages.length,
        analysisCount: patientAnalyses.length,
        finalSentiment: patientAnalyses[patientAnalyses.length - 1]?.sentiment || 'neutral',
        finalEngagement: patientAnalyses[patientAnalyses.length - 1]?.engagementLevel || 'low',
        finalMotivation: patientAnalyses[patientAnalyses.length - 1]?.motivationLevel || 'low',
        improvements: {
          sentiment: sentimentImprovement,
          engagement: engagementImprovement,
          motivation: motivationImprovement
        },
        overallScore: (sentimentImprovement + engagementImprovement + motivationImprovement) / 3
      };

      comparison.summary.totalMessages += modeMessages.length;
      comparison.summary.totalAnalysisPoints += patientAnalyses.length;
    });

    // Determine best performing mode
    let bestScore = -Infinity;
    let bestMode = null;
    Object.entries(comparison.modes).forEach(([mode, data]: [string, any]) => {
      if (data.overallScore > bestScore) {
        bestScore = data.overallScore;
        bestMode = mode;
      }
    });
    comparison.summary.bestPerformingMode = bestMode;

    return comparison;
  }

  function calculateImprovement(analyses: any[], metric: string): number {
    if (analyses.length < 2) return 0;
    
    const first = analyses[0][metric];
    const last = analyses[analyses.length - 1][metric];
    
    const scoreMap = {
      'positive': 3, 'neutral': 2, 'negative': 1,
      'high': 3, 'medium': 2, 'low': 1
    };
    
    const firstScore = scoreMap[first] || 2;
    const lastScore = scoreMap[last] || 2;
    
    return lastScore - firstScore;
  }

  function getModeName(mode: string): string {
    switch (mode) {
      case 'cbt-only': return 'CBT-Only';
      case 'mi-fixed-pretreatment': return 'Fixed Pretreatment';
      case 'dynamic-adaptive': return 'Dynamic Adaptive';
      default: return mode;
    }
  }

  function getImprovementColor(score: number): string {
    if (score > 0) return 'text-green-600 dark:text-green-400';
    if (score < 0) return 'text-red-600 dark:text-red-400';
    return 'text-gray-600 dark:text-gray-400';
  }

  function getImprovementIcon(score: number): string {
    if (score > 0) return '↗️';
    if (score < 0) return '↘️';
    return '➡️';
  }
</script>

{#if comparativeData}
  <div class="space-y-6">
    <!-- Comparative Overview -->
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
      <h3 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
        Comparative Mode Analysis
      </h3>
      
      <!-- Best Performing Mode Highlight -->
      {#if comparativeData.summary.bestPerformingMode}
        <div class="mb-6 p-4 rounded-lg {getModeColor(comparativeData.summary.bestPerformingMode)} border border-current border-opacity-20">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium {getModeTextColor(comparativeData.summary.bestPerformingMode)}">
                🏆 Best Performing Mode: {getModeName(comparativeData.summary.bestPerformingMode)}
              </h4>
              <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
                Highest overall improvement score: {comparativeData.modes[comparativeData.summary.bestPerformingMode].overallScore.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      {/if}

      <!-- Mode Comparison Table -->
      <div class="overflow-x-auto">
        <table class="w-full text-sm">
          <thead>
            <tr class="border-b border-neutral-200 dark:border-neutral-700">
              <th class="text-left py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Mode</th>
              <th class="text-center py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Messages</th>
              <th class="text-center py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Analysis Points</th>
              <th class="text-center py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Final Sentiment</th>
              <th class="text-center py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Final Engagement</th>
              <th class="text-center py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Final Motivation</th>
              <th class="text-center py-3 px-2 font-medium text-neutral-700 dark:text-neutral-300">Overall Score</th>
            </tr>
          </thead>
          <tbody>
            {#each Object.entries(comparativeData.modes) as [mode, data]}
              <tr class="border-b border-neutral-100 dark:border-neutral-800">
                <td class="py-3 px-2">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full {getModeColor(mode)}"></div>
                    <span class="font-medium {getModeTextColor(mode)}">{getModeName(mode)}</span>
                  </div>
                </td>
                <td class="text-center py-3 px-2">{data.messageCount}</td>
                <td class="text-center py-3 px-2">{data.analysisCount}</td>
                <td class="text-center py-3 px-2">
                  <span class="px-2 py-1 rounded text-xs font-medium {getSentimentColor(data.finalSentiment)} bg-current bg-opacity-10">
                    {data.finalSentiment.toUpperCase()}
                  </span>
                </td>
                <td class="text-center py-3 px-2">
                  <span class="px-2 py-1 rounded text-xs font-medium {getEngagementColor(data.finalEngagement)} bg-current bg-opacity-10">
                    {data.finalEngagement.toUpperCase()}
                  </span>
                </td>
                <td class="text-center py-3 px-2">
                  <span class="px-2 py-1 rounded text-xs font-medium {getMotivationColor(data.finalMotivation)} bg-current bg-opacity-10">
                    {data.finalMotivation.toUpperCase()}
                  </span>
                </td>
                <td class="text-center py-3 px-2">
                  <span class="font-bold {data.overallScore > 0 ? 'text-green-600 dark:text-green-400' : data.overallScore < 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-600 dark:text-gray-400'}">
                    {data.overallScore.toFixed(2)}
                  </span>
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Improvement Trends Comparison -->
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
      <h4 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
        Improvement Trends Comparison
      </h4>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {#each Object.entries(comparativeData.modes) as [mode, data]}
          <div class="p-4 rounded-lg border {getModeColor(mode)} border-current border-opacity-20">
            <h5 class="font-medium {getModeTextColor(mode)} mb-3">
              {getModeName(mode)}
            </h5>
            
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span class="text-sm text-neutral-600 dark:text-neutral-400">Sentiment</span>
                <div class="flex items-center space-x-1">
                  <span class="text-sm {getImprovementColor(data.improvements.sentiment)}">
                    {getImprovementIcon(data.improvements.sentiment)}
                  </span>
                  <span class="text-sm font-medium {getImprovementColor(data.improvements.sentiment)}">
                    {data.improvements.sentiment > 0 ? '+' : ''}{data.improvements.sentiment}
                  </span>
                </div>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-neutral-600 dark:text-neutral-400">Engagement</span>
                <div class="flex items-center space-x-1">
                  <span class="text-sm {getImprovementColor(data.improvements.engagement)}">
                    {getImprovementIcon(data.improvements.engagement)}
                  </span>
                  <span class="text-sm font-medium {getImprovementColor(data.improvements.engagement)}">
                    {data.improvements.engagement > 0 ? '+' : ''}{data.improvements.engagement}
                  </span>
                </div>
              </div>
              
              <div class="flex items-center justify-between">
                <span class="text-sm text-neutral-600 dark:text-neutral-400">Motivation</span>
                <div class="flex items-center space-x-1">
                  <span class="text-sm {getImprovementColor(data.improvements.motivation)}">
                    {getImprovementIcon(data.improvements.motivation)}
                  </span>
                  <span class="text-sm font-medium {getImprovementColor(data.improvements.motivation)}">
                    {data.improvements.motivation > 0 ? '+' : ''}{data.improvements.motivation}
                  </span>
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </div>

    <!-- Summary Statistics -->
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
      <h4 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
        Session Summary
      </h4>
      
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="text-center p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
          <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            {comparativeData.summary.totalMessages}
          </div>
          <div class="text-sm text-neutral-600 dark:text-neutral-400">
            Total Messages
          </div>
        </div>
        
        <div class="text-center p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
          <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            {comparativeData.summary.totalAnalysisPoints}
          </div>
          <div class="text-sm text-neutral-600 dark:text-neutral-400">
            Analysis Points
          </div>
        </div>
        
        <div class="text-center p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
          <div class="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
            3
          </div>
          <div class="text-sm text-neutral-600 dark:text-neutral-400">
            Therapeutic Modes
          </div>
        </div>
        
        <div class="text-center p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
          <div class="text-2xl font-bold {getModeTextColor(comparativeData.summary.bestPerformingMode || '')}">
            {getModeName(comparativeData.summary.bestPerformingMode || 'N/A')}
          </div>
          <div class="text-sm text-neutral-600 dark:text-neutral-400">
            Best Mode
          </div>
        </div>
      </div>
    </div>
  </div>
{:else}
  <div class="text-center py-8">
    <p class="text-neutral-600 dark:text-neutral-400">No comparative analysis data available</p>
  </div>
{/if}
