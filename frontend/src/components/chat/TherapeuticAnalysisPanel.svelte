<script lang="ts">
  import type { Conversation } from '../../types/index.js';
  import MultiModeAnalysisPanel from './MultiModeAnalysisPanel.svelte';
  import ComparativeAnalysisPanel from './ComparativeAnalysisPanel.svelte';

  export let conversation: Conversation;
  export let multiTherapistData: any = null;

  // UI state for mode switching
  let analysisMode: 'single' | 'multi' | 'comparative' = 'single';

  // Detect if this is a multi-therapist conversation
  $: isMultiTherapist = conversation.therapist_mode === 'multi-therapist' ||
                        (multiTherapistData && Object.keys(multiTherapistData).length > 0) ||
                        hasMultiModeData();

  function hasMultiModeData(): boolean {
    if (!conversation.messages) return false;

    const modes = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];
    return modes.some(mode =>
      conversation.messages.some(m =>
        m.sender === 'therapist' &&
        m.metadata?.therapeuticApproach?.approachName?.toLowerCase().includes(mode.split('-')[0])
      )
    );
  }

  // Auto-set analysis mode based on conversation type
  $: if (isMultiTherapist && analysisMode === 'single') {
    analysisMode = 'multi';
  }

  function getSentimentColor(sentiment: string): string {
    switch (sentiment) {
      case 'positive': return 'text-green-600 dark:text-green-400';
      case 'negative': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getEngagementColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getMotivationColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  // Reactive computations for analysis data
  $: therapeuticApproaches = conversation.messages
    ?.filter(m => m.sender === 'therapist' && m.metadata?.therapeuticApproach)
    .map(m => m.metadata?.therapeuticApproach)
    .filter(ta => ta != null) || [];

  $: patientAnalyses = conversation.messages
    ?.filter(m => m.sender === 'therapist' && m.metadata?.patientAnalysis)
    .map((m, index) => ({ ...m.metadata?.patientAnalysis, messageIndex: index + 1 }))
    .filter(analysis => analysis.sentiment && analysis.engagementLevel && analysis.motivationLevel) || [];

  $: uniqueApproaches = [...new Set(therapeuticApproaches.map(ta => ta?.approachName).filter(name => name != null))];
  $: uniqueTechniques = [...new Set(therapeuticApproaches.map(ta => ta?.selectedTechnique?.name).filter(name => name != null))];
</script>

<div class="space-y-6">
  <!-- Mode Selection (for multi-therapist conversations) -->
  {#if isMultiTherapist}
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100">
          Analysis Mode
        </h3>
        <div class="flex space-x-2">
          <button
            class="px-3 py-1 text-sm rounded-md transition-colors {analysisMode === 'single' ? 'bg-primary-600 text-white' : 'bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-600'}"
            on:click={() => analysisMode = 'single'}
          >
            Single Mode
          </button>
          <button
            class="px-3 py-1 text-sm rounded-md transition-colors {analysisMode === 'multi' ? 'bg-primary-600 text-white' : 'bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-600'}"
            on:click={() => analysisMode = 'multi'}
          >
            Multi-Mode
          </button>
          <button
            class="px-3 py-1 text-sm rounded-md transition-colors {analysisMode === 'comparative' ? 'bg-primary-600 text-white' : 'bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-200 dark:hover:bg-neutral-600'}"
            on:click={() => analysisMode = 'comparative'}
          >
            Comparative
          </button>
        </div>
      </div>
      <p class="text-sm text-neutral-600 dark:text-neutral-400">
        {#if analysisMode === 'single'}
          Standard single-mode therapeutic analysis view
        {:else if analysisMode === 'multi'}
          Detailed analysis for each therapeutic mode (CBT-only, Fixed Pretreatment, Dynamic)
        {:else}
          Side-by-side comparison of all therapeutic modes with performance metrics
        {/if}
      </p>
    </div>
  {/if}

  <!-- Analysis Content -->
  {#if analysisMode === 'multi' && isMultiTherapist}
    <MultiModeAnalysisPanel {conversation} {multiTherapistData} />
  {:else if analysisMode === 'comparative' && isMultiTherapist}
    <ComparativeAnalysisPanel {conversation} {multiTherapistData} />
  {:else}
    <!-- Standard Single Mode Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
  <!-- Session Overview -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
    <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
      Session Overview
    </h3>
    
    {#if conversation.analytics}
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-neutral-600 dark:text-neutral-400">Total Messages:</span>
          <span class="font-medium text-neutral-900 dark:text-neutral-100">
            {conversation.analytics.totalTurns}
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-neutral-600 dark:text-neutral-400">Avg Response Time:</span>
          <span class="font-medium text-neutral-900 dark:text-neutral-100">
            {conversation.analytics.averageResponseTime}ms
          </span>
        </div>
      </div>
    {:else}
      <div class="space-y-3">
        <div class="flex justify-between">
          <span class="text-neutral-600 dark:text-neutral-400">Total Messages:</span>
          <span class="font-medium text-neutral-900 dark:text-neutral-100">
            {conversation.messages?.length || 0}
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-neutral-600 dark:text-neutral-400">Therapist Messages:</span>
          <span class="font-medium text-neutral-900 dark:text-neutral-100">
            {conversation.messages?.filter(m => m.sender === 'therapist').length || 0}
          </span>
        </div>
        <div class="flex justify-between">
          <span class="text-neutral-600 dark:text-neutral-400">Patient Messages:</span>
          <span class="font-medium text-neutral-900 dark:text-neutral-100">
            {conversation.messages?.filter(m => m.sender === 'patient').length || 0}
          </span>
        </div>
      </div>
    {/if}
  </div>

  <!-- Therapeutic Approaches Used -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
    <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
      Therapeutic Approaches
    </h3>
    
    {#if uniqueApproaches.length > 0}
      <div class="space-y-2">
        {#each uniqueApproaches as approach}
          <div class="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
            <span class="text-blue-700 dark:text-blue-300 font-medium">{approach}</span>
            <span class="text-xs text-blue-600 dark:text-blue-400">
              {therapeuticApproaches.filter(ta => ta?.approachName === approach).length} times
            </span>
          </div>
        {/each}
      </div>
    {:else}
      <p class="text-neutral-600 dark:text-neutral-400">No therapeutic approaches recorded</p>
    {/if}
  </div>

  <!-- Therapeutic Techniques Used -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
    <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
      Therapeutic Techniques
    </h3>
    
    {#if uniqueTechniques.length > 0}
      <div class="space-y-2">
        {#each uniqueTechniques as technique}
          <div class="flex items-center justify-between p-2 bg-purple-50 dark:bg-purple-900/20 rounded">
            <span class="text-purple-700 dark:text-purple-300 font-medium">{technique}</span>
            <span class="text-xs text-purple-600 dark:text-purple-400">
              {therapeuticApproaches.filter(ta => ta?.selectedTechnique?.name === technique).length} times
            </span>
          </div>
        {/each}
      </div>
    {:else}
      <p class="text-neutral-600 dark:text-neutral-400">No therapeutic techniques recorded</p>
    {/if}
  </div>

  <!-- Patient Progress Tracking -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
    <h3 class="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4">
      Patient Progress Summary
    </h3>
    
    {#if patientAnalyses.length > 0}
      <div class="space-y-4">
        <!-- Overall Progress Indicators -->
        <div class="grid grid-cols-3 gap-4 text-center">
          <div class="p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
            <div class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Final Sentiment
            </div>
            <div class="text-lg font-bold {getSentimentColor(patientAnalyses[patientAnalyses.length - 1]?.sentiment || 'neutral')}">
              {(patientAnalyses[patientAnalyses.length - 1]?.sentiment || 'N/A').toUpperCase()}
            </div>
          </div>
          <div class="p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
            <div class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Final Engagement
            </div>
            <div class="text-lg font-bold {getEngagementColor(patientAnalyses[patientAnalyses.length - 1]?.engagementLevel || 'low')}">
              {(patientAnalyses[patientAnalyses.length - 1]?.engagementLevel || 'N/A').toUpperCase()}
            </div>
          </div>
          <div class="p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
            <div class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Final Motivation
            </div>
            <div class="text-lg font-bold {getMotivationColor(patientAnalyses[patientAnalyses.length - 1]?.motivationLevel || 'low')}">
              {(patientAnalyses[patientAnalyses.length - 1]?.motivationLevel || 'N/A').toUpperCase()}
            </div>
          </div>
        </div>

        <!-- Progress Trends -->
        <div class="space-y-3">
          <h4 class="text-sm font-medium text-neutral-700 dark:text-neutral-300">
            Progress Trends ({patientAnalyses.length} data points)
          </h4>
          
          <!-- Sentiment Trend -->
          <div>
            <div class="text-xs text-neutral-600 dark:text-neutral-400 mb-1">Sentiment Progression</div>
            <div class="flex items-center space-x-1">
              {#each patientAnalyses as analysis, index}
                <div 
                  class="w-3 h-3 rounded-full {getSentimentColor(analysis.sentiment || 'neutral')} bg-current opacity-75"
                  title="Message {index + 1}: {analysis.sentiment}"
                ></div>
              {/each}
            </div>
          </div>

          <!-- Engagement Trend -->
          <div>
            <div class="text-xs text-neutral-600 dark:text-neutral-400 mb-1">Engagement Progression</div>
            <div class="flex items-center space-x-1">
              {#each patientAnalyses as analysis, index}
                <div 
                  class="w-3 h-3 rounded-full {getEngagementColor(analysis.engagementLevel || 'low')} bg-current opacity-75"
                  title="Message {index + 1}: {analysis.engagementLevel}"
                ></div>
              {/each}
            </div>
          </div>

          <!-- Motivation Trend -->
          <div>
            <div class="text-xs text-neutral-600 dark:text-neutral-400 mb-1">Motivation Progression</div>
            <div class="flex items-center space-x-1">
              {#each patientAnalyses as analysis, index}
                <div 
                  class="w-3 h-3 rounded-full {getMotivationColor(analysis.motivationLevel || 'low')} bg-current opacity-75"
                  title="Message {index + 1}: {analysis.motivationLevel}"
                ></div>
              {/each}
            </div>
          </div>
        </div>
      </div>
    {:else}
      <p class="text-neutral-600 dark:text-neutral-400">No patient analysis data available</p>
    {/if}
  </div>
</div>
  {/if}
</div>
