<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  export let searchQuery = '';
  export let selectedPersonaFilter = '';
  export let selectedTherapistMode = '';
  export let selectedDateRange = '';
  export let selectedStatus = '';
  export let availablePersonas: string[] = [];

  const dispatch = createEventDispatcher();

  const therapistModes = [
    { value: '', label: 'All Modes' },
    { value: 'single', label: 'Single Therapist' },
    { value: 'multi-therapist', label: 'Multi-Therapist' }
  ];

  const dateRanges = [
    { label: 'All time', value: 'all' },
    { label: 'Today', value: 'today' },
    { label: 'Last 7 days', value: '7days' },
    { label: 'Last 30 days', value: '30days' },
    { label: 'Last 90 days', value: '90days' }
  ];

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'paused', label: 'Paused' }
  ];

  function handleFilterChange() {
    dispatch('filterChange', {
      searchQuery,
      selectedPersonaFilter,
      selectedTherapistMode,
      selectedDateRange,
      selectedStatus
    });
  }

  function clearFilters() {
    searchQuery = '';
    selectedPersonaFilter = '';
    selectedTherapistMode = '';
    selectedDateRange = 'all';
    selectedStatus = '';
    dispatch('clearFilters');
  }

  function exportResults() {
    dispatch('export');
  }

  // Reactive statements to trigger filter changes
  $: if (searchQuery !== undefined) handleFilterChange();
  $: if (selectedPersonaFilter !== undefined) handleFilterChange();
  $: if (selectedTherapistMode !== undefined) handleFilterChange();
  $: if (selectedDateRange !== undefined) handleFilterChange();
  $: if (selectedStatus !== undefined) handleFilterChange();
</script>

<div class="bg-white dark:bg-neutral-900 border-b border-neutral-200 dark:border-neutral-700 p-4">
  <div class="space-y-4">
    <!-- Search Bar -->
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        placeholder="Search conversations by ID, persona, or content..."
        bind:value={searchQuery}
        class="block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 placeholder-neutral-500 dark:placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      />
    </div>

    <!-- Filter Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Patient Persona Filter -->
      <div>
        <label for="persona-filter" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
          Patient Persona
        </label>
        <select
          id="persona-filter"
          bind:value={selectedPersonaFilter}
          class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="">All Personas</option>
          {#each availablePersonas as persona}
            <option value={persona}>{persona}</option>
          {/each}
        </select>
      </div>

      <!-- Therapist Mode Filter -->
      <div>
        <label for="mode-filter" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
          Therapist Mode
        </label>
        <select
          id="mode-filter"
          bind:value={selectedTherapistMode}
          class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          {#each therapistModes as mode}
            <option value={mode.value}>{mode.label}</option>
          {/each}
        </select>
      </div>

      <!-- Status Filter -->
      <div>
        <label for="status-filter" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
          Status
        </label>
        <select
          id="status-filter"
          bind:value={selectedStatus}
          class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          {#each statusOptions as status}
            <option value={status.value}>{status.label}</option>
          {/each}
        </select>
      </div>

      <!-- Date Range Filter -->
      <div>
        <label for="date-filter" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
          Date Range
        </label>
        <select
          id="date-filter"
          bind:value={selectedDateRange}
          class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          {#each dateRanges as range}
            <option value={range.value}>{range.label}</option>
          {/each}
        </select>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <button
          on:click={clearFilters}
          class="px-4 py-2 text-sm text-neutral-600 dark:text-neutral-400 hover:text-neutral-800 dark:hover:text-neutral-200 border border-neutral-300 dark:border-neutral-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors"
        >
          Clear Filters
        </button>
        
        <button
          on:click={exportResults}
          class="px-4 py-2 text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 border border-primary-300 dark:border-primary-600 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-colors"
        >
          Export Results
        </button>
      </div>

      <!-- Filter Summary -->
      <div class="text-sm text-neutral-500 dark:text-neutral-400">
        {#if searchQuery || selectedPersonaFilter || selectedTherapistMode || selectedDateRange !== 'all' || selectedStatus}
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/50 text-primary-700 dark:text-primary-300">
            Filters Active
          </span>
        {:else}
          <span>No filters applied</span>
        {/if}
      </div>
    </div>

    <!-- Advanced Search Options (Collapsible) -->
    <details class="group">
      <summary class="cursor-pointer text-sm font-medium text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-neutral-100 transition-colors">
        <span class="inline-flex items-center">
          <svg class="w-4 h-4 mr-1 transform group-open:rotate-90 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
          Advanced Search Options
        </span>
      </summary>
      
      <div class="mt-3 p-4 bg-neutral-50 dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Message Count Range -->
          <div>
            <label class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              Message Count Range
            </label>
            <div class="flex items-center space-x-2">
              <input
                type="number"
                placeholder="Min"
                min="0"
                class="w-20 px-2 py-1 text-sm border border-neutral-300 dark:border-neutral-600 rounded bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
              />
              <span class="text-neutral-500 dark:text-neutral-400">to</span>
              <input
                type="number"
                placeholder="Max"
                min="0"
                class="w-20 px-2 py-1 text-sm border border-neutral-300 dark:border-neutral-600 rounded bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
              />
              <span class="text-sm text-neutral-500 dark:text-neutral-400">messages</span>
            </div>
          </div>

          <!-- Duration Range -->
          <div>
            <label class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              Session Duration
            </label>
            <div class="flex items-center space-x-2">
              <input
                type="number"
                placeholder="Min"
                min="0"
                class="w-20 px-2 py-1 text-sm border border-neutral-300 dark:border-neutral-600 rounded bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
              />
              <span class="text-neutral-500 dark:text-neutral-400">to</span>
              <input
                type="number"
                placeholder="Max"
                min="0"
                class="w-20 px-2 py-1 text-sm border border-neutral-300 dark:border-neutral-600 rounded bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
              />
              <span class="text-sm text-neutral-500 dark:text-neutral-400">minutes</span>
            </div>
          </div>
        </div>

        <!-- Search in Content -->
        <div class="mt-4">
          <label class="flex items-center">
            <input
              type="checkbox"
              class="rounded border-neutral-300 dark:border-neutral-600 text-primary-600 focus:ring-primary-500"
            />
            <span class="ml-2 text-sm text-neutral-700 dark:text-neutral-300">
              Search in message content (slower)
            </span>
          </label>
        </div>
      </div>
    </details>
  </div>
</div>
