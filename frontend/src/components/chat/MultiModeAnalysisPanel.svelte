<script lang="ts">
  import type { Conversation } from '../../types/index.js';

  export let conversation: Conversation;
  export let multiTherapistData: any = null; // Multi-therapist session data

  // Color utility functions
  function getSentimentColor(sentiment: string): string {
    switch (sentiment) {
      case 'positive': return 'text-green-600 dark:text-green-400';
      case 'negative': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getEngagementColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getMotivationColor(level: string): string {
    switch (level) {
      case 'high': return 'text-green-600 dark:text-green-400';
      case 'medium': return 'text-yellow-600 dark:text-yellow-400';
      case 'low': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  }

  function getModeColor(mode: string): string {
    switch (mode.toLowerCase()) {
      case 'cbt-only': return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'mi-fixed-pretreatment': return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'dynamic-adaptive': return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800';
      default: return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
    }
  }

  function getModeTextColor(mode: string): string {
    switch (mode.toLowerCase()) {
      case 'cbt-only': return 'text-blue-700 dark:text-blue-300';
      case 'mi-fixed-pretreatment': return 'text-green-700 dark:text-green-300';
      case 'dynamic-adaptive': return 'text-purple-700 dark:text-purple-300';
      default: return 'text-gray-700 dark:text-gray-300';
    }
  }

  // Extract analysis data for each mode
  $: modeAnalysisData = extractModeAnalysisData();

  function extractModeAnalysisData() {
    if (!conversation.messages) return {};

    const modes = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];
    const modeData: any = {};

    modes.forEach(mode => {
      // For multi-therapist conversations, we need to look for mode-specific messages
      const modeMessages = conversation.messages.filter(m => 
        m.sender === 'therapist' && 
        (m.metadata?.therapeuticApproach?.approachName?.toLowerCase().includes(mode.split('-')[0]) ||
         m.metadata?.patientAnalysis)
      );

      const patientAnalyses = modeMessages
        .filter(m => m.metadata?.patientAnalysis)
        .map((m, index) => ({ ...m.metadata?.patientAnalysis, messageIndex: index + 1 }))
        .filter(analysis => analysis.sentiment && analysis.engagementLevel && analysis.motivationLevel);

      const therapeuticApproaches = modeMessages
        .filter(m => m.metadata?.therapeuticApproach)
        .map(m => m.metadata?.therapeuticApproach)
        .filter(ta => ta != null);

      modeData[mode] = {
        messages: modeMessages,
        patientAnalyses,
        therapeuticApproaches,
        uniqueApproaches: [...new Set(therapeuticApproaches.map(ta => ta?.approachName).filter(name => name != null))],
        uniqueTechniques: [...new Set(therapeuticApproaches.map(ta => ta?.selectedTechnique?.name).filter(name => name != null))]
      };
    });

    return modeData;
  }

  function getModeName(mode: string): string {
    switch (mode) {
      case 'cbt-only': return 'CBT-Only Mode';
      case 'mi-fixed-pretreatment': return 'Fixed Pretreatment Mode';
      case 'dynamic-adaptive': return 'Dynamic Mode';
      default: return mode;
    }
  }

  function getModeDescription(mode: string): string {
    switch (mode) {
      case 'cbt-only': return 'Cognitive Behavioral Therapy focused approach';
      case 'mi-fixed-pretreatment': return 'Motivational Interviewing with fixed pretreatment protocol';
      case 'dynamic-adaptive': return 'Adaptive approach switching between CBT and MI based on patient readiness';
      default: return 'Therapeutic mode';
    }
  }
</script>

<div class="space-y-6">
  <!-- Multi-Mode Overview -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
    <h3 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
      Multi-Mode Therapeutic Analysis
    </h3>
    <p class="text-neutral-600 dark:text-neutral-400 mb-4">
      Comparative analysis across three therapeutic modes: CBT-only, Fixed Pretreatment, and Dynamic Adaptive approaches.
    </p>
    
    <!-- Mode Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      {#each Object.entries(modeAnalysisData) as [mode, data]}
        <div class="p-4 rounded-lg border {getModeColor(mode)}">
          <h4 class="font-medium {getModeTextColor(mode)} mb-2">
            {getModeName(mode)}
          </h4>
          <div class="text-sm text-neutral-600 dark:text-neutral-400 space-y-1">
            <div>Messages: {data.messages?.length || 0}</div>
            <div>Analysis Points: {data.patientAnalyses?.length || 0}</div>
            <div>Techniques Used: {data.uniqueTechniques?.length || 0}</div>
          </div>
        </div>
      {/each}
    </div>
  </div>

  <!-- Detailed Mode Analysis -->
  {#each Object.entries(modeAnalysisData) as [mode, data]}
    <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-medium text-neutral-900 dark:text-neutral-100">
          {getModeName(mode)}
        </h4>
        <span class="text-sm text-neutral-500 dark:text-neutral-400">
          {getModeDescription(mode)}
        </span>
      </div>

      {#if data.patientAnalyses && data.patientAnalyses.length > 0}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Progress Indicators -->
          <div>
            <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3">
              Final Progress Indicators
            </h5>
            <div class="grid grid-cols-3 gap-3 text-center">
              <div class="p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">
                  Sentiment
                </div>
                <div class="text-sm font-bold {getSentimentColor(data.patientAnalyses[data.patientAnalyses.length - 1]?.sentiment || 'neutral')}">
                  {(data.patientAnalyses[data.patientAnalyses.length - 1]?.sentiment || 'N/A').toUpperCase()}
                </div>
              </div>
              <div class="p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">
                  Engagement
                </div>
                <div class="text-sm font-bold {getEngagementColor(data.patientAnalyses[data.patientAnalyses.length - 1]?.engagementLevel || 'low')}">
                  {(data.patientAnalyses[data.patientAnalyses.length - 1]?.engagementLevel || 'N/A').toUpperCase()}
                </div>
              </div>
              <div class="p-3 bg-neutral-50 dark:bg-neutral-700 rounded">
                <div class="text-xs font-medium text-neutral-600 dark:text-neutral-400 mb-1">
                  Motivation
                </div>
                <div class="text-sm font-bold {getMotivationColor(data.patientAnalyses[data.patientAnalyses.length - 1]?.motivationLevel || 'low')}">
                  {(data.patientAnalyses[data.patientAnalyses.length - 1]?.motivationLevel || 'N/A').toUpperCase()}
                </div>
              </div>
            </div>
          </div>

          <!-- Progress Trends -->
          <div>
            <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3">
              Progress Trends ({data.patientAnalyses.length} data points)
            </h5>
            <div class="space-y-2">
              <!-- Sentiment Trend -->
              <div>
                <div class="text-xs text-neutral-600 dark:text-neutral-400 mb-1">Sentiment</div>
                <div class="flex items-center space-x-1">
                  {#each data.patientAnalyses as analysis, index}
                    <div 
                      class="w-2 h-2 rounded-full {getSentimentColor(analysis.sentiment || 'neutral')} bg-current opacity-75"
                      title="Message {index + 1}: {analysis.sentiment}"
                    ></div>
                  {/each}
                </div>
              </div>
              <!-- Engagement Trend -->
              <div>
                <div class="text-xs text-neutral-600 dark:text-neutral-400 mb-1">Engagement</div>
                <div class="flex items-center space-x-1">
                  {#each data.patientAnalyses as analysis, index}
                    <div 
                      class="w-2 h-2 rounded-full {getEngagementColor(analysis.engagementLevel || 'low')} bg-current opacity-75"
                      title="Message {index + 1}: {analysis.engagementLevel}"
                    ></div>
                  {/each}
                </div>
              </div>
              <!-- Motivation Trend -->
              <div>
                <div class="text-xs text-neutral-600 dark:text-neutral-400 mb-1">Motivation</div>
                <div class="flex items-center space-x-1">
                  {#each data.patientAnalyses as analysis, index}
                    <div 
                      class="w-2 h-2 rounded-full {getMotivationColor(analysis.motivationLevel || 'low')} bg-current opacity-75"
                      title="Message {index + 1}: {analysis.motivationLevel}"
                    ></div>
                  {/each}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Therapeutic Approaches and Techniques -->
        {#if data.uniqueApproaches && data.uniqueApproaches.length > 0}
          <div class="mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-700">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Approaches Used
                </h5>
                <div class="space-y-1">
                  {#each data.uniqueApproaches as approach}
                    <div class="flex items-center justify-between text-xs p-2 bg-neutral-50 dark:bg-neutral-700 rounded">
                      <span class="font-medium">{approach}</span>
                      <span class="text-neutral-500">
                        {data.therapeuticApproaches.filter(ta => ta?.approachName === approach).length}x
                      </span>
                    </div>
                  {/each}
                </div>
              </div>
              <div>
                <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Techniques Used
                </h5>
                <div class="space-y-1">
                  {#each data.uniqueTechniques as technique}
                    <div class="flex items-center justify-between text-xs p-2 bg-neutral-50 dark:bg-neutral-700 rounded">
                      <span class="font-medium">{technique}</span>
                      <span class="text-neutral-500">
                        {data.therapeuticApproaches.filter(ta => ta?.selectedTechnique?.name === technique).length}x
                      </span>
                    </div>
                  {/each}
                </div>
              </div>
            </div>
          </div>
        {/if}
      {:else}
        <div class="text-center py-8">
          <p class="text-neutral-600 dark:text-neutral-400">No analysis data available for this mode</p>
        </div>
      {/if}
    </div>
  {/each}
</div>
