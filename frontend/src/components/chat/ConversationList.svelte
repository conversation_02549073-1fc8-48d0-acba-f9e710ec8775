<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { Conversation } from '../../types/index.js';

  export let conversations: Conversation[] = [];
  export let selectedConversationId: string | null = null;
  export let loading = false;
  export let error: string | null = null;
  export let currentPage = 1;
  export let totalPages = 1;
  export let totalConversations = 0;

  const dispatch = createEventDispatcher();

  function formatTimestamp(timestamp: string): string {
    if (!timestamp) return 'Invalid Date';

    try {
      const date = new Date(timestamp);
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }
      return date.toLocaleString( 'th', { hour: 'numeric', minute: 'numeric', second: 'numeric' } );
    } catch (error) {
      console.warn('Error formatting timestamp:', timestamp, error);
      return 'Invalid Date';
    }
  }

  function getStatusColor(status: string): string {
    switch (status) {
      case 'completed': return 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300';
      case 'active': return 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300';
      case 'paused': return 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300';
      default: return 'bg-gray-100 dark:bg-gray-900/50 text-gray-700 dark:text-gray-300';
    }
  }

  function selectConversation(conversationId: string) {
    dispatch('select', { conversationId });
  }

  function nextPage() {
    if (currentPage < totalPages) {
      dispatch('pageChange', { page: currentPage + 1 });
    }
  }

  function prevPage() {
    if (currentPage > 1) {
      dispatch('pageChange', { page: currentPage - 1 });
    }
  }

  function goToPage(page: number) {
    if (page >= 1 && page <= totalPages) {
      dispatch('pageChange', { page });
    }
  }

  function retryLoad() {
    dispatch('retry');
  }

  // Calculate pagination display
  $: canGoNext = currentPage < totalPages;
  $: canGoPrev = currentPage > 1;
  $: startItem = (currentPage - 1) * 10 + 1;
  $: endItem = Math.min(currentPage * 10, totalConversations);
  
  // Generate page numbers for pagination
  $: pageNumbers = (() => {
    const pages = [];
    const maxVisible = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    // Adjust start if we're near the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  })();
</script>

<div class="flex flex-col h-full">
  <!-- Header -->
  <div class="p-4 border-b border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800">
    <div class="flex items-center justify-between">
      <h2 class="text-lg font-medium text-neutral-900 dark:text-neutral-100">
        Conversations
      </h2>
      <div class="text-sm text-neutral-500 dark:text-neutral-400">
        {#if totalConversations > 0}
          {startItem}-{endItem} of {totalConversations}
        {:else}
          No conversations
        {/if}
      </div>
    </div>
  </div>

  <!-- Conversation List -->
  <div class="flex-1 overflow-y-auto">
    {#if loading}
      <div class="p-4 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
        <p class="mt-2 text-neutral-600 dark:text-neutral-400">Loading conversations...</p>
      </div>
    {:else if error}
      <div class="p-4 text-center">
        <div class="text-red-600 dark:text-red-400 mb-2">
          <svg class="w-8 h-8 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p class="text-red-600 dark:text-red-400 mb-3">{error}</p>
        <button
          on:click={retryLoad}
          class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          Retry
        </button>
      </div>
    {:else if conversations.length === 0}
      <div class="p-8 text-center">
        <div class="text-neutral-400 dark:text-neutral-500 text-4xl mb-3">💬</div>
        <h3 class="text-lg font-medium text-neutral-600 dark:text-neutral-400 mb-2">
          No conversations found
        </h3>
        <p class="text-neutral-500 dark:text-neutral-500">
          Try adjusting your filters or start a new conversation.
        </p>
      </div>
    {:else}
      {#each conversations as conversation}
        <div
          class="p-4 border-b border-neutral-200 dark:border-neutral-700 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors {selectedConversationId === conversation.id ? 'bg-primary-50 dark:bg-primary-900/20 border-l-4 border-l-primary-500' : ''}"
          on:click={() => selectConversation(conversation.id)}
          on:keydown={(e) => e.key === 'Enter' && selectConversation(conversation.id)}
          tabindex="0"
          role="button"
          aria-label="Select conversation {conversation.id.slice(-8)}"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1 min-w-0">
              <!-- Conversation Header -->
              <div class="flex items-center space-x-2 mb-2">
                <span class="text-sm font-medium text-neutral-900 dark:text-neutral-100 truncate">
                  Session {conversation.id.slice(-8)}
                </span>
                <!-- <span class="px-2 py-1 text-xs rounded-full {getStatusColor(conversation.status)} flex-shrink-0">
                  {conversation.status}
                </span> -->
                <!-- {#if conversation.therapist_mode === 'multi-therapist'}
                  <span class="px-2 py-1 text-xs rounded-full bg-purple-100 dark:bg-purple-900/50 text-purple-700 dark:text-purple-300 flex-shrink-0">
                    Multi
                  </span>
                {/if} -->
              </div>
              
              <!-- Conversation Details -->
              <div class="text-xs text-neutral-600 dark:text-neutral-400 space-y-1">
                <div class="flex items-center space-x-4">
                  <div class="flex items-center space-x-1">
                    <!-- <span class="font-medium">Persona:</span> -->
                    <span class="truncate max-w-24">
                      {conversation.config.patient.persona.name || conversation.patient_persona_id || 'Unknown'}
                    </span>
                  </div>
                  <!-- <div class="flex items-center space-x-1">
                    <span class="font-medium">Messages:</span>
                    <span>{conversation.messages?.length || 0}</span>
                  </div> -->
                </div>
                
                <div class="flex items-center space-x-1">
                  <!-- <span class="font-medium">Started:</span> -->
                  <span class="truncate">
                    {formatTimestamp(conversation.created_at || conversation.startTime)}
                  </span>
                </div>

                {#if conversation.endTime}
                  <div class="flex items-center space-x-1">
                    <span class="font-medium">Duration:</span>
                    <span>
                      {Math.round((new Date(conversation.endTime).getTime() - new Date(conversation.startTime).getTime()) / 60000)} min
                    </span>
                  </div>
                {/if}
              </div>

              <!-- Progress Indicators -->
              {#if conversation.messages && conversation.messages.length > 0}
                {@const lastTherapistMessage = conversation.messages.filter(m => m.sender === 'therapist').pop()}
                {#if lastTherapistMessage?.metadata?.patientAnalysis}
                  <div class="mt-2 flex items-center space-x-2">
                    <div class="flex items-center space-x-1">
                      <div class="w-2 h-2 rounded-full {
                        lastTherapistMessage.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-500' :
                        lastTherapistMessage.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-500' :
                        'bg-gray-500'
                      }"></div>
                      <span class="text-xs text-neutral-500 dark:text-neutral-400">
                        {lastTherapistMessage.metadata.patientAnalysis.sentiment}
                      </span>
                    </div>
                    <div class="flex items-center space-x-1">
                      <div class="w-2 h-2 rounded-full {
                        lastTherapistMessage.metadata.patientAnalysis.engagementLevel === 'high' ? 'bg-green-500' :
                        lastTherapistMessage.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-yellow-500' :
                        'bg-red-500'
                      }"></div>
                      <span class="text-xs text-neutral-500 dark:text-neutral-400">
                        {lastTherapistMessage.metadata.patientAnalysis.engagementLevel} engagement
                      </span>
                    </div>
                  </div>
                {/if}
              {/if}
            </div>
            
            <!-- Selection Indicator -->
            {#if selectedConversationId === conversation.id}
              <div class="flex-shrink-0 ml-2">
                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            {/if}
          </div>
        </div>
      {/each}
    {/if}
  </div>

  <!-- Pagination -->
  {#if totalPages > 1}
    <div class="p-4 border-t border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-900">
      <div class="flex items-center justify-between">
        <!-- Previous Button -->
        <button
          on:click={prevPage}
          disabled={!canGoPrev}
          class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
        >
          Previous
        </button>
        
        <!-- Page Numbers -->
        <div class="flex items-center space-x-1">
          {#if pageNumbers[0] > 1}
            <button
              on:click={() => goToPage(1)}
              class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
            >
              1
            </button>
            {#if pageNumbers[0] > 2}
              <span class="px-2 text-neutral-500 dark:text-neutral-400">...</span>
            {/if}
          {/if}
          
          {#each pageNumbers as page}
            <button
              on:click={() => goToPage(page)}
              class="px-3 py-2 text-sm border rounded-lg transition-colors {
                page === currentPage 
                  ? 'bg-primary-600 text-white border-primary-600' 
                  : 'border-neutral-300 dark:border-neutral-600 hover:bg-neutral-100 dark:hover:bg-neutral-800'
              }"
            >
              {page}
            </button>
          {/each}
          
          {#if pageNumbers[pageNumbers.length - 1] < totalPages}
            {#if pageNumbers[pageNumbers.length - 1] < totalPages - 1}
              <span class="px-2 text-neutral-500 dark:text-neutral-400">...</span>
            {/if}
            <button
              on:click={() => goToPage(totalPages)}
              class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
            >
              {totalPages}
            </button>
          {/if}
        </div>
        
        <!-- Next Button -->
        <button
          on:click={nextPage}
          disabled={!canGoNext}
          class="px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
        >
          Next
        </button>
      </div>
    </div>
  {/if}
</div>
