<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props

  export let step: any;
  export let index: number;
  export let totalSteps: number;
  export let isSelected: boolean = false;
  export let compact: boolean = false;

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Computed properties
  $: isFirstStep = index === 0;
  $: isLastStep = index === totalSteps - 1;
  $: hasVariables = step.variables && Object.keys(step.variables).length > 0;
  $: outputPreview = getOutputPreview(step.output);

  function getOutputPreview(output: string): string {
    if (!output) return 'No output';
    if (output.length <= 150) return output;
    return output.substring(0, 150) + '...';
  }

  function handleClick() {
    dispatch('click');
  }

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
      console.log('Copied to clipboard');
    });
  }

  // Get step color based on index
  function getStepColor(index: number): string {
    const colors = [
      'bg-blue-500',
      'bg-green-500', 
      'bg-yellow-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500'
    ];
    return colors[index % colors.length];
  }
</script>

<div class="relative">
  <!-- Connection Line (not for first step) -->
  {#if !isFirstStep}
    <div class="absolute left-6 -top-4 w-0.5 h-4 bg-neutral-300 dark:bg-neutral-600"></div>
  {/if}

  <!-- Step Container -->
  <div 
    class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm hover:shadow-md theme-transition cursor-pointer {
      isSelected ? 'ring-2 ring-primary-500 border-primary-500' : ''
    }"
    on:click={handleClick}
    on:keydown={(e) => e.key === 'Enter' && handleClick()}
    role="button"
    tabindex="0"
  >
    <!-- Step Header -->
    <div class="p-4 {isSelected ? 'pb-2' : ''}">
      <div class="flex items-start space-x-4">
        <!-- Step Number -->
        <div class="flex-shrink-0">
          <div class="w-12 h-12 rounded-full {getStepColor(index)} flex items-center justify-center text-white font-bold">
            {index + 1}
          </div>
        </div>

        <!-- Step Content -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between">
            <h4 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
              {step.name}
            </h4>
            
            <!-- Expand/Collapse Icon -->
            <svg 
              class="w-5 h-5 text-neutral-400 transform transition-transform {isSelected ? 'rotate-180' : ''}"
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>

          <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
            {step.description}
          </p>

          {#if !compact}
            <!-- Output Preview -->
            <div class="mt-3 p-3 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
              <div class="flex items-center justify-between mb-2">
                <span class="text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wide">
                  Output Preview
                </span>
                {#if step.output}
                  <button
                    on:click|stopPropagation={() => copyToClipboard(step.output)}
                    class="text-xs text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 theme-transition"
                    title="Copy full output"
                  >
                    Copy
                  </button>
                {/if}
              </div>
              <p class="text-sm text-neutral-700 dark:text-neutral-300 font-mono leading-relaxed">
                {outputPreview}
              </p>
            </div>
          {/if}

          <!-- Variables Indicator -->
          {#if hasVariables && !compact}
            <div class="mt-2 flex items-center space-x-2">
              <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
              </svg>
              <span class="text-xs text-blue-600 dark:text-blue-400">
                {Object.keys(step.variables).length} variables
              </span>
            </div>
          {/if}
        </div>
      </div>
    </div>

    <!-- Expanded Content -->
    {#if isSelected && !compact}
      <div class="px-4 pb-4 border-t border-neutral-200 dark:border-neutral-700 pt-4">
        <!-- Template Content -->
        {#if step.template && step.template !== step.output}
          <div class="mb-4">
            <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Template
            </h5>
            <div class="bg-neutral-100 dark:bg-neutral-700 rounded-lg p-3 max-h-40 overflow-y-auto prompt-content">
              <pre class="text-sm text-neutral-700 dark:text-neutral-300 whitespace-pre-wrap font-mono">{step.template}</pre>
            </div>
          </div>
        {/if}

        <!-- Variables -->
        {#if hasVariables}
          <div class="mb-4">
            <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
              </svg>
              Variables ({Object.keys(step.variables).length})
            </h5>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
              {#each Object.entries(step.variables) as [key, value]}
                <div class="bg-neutral-100 dark:bg-neutral-700 rounded p-2">
                  <div class="text-xs font-medium text-blue-600 dark:text-blue-400">{{key}}</div>
                  <div class="text-sm text-neutral-700 dark:text-neutral-300 mt-1 truncate" title={String(value)}>
                    {String(value)}
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}

        <!-- Full Output -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <h5 class="text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              Full Output ({step.output?.length || 0} characters)
            </h5>
            <button
              on:click|stopPropagation={() => copyToClipboard(step.output)}
              class="text-xs px-2 py-1 bg-neutral-200 dark:bg-neutral-600 text-neutral-700 dark:text-neutral-300 rounded hover:bg-neutral-300 dark:hover:bg-neutral-500 theme-transition"
            >
              Copy
            </button>
          </div>
          <div class="bg-neutral-100 dark:bg-neutral-700 rounded-lg p-3 max-h-60 overflow-y-auto prompt-content">
            <pre class="text-sm text-neutral-700 dark:text-neutral-300 whitespace-pre-wrap font-mono">{step.output || 'No output available'}</pre>
          </div>
        </div>
      </div>
    {/if}
  </div>

  <!-- Connection Line (not for last step) -->
  {#if !isLastStep}
    <div class="absolute left-6 bottom-0 w-0.5 h-4 bg-neutral-300 dark:bg-neutral-600 translate-y-full"></div>
  {/if}
</div>

<style>
  /* Smooth transitions */
  .theme-transition {
    transition: all 0.2s ease-in-out;
  }

  /* Custom scrollbar for prompt content */
  :global(.prompt-content) {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  :global(.prompt-content::-webkit-scrollbar) {
    width: 6px;
  }

  :global(.prompt-content::-webkit-scrollbar-track) {
    background: transparent;
  }

  :global(.prompt-content::-webkit-scrollbar-thumb) {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  :global(.dark .prompt-content) {
    scrollbar-color: rgb(75 85 99) transparent;
  }

  :global(.dark .prompt-content::-webkit-scrollbar-thumb) {
    background-color: rgb(75 85 99);
  }
</style>
