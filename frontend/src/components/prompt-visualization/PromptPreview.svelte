<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let prompt: string;
  export let title: string = 'Prompt Preview';

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Component state
  let searchTerm: string = '';
  let showLineNumbers: boolean = true;
  let wordWrap: boolean = true;

  // Computed properties
  $: lines = prompt.split('\n');
  $: filteredLines = getFilteredLines();
  $: stats = getPromptStats();
  $: highlightedPrompt = getHighlightedPrompt();

  function getFilteredLines() {
    if (!searchTerm) return lines.map((line, index) => ({ line, index, visible: true }));
    
    const searchLower = searchTerm.toLowerCase();
    return lines.map((line, index) => ({
      line,
      index,
      visible: line.toLowerCase().includes(searchLower)
    }));
  }

  function getPromptStats() {
    return {
      totalLines: lines.length,
      totalCharacters: prompt.length,
      totalWords: prompt.split(/\s+/).filter(word => word.length > 0).length,
      visibleLines: filteredLines.filter(l => l.visible).length
    };
  }

  function getHighlightedPrompt(): string {
    if (!searchTerm) return prompt;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return prompt.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>');
  }

  function handleClose() {
    dispatch('close');
  }

  function copyPrompt() {
    navigator.clipboard.writeText(prompt).then(() => {
      console.log('Prompt copied to clipboard');
    });
  }

  function downloadPrompt() {
    const blob = new Blob([prompt], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  function toggleLineNumbers() {
    showLineNumbers = !showLineNumbers;
  }

  function toggleWordWrap() {
    wordWrap = !wordWrap;
  }
</script>

<!-- Modal Overlay -->
<div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
  <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-neutral-700">
      <div>
        <h3 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
          {title}
        </h3>
        <div class="flex items-center space-x-4 mt-2 text-sm text-neutral-600 dark:text-neutral-400">
          <span>{stats.totalLines} lines</span>
          <span>{stats.totalWords} words</span>
          <span>{stats.totalCharacters} characters</span>
          {#if searchTerm}
            <span class="text-primary-600 dark:text-primary-400">
              {stats.visibleLines} visible lines
            </span>
          {/if}
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <!-- View Options -->
        <button
          on:click={toggleLineNumbers}
          class="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition {
            showLineNumbers ? 'bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-200' : ''
          }"
          title="Toggle line numbers"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
          </svg>
        </button>
        <button
          on:click={toggleWordWrap}
          class="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition {
            wordWrap ? 'bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-200' : ''
          }"
          title="Toggle word wrap"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
          </svg>
        </button>
        
        <!-- Actions -->
        <button
          on:click={copyPrompt}
          class="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition"
          title="Copy prompt"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
        </button>
        <button
          on:click={downloadPrompt}
          class="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition"
          title="Download prompt"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </button>
        <button
          on:click={handleClose}
          class="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Search -->
    <div class="p-4 border-b border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-700">
      <div class="relative">
        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <input
          type="text"
          bind:value={searchTerm}
          placeholder="Search within prompt..."
          class="w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 theme-transition"
        />
        {#if searchTerm}
          <button
            on:click={() => searchTerm = ''}
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        {/if}
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-auto bg-neutral-900 text-neutral-100">
        <div class="p-4">
          {#if searchTerm}
            <!-- Filtered view with highlighting -->
            <div class="space-y-1">
              {#each filteredLines as { line, index, visible }}
                {#if visible}
                  <div class="flex {showLineNumbers ? 'space-x-4' : ''}">
                    {#if showLineNumbers}
                      <div class="text-neutral-500 text-sm font-mono w-12 text-right flex-shrink-0 select-none">
                        {index + 1}
                      </div>
                    {/if}
                    <div class="font-mono text-sm {wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'} flex-1">
                      {@html line.replace(new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi'), '<mark class="bg-yellow-400 text-black px-1 rounded">$1</mark>')}
                    </div>
                  </div>
                {/if}
              {/each}
            </div>
          {:else}
            <!-- Full prompt view -->
            <div class="flex {showLineNumbers ? 'space-x-4' : ''}">
              {#if showLineNumbers}
                <div class="text-neutral-500 text-sm font-mono flex-shrink-0 select-none">
                  {#each lines as _, index}
                    <div class="text-right w-12 leading-relaxed">{index + 1}</div>
                  {/each}
                </div>
              {/if}
              <pre class="font-mono text-sm {wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'} flex-1 leading-relaxed">{prompt}</pre>
            </div>
          {/if}

          {#if searchTerm && stats.visibleLines === 0}
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-neutral-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              <p class="text-neutral-400">
                No matches found for "{searchTerm}"
              </p>
            </div>
          {/if}
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="p-4 border-t border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-700">
      <div class="flex items-center justify-between">
        <div class="text-sm text-neutral-600 dark:text-neutral-400">
          {#if searchTerm}
            Showing {stats.visibleLines} of {stats.totalLines} lines
          {:else}
            {stats.totalLines} lines, {stats.totalWords} words, {stats.totalCharacters} characters
          {/if}
        </div>
        <div class="flex space-x-2">
          <button
            on:click={copyPrompt}
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 theme-transition text-sm"
          >
            Copy All
          </button>
          <button
            on:click={handleClose}
            class="px-4 py-2 bg-neutral-600 text-white rounded-lg hover:bg-neutral-700 theme-transition text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .theme-transition {
    transition: all 0.2s ease-in-out;
  }

  /* Custom scrollbar for code area */
  .overflow-auto {
    scrollbar-width: thin;
    scrollbar-color: rgb(75 85 99) rgb(23 23 23);
  }

  .overflow-auto::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .overflow-auto::-webkit-scrollbar-track {
    background: rgb(23 23 23);
  }

  .overflow-auto::-webkit-scrollbar-thumb {
    background-color: rgb(75 85 99);
    border-radius: 4px;
  }

  .overflow-auto::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
</style>
