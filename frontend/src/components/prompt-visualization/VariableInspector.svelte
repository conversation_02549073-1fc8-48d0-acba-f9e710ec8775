<script lang="ts">
  import { createEventDispatcher } from 'svelte';

  // Props
  export let variables: any;
  export let personaName: string;

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Component state
  let selectedCategory: string | null = null;
  let searchTerm: string = '';

  // Computed properties
  $: categories = Object.keys(variables || {});
  $: filteredCategories = getFilteredCategories();

  function getFilteredCategories() {
    if (!variables || !searchTerm) return categories;
    
    return categories.filter(category => {
      const categoryVars = variables[category];
      return Object.keys(categoryVars).some(key => 
        key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        String(categoryVars[key].value).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
  }

  function handleClose() {
    dispatch('close');
  }

  function handleCategoryClick(category: string) {
    selectedCategory = selectedCategory === category ? null : category;
  }

  function copyVariable(value: any) {
    const textValue = Array.isArray(value) ? value.join(', ') : String(value);
    navigator.clipboard.writeText(textValue).then(() => {
      console.log('Variable copied to clipboard');
    });
  }

  function formatValue(value: any): string {
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    return String(value);
  }

  function getTypeColor(type: string): string {
    switch (type) {
      case 'string': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'number': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'array': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  }

  function getCategoryIcon(category: string): string {
    switch (category) {
      case 'basic_info':
        return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>`;
      case 'psychological_profile':
        return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>`;
      case 'emotional_state':
        return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>`;
      case 'behavioral_patterns':
        return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>`;
      case 'communication_style':
        return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>`;
      default:
        return `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>`;
    }
  }
</script>

<!-- Modal Overlay -->
<div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
  <div class="bg-white dark:bg-neutral-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-neutral-700">
      <div>
        <h3 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
          Template Variables Inspector
        </h3>
        <p class="text-sm text-neutral-600 dark:text-neutral-400 mt-1">
          {personaName} - All template variables and their values
        </p>
      </div>
      <button
        on:click={handleClose}
        class="p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Search -->
    <div class="p-6 border-b border-neutral-200 dark:border-neutral-700">
      <div class="relative">
        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <input
          type="text"
          bind:value={searchTerm}
          placeholder="Search variables..."
          class="w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 theme-transition"
        />
      </div>
    </div>

    <!-- Content -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-y-auto p-6">
        <div class="space-y-6">
          {#each filteredCategories as category}
            {@const categoryData = variables[category]}
            {@const categoryVars = Object.entries(categoryData)}
            
            <div class="border border-neutral-200 dark:border-neutral-700 rounded-lg">
              <!-- Category Header -->
              <button
                on:click={() => handleCategoryClick(category)}
                class="w-full p-4 text-left hover:bg-neutral-50 dark:hover:bg-neutral-700 theme-transition rounded-t-lg {
                  selectedCategory === category ? 'bg-neutral-50 dark:bg-neutral-700' : ''
                }"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-neutral-500 dark:text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      {@html getCategoryIcon(category)}
                    </svg>
                    <h4 class="text-lg font-medium text-neutral-900 dark:text-neutral-100">
                      {category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </h4>
                    <span class="text-sm text-neutral-500 dark:text-neutral-400">
                      ({categoryVars.length} variables)
                    </span>
                  </div>
                  <svg 
                    class="w-5 h-5 text-neutral-400 transform transition-transform {
                      selectedCategory === category ? 'rotate-180' : ''
                    }"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </button>

              <!-- Category Variables -->
              {#if selectedCategory === category}
                <div class="border-t border-neutral-200 dark:border-neutral-700 p-4 bg-neutral-50 dark:bg-neutral-700">
                  <div class="space-y-3">
                    {#each categoryVars as [varName, varData]}
                      <div class="bg-white dark:bg-neutral-800 rounded-lg p-4 border border-neutral-200 dark:border-neutral-600">
                        <div class="flex items-start justify-between">
                          <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                              <code class="text-sm font-mono bg-neutral-100 dark:bg-neutral-700 px-2 py-1 rounded text-primary-600 dark:text-primary-400">
                                {varName}
                              </code>
                              <span class="text-xs px-2 py-1 rounded-full {getTypeColor(varData.type)}">
                                {varData.type}
                              </span>
                            </div>
                            <p class="text-sm text-neutral-600 dark:text-neutral-400 mb-3">
                              {varData.description}
                            </p>
                            <div class="bg-neutral-100 dark:bg-neutral-700 rounded p-3">
                              <div class="text-sm text-neutral-700 dark:text-neutral-300">
                                {formatValue(varData.value)}
                              </div>
                            </div>
                          </div>
                          <button
                            on:click={() => copyVariable(varData.value)}
                            class="ml-4 p-2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-200 rounded hover:bg-neutral-100 dark:hover:bg-neutral-600 theme-transition"
                            title="Copy value"
                          >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          {/each}

          {#if filteredCategories.length === 0}
            <div class="text-center py-8">
              <svg class="w-12 h-12 text-neutral-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              <p class="text-neutral-500 dark:text-neutral-400">
                No variables found matching "{searchTerm}"
              </p>
            </div>
          {/if}
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="p-6 border-t border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-700">
      <div class="flex items-center justify-between text-sm text-neutral-600 dark:text-neutral-400">
        <div>
          Total: {categories.length} categories, {Object.values(variables || {}).reduce((sum, cat) => sum + Object.keys(cat).length, 0)} variables
        </div>
        <button
          on:click={handleClose}
          class="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 theme-transition"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .theme-transition {
    transition: all 0.2s ease-in-out;
  }
</style>
