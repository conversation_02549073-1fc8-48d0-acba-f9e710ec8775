<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import PipelineStep from './PipelineStep.svelte';
  import VariableInspector from './VariableInspector.svelte';
  import PromptPreview from './PromptPreview.svelte';

  // Props
  export let type: 'patient' | 'therapist';
  export let pipeline: any;
  export let personas: any[] = [];
  export let selectedPersonaId: string = '';
  export let therapeuticApproaches: string[] = [];
  export let availableTechniques: string[] = [];
  export let selectedTherapeuticApproach: string = '';
  export let selectedTechnique: string = '';
  export let compact: boolean = false;

  // Event dispatcher
  const dispatch = createEventDispatcher();

  // Component state
  let selectedStepId: string | null = null;
  let showVariables = false;
  let showFinalPrompt = false;

  // Computed properties
  $: pipelineSteps = pipeline ? Object.entries(pipeline.pipeline || {}) : [];
  $: selectedPersona = personas.find(p => p.id === selectedPersonaId);
  $: finalPrompt = getFinalPrompt();

  function getFinalPrompt(): string {
    if (!pipeline?.pipeline) return '';
    
    const steps = Object.values(pipeline.pipeline);
    const finalStep = steps[steps.length - 1] as any;
    return finalStep?.output || '';
  }

  function handleStepClick(stepId: string) {
    selectedStepId = selectedStepId === stepId ? null : stepId;
  }

  function handlePersonaChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    dispatch('personaChange', target.value);
  }

  function handleApproachChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    dispatch('approachChange', target.value);
  }

  function handleTechniqueChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    dispatch('techniqueChange', target.value);
  }

  function toggleVariables() {
    showVariables = !showVariables;
  }

  function toggleFinalPrompt() {
    showFinalPrompt = !showFinalPrompt;
  }
</script>

<div class="space-y-6">
  <!-- Controls -->
  <div class="bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-6 theme-transition">
    <div class="flex flex-wrap gap-4 items-center">
      {#if type === 'patient' && personas.length > 0}
        <div class="flex-1 min-w-64">
          <label for="persona-select" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
            Patient Persona
          </label>
          <select
            id="persona-select"
            bind:value={selectedPersonaId}
            on:change={handlePersonaChange}
            class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 theme-transition"
          >
            {#each personas as persona}
              <option value={persona.id}>{persona.name} ({persona.age} years old)</option>
            {/each}
          </select>
        </div>
      {:else if type === 'therapist'}
        <div class="flex-1 min-w-48">
          <label for="approach-select" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
            Therapeutic Approach
          </label>
          <select
            id="approach-select"
            bind:value={selectedTherapeuticApproach}
            on:change={handleApproachChange}
            class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 theme-transition"
          >
            {#each therapeuticApproaches as approach}
              <option value={approach}>{approach.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</option>
            {/each}
          </select>
        </div>
        <div class="flex-1 min-w-48">
          <label for="technique-select" class="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
            Specific Technique
          </label>
          <select
            id="technique-select"
            bind:value={selectedTechnique}
            on:change={handleTechniqueChange}
            class="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 theme-transition"
          >
            {#each availableTechniques as technique}
              <option value={technique}>{technique.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</option>
            {/each}
          </select>
        </div>
      {/if}

      <!-- Action Buttons -->
      <div class="flex space-x-2">
        {#if type === 'patient'}
          <button
            on:click={toggleVariables}
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 theme-transition text-sm"
          >
            {showVariables ? 'Hide' : 'Show'} Variables
          </button>
        {/if}
        <button
          on:click={toggleFinalPrompt}
          class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 theme-transition text-sm"
        >
          {showFinalPrompt ? 'Hide' : 'Show'} Final Prompt
        </button>
      </div>
    </div>

    <!-- Selected Persona Info -->
    {#if type === 'patient' && selectedPersona && !compact}
      <div class="mt-4 p-4 bg-neutral-50 dark:bg-neutral-700 rounded-lg">
        <h4 class="font-medium text-neutral-900 dark:text-neutral-100 mb-2">
          {selectedPersona.name} - {selectedPersona.age} years old
        </h4>
        <p class="text-sm text-neutral-600 dark:text-neutral-400">
          {selectedPersona.background}
        </p>
      </div>
    {/if}
  </div>

  <!-- Pipeline Steps -->
  <div class="space-y-4">
    <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100">
      {type === 'patient' ? 'Patient Persona' : 'Therapist AI'} Construction Pipeline
    </h3>
    
    {#each pipelineSteps as [stepId, step], index}
      <PipelineStep
        {stepId}
        {step}
        {index}
        totalSteps={pipelineSteps.length}
        isSelected={selectedStepId === stepId}
        {compact}
        on:click={() => handleStepClick(stepId)}
      />
    {/each}
  </div>

  <!-- Variable Inspector -->
  {#if showVariables && type === 'patient' && pipeline.templateVariables}
    <VariableInspector
      variables={pipeline.templateVariables}
      personaName={selectedPersona?.name || 'Unknown'}
      on:close={toggleVariables}
    />
  {/if}

  <!-- Final Prompt Preview -->
  {#if showFinalPrompt && finalPrompt}
    <PromptPreview
      prompt={finalPrompt}
      title={`Final ${type === 'patient' ? 'Patient Persona' : 'Therapist AI'} Prompt`}
      on:close={toggleFinalPrompt}
    />
  {/if}

  <!-- Pipeline Metadata -->
  {#if pipeline.metadata && !compact}
    <div class="bg-neutral-50 dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 p-4">
      <h4 class="font-medium text-neutral-900 dark:text-neutral-100 mb-3">Pipeline Metadata</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        {#if pipeline.metadata.totalSteps}
          <div>
            <span class="text-neutral-500 dark:text-neutral-400">Total Steps:</span>
            <span class="ml-2 font-medium text-neutral-900 dark:text-neutral-100">{pipeline.metadata.totalSteps}</span>
          </div>
        {/if}
        {#if pipeline.metadata.totalVariables}
          <div>
            <span class="text-neutral-500 dark:text-neutral-400">Variables:</span>
            <span class="ml-2 font-medium text-neutral-900 dark:text-neutral-100">{pipeline.metadata.totalVariables}</span>
          </div>
        {/if}
        {#if pipeline.metadata.templateLength}
          <div>
            <span class="text-neutral-500 dark:text-neutral-400">Template Size:</span>
            <span class="ml-2 font-medium text-neutral-900 dark:text-neutral-100">{pipeline.metadata.templateLength} chars</span>
          </div>
        {/if}
        {#if pipeline.metadata.finalPromptLength}
          <div>
            <span class="text-neutral-500 dark:text-neutral-400">Final Size:</span>
            <span class="ml-2 font-medium text-neutral-900 dark:text-neutral-100">{pipeline.metadata.finalPromptLength} chars</span>
          </div>
        {/if}
      </div>
    </div>
  {/if}
</div>

<style>
  /* Smooth transitions for interactive elements */
  button {
    transition: all 0.2s ease-in-out;
  }

  select {
    transition: all 0.2s ease-in-out;
  }

  /* Focus styles for accessibility */
  button:focus,
  select:focus {
    outline: none;
  }
</style>
