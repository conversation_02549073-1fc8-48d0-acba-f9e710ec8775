<script lang="ts">
  import { onMount } from 'svelte';
  import { themeStore } from '$stores/theme';
  import type { Theme } from '$stores/theme';

  // Icons for light and dark modes
  const SunIcon = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
  </svg>`;

  const MoonIcon = `<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
  </svg>`;

  // Component props
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let showLabel = false;
  export let variant: 'button' | 'switch' = 'switch';

  // Reactive theme state
  $: currentTheme = $themeStore.current;
  $: isDark = currentTheme === 'dark';

  // Size classes
  const sizeClasses = {
    sm: 'w-10 h-6',
    md: 'w-12 h-7',
    lg: 'w-14 h-8'
  };

  const thumbSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  // Initialize theme store on mount
  onMount(() => {
    themeStore.init();
  });

  // Handle theme toggle
  function handleToggle() {
    themeStore.toggle();
  }

  // Handle keyboard events
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggle();
    }
  }
</script>

{#if variant === 'switch'}
  <!-- Switch Toggle -->
  <div class="flex items-center space-x-3">
    {#if showLabel}
      <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">
        {isDark ? 'Dark' : 'Light'} Mode
      </span>
    {/if}
    
    <button
      type="button"
      role="switch"
      aria-checked={isDark}
      aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
      class="relative inline-flex {sizeClasses[size]} items-center rounded-full border-2 border-transparent bg-neutral-200 dark:bg-neutral-700 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 {isDark ? 'bg-primary-600 dark:bg-primary-600' : ''}"
      on:click={handleToggle}
      on:keydown={handleKeydown}
    >
      <!-- Switch thumb -->
      <span
        class="pointer-events-none inline-block {thumbSizeClasses[size]} transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out {isDark ? 'translate-x-5' : 'translate-x-1'}"
        class:translate-x-6={size === 'lg' && isDark}
        class:translate-x-5={size === 'md' && isDark}
        class:translate-x-4={size === 'sm' && isDark}
      >
        <!-- Icon inside thumb -->
        <span class="absolute inset-0 flex items-center justify-center text-neutral-600 transition-opacity duration-200">
          {#if isDark}
            {@html MoonIcon.replace('w-4 h-4', iconSizeClasses[size])}
          {:else}
            {@html SunIcon.replace('w-4 h-4', iconSizeClasses[size])}
          {/if}
        </span>
      </span>
    </button>
  </div>
{:else}
  <!-- Button Toggle -->
  <button
    type="button"
    aria-label={isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    class="inline-flex items-center justify-center rounded-lg p-2 text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 hover:text-neutral-900 dark:hover:text-neutral-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 transition-all duration-200"
    on:click={handleToggle}
    on:keydown={handleKeydown}
  >
    <span class="sr-only">
      {isDark ? 'Switch to light mode' : 'Switch to dark mode'}
    </span>
    
    <!-- Icon with smooth transition -->
    <div class="relative {iconSizeClasses[size]}">
      <!-- Sun icon -->
      <div 
        class="absolute inset-0 transition-all duration-300 {isDark ? 'opacity-0 rotate-90 scale-0' : 'opacity-100 rotate-0 scale-100'}"
      >
        {@html SunIcon.replace('w-4 h-4', iconSizeClasses[size])}
      </div>
      
      <!-- Moon icon -->
      <div 
        class="absolute inset-0 transition-all duration-300 {isDark ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-0'}"
      >
        {@html MoonIcon.replace('w-4 h-4', iconSizeClasses[size])}
      </div>
    </div>
    
    {#if showLabel}
      <span class="ml-2 text-sm font-medium">
        {isDark ? 'Dark' : 'Light'}
      </span>
    {/if}
  </button>
{/if}

<style>
  /* Ensure smooth transitions */
  button {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
  }
  
  /* Custom focus styles for better accessibility */
  button:focus-visible {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
</style>
