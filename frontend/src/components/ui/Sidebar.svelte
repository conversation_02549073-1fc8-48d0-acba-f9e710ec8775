<script lang="ts">
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import ThemeToggle from './ThemeToggle.svelte';

  // Navigation items
  const navigationItems = [
    {
      name: 'Cha<PERSON>',
      href: '/',
      icon: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>`
    },
    {
      name: 'History',
      href: '/history',
      icon: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>`
    },
    {
      name: 'Prompt Pipeline',
      href: '/prompt-visualization',
      icon: `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
      </svg>`
    }
  ];

  // Reactive current path
  $: currentPath = $page.url.pathname;

  // Check if a navigation item is active
  $: isActive = (href: string): boolean => {
    if (href === '/') {
      return currentPath === '/';
    }
    return currentPath.startsWith(href);
  };

  // Mobile sidebar state
  let isMobileMenuOpen = false;

  function toggleMobileMenu() {
    isMobileMenuOpen = !isMobileMenuOpen;
    // Prevent body scroll when mobile menu is open
    if (typeof document !== 'undefined') {
      document.body.style.overflow = isMobileMenuOpen ? 'hidden' : '';
    }
  }

  function closeMobileMenu() {
    isMobileMenuOpen = false;
    // Restore body scroll
    if (typeof document !== 'undefined') {
      document.body.style.overflow = '';
    }
  }

  // Close mobile menu on route change
  $: if ($page.url.pathname) {
    closeMobileMenu();
  }

  // Handle keyboard navigation
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && isMobileMenuOpen) {
      closeMobileMenu();
    }
  }

  // Set up keyboard event listener
  onMount(() => {
    const handleGlobalKeydown = (event: KeyboardEvent) => handleKeydown(event);
    document.addEventListener('keydown', handleGlobalKeydown);

    return () => {
      document.removeEventListener('keydown', handleGlobalKeydown);
      // Clean up body overflow on unmount
      document.body.style.overflow = '';
    };
  });
</script>

<!-- Mobile menu button -->
<button
  class="lg:hidden fixed top-4 left-4 z-50 p-2 rounded-lg bg-white dark:bg-neutral-800 shadow-lg border border-neutral-200 dark:border-neutral-700 text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100 hover:shadow-xl"
  on:click={toggleMobileMenu}
  aria-label="Toggle navigation menu"
>
  <svg class="w-6 h-6 transition-transform duration-200 {isMobileMenuOpen ? 'rotate-90' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
  </svg>
</button>

<!-- Mobile overlay -->
{#if isMobileMenuOpen}
  <div 
    class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
    on:click={closeMobileMenu}
    on:keydown={(e) => e.key === 'Escape' && closeMobileMenu()}
    tabindex="-1"
    role="button"
    aria-label="Close navigation menu"
  ></div>
{/if}

<!-- Sidebar -->
<aside 
  class="fixed left-0 top-0 h-full w-64 bg-white dark:bg-neutral-800 border-r border-neutral-200 dark:border-neutral-700 shadow-lg theme-transition z-40 lg:translate-x-0 lg:static lg:shadow-none"
>
  <div class="flex flex-col h-full">
    <!-- Header -->
    <div class="flex items-center justify-between p-6">
      <h1 class="text-xl font-bold text-neutral-900 dark:text-neutral-100 theme-transition">MiCA</h1>
      <!-- Mobile close button -->
      <button
        class="lg:hidden p-1 rounded-lg text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-700 theme-transition"
        on:click={closeMobileMenu}
        aria-label="Close navigation menu"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Navigation -->
    <nav class="flex-1 p-4">
      <ul class="space-y-2">
        {#each navigationItems as item}
          <li>
            <a
              href={item.href}
              class="group flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium theme-transition {
                isActive(item.href)
                  ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 shadow-sm'
                  : 'text-neutral-600 dark:text-neutral-400 hover:bg-neutral-100 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-100 hover:shadow-sm'
              }"
              on:click={closeMobileMenu}
            >
              <span class="flex-shrink-0 {isActive(item.href) ? 'text-primary-600 dark:text-primary-400' : 'group-hover:text-primary-500'}">
                {@html item.icon}
              </span>
              <span>{item.name}</span>
              {#if isActive(item.href)}
                <div class="ml-auto w-1 h-1 bg-primary-500 rounded-full"></div>
              {/if}
            </a>
          </li>
        {/each}
      </ul>
    </nav>

    <!-- Footer with Theme Toggle -->
    <div class="p-4 border-t border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-900/50 theme-transition">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <!-- <svg class="w-4 h-4 text-neutral-500 dark:text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
          </svg> -->
          <span class="text-sm font-medium text-neutral-700 dark:text-neutral-300">Theme</span>
        </div>
        <ThemeToggle variant="switch" size="sm" />
      </div>
    </div>
  </div>
</aside>

<!-- Main content spacer for desktop -->
<!-- <div class="hidden lg:block w-64 flex-shrink-0"></div> -->

<style>
  /* Ensure smooth transitions */
  aside {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Custom scrollbar for navigation */
  nav {
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--color-border-secondary)) transparent;
  }

  nav::-webkit-scrollbar {
    width: 4px;
  }

  nav::-webkit-scrollbar-track {
    background: transparent;
  }

  nav::-webkit-scrollbar-thumb {
    background-color: rgb(var(--color-border-secondary));
    border-radius: 2px;
  }


</style>
