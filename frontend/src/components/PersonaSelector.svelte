<script lang="ts">
  import { onMount } from 'svelte';

  export let selectedPersonaId: string | null = null;
  export let onPersonaSelect: (personaId: string) => void;

  interface PersonaSummary {
    id: string;
    name: string;
    age: number;
    background: string;
    primaryConcerns: string[];
    sessionContext: string;
    primaryEmotions: string[];
    coreBeliefs: string[];
  }

  interface DetailedPersona {
    id: string;
    name: string;
    age: number;
    background: string;
    relevantHistory: string;
    cognitiveConceptualization: {
      coreBeliefs: string[];
      intermediateBeliefs: string[];
      intermediateBeliefsDepression?: string[];
      copingStrategies: string[];
    };
    currentSituation: string;
    automaticThoughts: string[];
    emotions: {
      primary: string[];
      secondary?: string[];
      intensityLevels?: { [emotion: string]: 'low' | 'medium' | 'high' };
    };
    behaviors: {
      maladaptive: string[];
      copingMechanisms: string[];
      behavioralPatterns: string[];
    };
    conversationalStyle: string;
    presentingConcerns: string[];
    sessionContext: string;
  }

  let personas: PersonaSummary[] = [];
  let selectedPersonaDetails: DetailedPersona | null = null;
  let loading = true;
  let error: string | null = null;

  onMount(async () => {
    await fetchPersonas();
  });

  async function fetchPersonas() {
    try {
      loading = true;
      const response = await fetch('/api/personas');
      const result = await response.json();
      
      if (result.success) {
        personas = result.data;
      } else {
        error = result.error || 'Failed to fetch personas';
      }
    } catch (err) {
      error = 'Network error while fetching personas';
      console.error('Error fetching personas:', err);
    } finally {
      loading = false;
    }
  }

  async function selectPersona(personaId: string) {
    try {
      // Fetch detailed persona information
      const response = await fetch(`/api/personas/${personaId}`);
      const result = await response.json();
      
      if (result.success) {
        selectedPersonaDetails = result.data;
        selectedPersonaId = personaId;
        onPersonaSelect(personaId);
      } else {
        error = result.error || 'Failed to fetch persona details';
      }
    } catch (err) {
      error = 'Network error while fetching persona details';
      console.error('Error fetching persona details:', err);
    }
  }

  function getEmotionColor(emotion: string): string {
    const emotionColors: { [key: string]: string } = {
      'anxious': 'text-yellow-600 dark:text-yellow-400',
      'worried': 'text-yellow-600 dark:text-yellow-400',
      'depressed': 'text-blue-600 dark:text-blue-400',
      'hopeless': 'text-gray-600 dark:text-gray-400',
      'fearful': 'text-red-600 dark:text-red-400',
      'frustrated': 'text-orange-600 dark:text-orange-400',
      'confused': 'text-purple-600 dark:text-purple-400',
      'angry': 'text-red-700 dark:text-red-500',
      'sad': 'text-blue-500 dark:text-blue-300',
      'guilty': 'text-indigo-600 dark:text-indigo-400',
      'ashamed': 'text-gray-700 dark:text-gray-300'
    };
    return emotionColors[emotion] || 'text-neutral-600 dark:text-neutral-400';
  }

  function getConcernIcon(concern: string): string {
    if (concern.toLowerCase().includes('anxiety') || concern.toLowerCase().includes('panic')) return '😰';
    if (concern.toLowerCase().includes('depression') || concern.toLowerCase().includes('mood')) return '😔';
    if (concern.toLowerCase().includes('trauma') || concern.toLowerCase().includes('ptsd')) return '💔';
    if (concern.toLowerCase().includes('relationship') || concern.toLowerCase().includes('family')) return '👥';
    if (concern.toLowerCase().includes('work') || concern.toLowerCase().includes('career')) return '💼';
    if (concern.toLowerCase().includes('sleep')) return '😴';
    if (concern.toLowerCase().includes('substance') || concern.toLowerCase().includes('alcohol')) return '🍷';
    return '🧠';
  }
</script>

<div class="persona-selector">
  <!-- <h2 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
    Select Persona
  </h2> -->

  {#if loading}
    <div class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      <span class="ml-2 text-neutral-600 dark:text-neutral-400">Loading personas...</span>
    </div>
  {:else if error}
    <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
      <p class="text-red-700 dark:text-red-300">{error}</p>
      <button 
        on:click={fetchPersonas}
        class="mt-2 text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 underline"
      >
        Try again
      </button>
    </div>
  {:else}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      {#each personas as persona}
        <div 
          class="persona-card cursor-pointer border-2 rounded-lg p-4 transition-all duration-200 {
            selectedPersonaId === persona.id 
              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
              : 'border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 hover:border-primary-300 dark:hover:border-primary-600'
          }"
          on:click={() => selectPersona(persona.id)}
          on:keydown={(e) => e.key === 'Enter' && selectPersona(persona.id)}
          tabindex="0"
          role="button"
        >
          <div class="flex items-start justify-between mb-2">
            <h3 class="font-semibold text-lg text-neutral-900 dark:text-neutral-100">
              {persona.name}
            </h3>
            <span class="text-sm text-neutral-500 dark:text-neutral-400">
              Age {persona.age}
            </span>
          </div>
          
          <p class="text-sm text-neutral-600 dark:text-neutral-300 mb-3">
            {persona.background}
          </p>

          <div class="mb-3">
            <h4 class="text-xs font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Primary Emotions:
            </h4>
            <div class="flex flex-wrap gap-1">
              {#each persona.primaryEmotions as emotion}
                <span class="text-xs px-2 py-1 rounded-full bg-neutral-100 dark:bg-neutral-700 {getEmotionColor(emotion)}">
                  {emotion}
                </span>
              {/each}
            </div>
          </div>

          <div class="mb-3">
            <h4 class="text-xs font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Key Concerns:
            </h4>
            <div class="space-y-1">
              {#each persona.primaryConcerns.slice(0, 2) as concern}
                <div class="flex items-center text-xs text-neutral-600 dark:text-neutral-400">
                  <span class="mr-1">{getConcernIcon(concern)}</span>
                  {concern}
                </div>
              {/each}
            </div>
          </div>

          <div class="text-xs text-neutral-500 dark:text-neutral-400 border-t border-neutral-200 dark:border-neutral-600 pt-2">
            {persona.sessionContext}
          </div>
        </div>
      {/each}
    </div>
  {/if}

  {#if selectedPersonaDetails}
    <div class="selected-persona-details bg-neutral-50 dark:bg-neutral-800 rounded-lg p-6 border border-neutral-200 dark:border-neutral-700">
      <h3 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4">
        Selected: {selectedPersonaDetails.name}
      </h3>
      
      <!-- Enhanced Three-Column Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column: History & Situation -->
        <div class="space-y-4">
          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              📋 Relevant History
            </h4>
            <p class="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
              {selectedPersonaDetails.relevantHistory}
            </p>
          </div>

          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              🎯 Current Situation
            </h4>
            <p class="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
              {selectedPersonaDetails.currentSituation}
            </p>
          </div>

          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              💭 Automatic Thoughts
            </h4>
            <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
              {#each selectedPersonaDetails.automaticThoughts as thought}
                <li class="flex items-start">
                  <span class="text-neutral-400 mr-2">•</span>
                  <span class="italic">"{thought}"</span>
                </li>
              {/each}
            </ul>
          </div>
        </div>

        <!-- Middle Column: Cognitive Conceptualization -->
        <div class="space-y-4">
          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-3 flex items-center">
              🧠 Cognitive Conceptualization
            </h4>

            <!-- Core Beliefs -->
            <div class="mb-4">
              <h5 class="text-sm font-medium text-red-600 dark:text-red-400 mb-2">Core Beliefs</h5>
              <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                {#each selectedPersonaDetails.cognitiveConceptualization.coreBeliefs as belief}
                  <li class="flex items-start">
                    <span class="text-red-400 mr-2">•</span>
                    <span class="font-medium">"{belief}"</span>
                  </li>
                {/each}
              </ul>
            </div>

            <!-- Intermediate Beliefs -->
            <div class="mb-4">
              <h5 class="text-sm font-medium text-orange-600 dark:text-orange-400 mb-2">Intermediate Beliefs</h5>
              <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                {#each selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefs as belief}
                  <li class="flex items-start">
                    <span class="text-orange-400 mr-2">•</span>
                    <span>"{belief}"</span>
                  </li>
                {/each}
              </ul>
            </div>

            <!-- Depression-specific Intermediate Beliefs (if present) -->
            {#if selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefsDepression}
              <div class="mb-4">
                <h5 class="text-sm font-medium text-blue-600 dark:text-blue-400 mb-2">During Depression</h5>
                <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                  {#each selectedPersonaDetails.cognitiveConceptualization.intermediateBeliefsDepression as belief}
                    <li class="flex items-start">
                      <span class="text-blue-400 mr-2">•</span>
                      <span>"{belief}"</span>
                    </li>
                  {/each}
                </ul>
              </div>
            {/if}

            <!-- Coping Strategies -->
            <div>
              <h5 class="text-sm font-medium text-green-600 dark:text-green-400 mb-2">Coping Strategies</h5>
              <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
                {#each selectedPersonaDetails.cognitiveConceptualization.copingStrategies as strategy}
                  <li class="flex items-start">
                    <span class="text-green-400 mr-2">•</span>
                    <span>{strategy}</span>
                  </li>
                {/each}
              </ul>
            </div>
          </div>
        </div>

        <!-- Right Column: Emotions & Behaviors -->
        <div class="space-y-4">
          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              😊 Emotions
            </h4>
            <div class="space-y-3">
              <div>
                <span class="text-xs font-medium text-neutral-700 dark:text-neutral-300">Primary:</span>
                <div class="flex flex-wrap gap-1 mt-1">
                  {#each selectedPersonaDetails.emotions.primary as emotion}
                    <span class="text-xs px-2 py-1 rounded-full bg-neutral-200 dark:bg-neutral-600 {getEmotionColor(emotion)}">
                      {emotion}
                      {#if selectedPersonaDetails.emotions.intensityLevels?.[emotion]}
                        ({selectedPersonaDetails.emotions.intensityLevels[emotion]})
                      {/if}
                    </span>
                  {/each}
                </div>
              </div>
              {#if selectedPersonaDetails.emotions.secondary}
                <div>
                  <span class="text-xs font-medium text-neutral-700 dark:text-neutral-300">Secondary:</span>
                  <div class="flex flex-wrap gap-1 mt-1">
                    {#each selectedPersonaDetails.emotions.secondary as emotion}
                      <span class="text-xs px-2 py-1 rounded-full bg-neutral-100 dark:bg-neutral-700 {getEmotionColor(emotion)}">
                        {emotion}
                      </span>
                    {/each}
                  </div>
                </div>
              {/if}
            </div>
          </div>

          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              ⚠️ Maladaptive Behaviors
            </h4>
            <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
              {#each selectedPersonaDetails.behaviors.maladaptive as behavior}
                <li class="flex items-start">
                  <span class="text-orange-400 mr-2">•</span>
                  {behavior}
                </li>
              {/each}
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              🛠️ Coping Mechanisms
            </h4>
            <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
              {#each selectedPersonaDetails.behaviors.copingMechanisms as mechanism}
                <li class="flex items-start">
                  <span class="text-blue-400 mr-2">•</span>
                  {mechanism}
                </li>
              {/each}
            </ul>
          </div>

          <div>
            <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
              🔄 Behavioral Patterns
            </h4>
            <ul class="text-sm text-neutral-600 dark:text-neutral-300 space-y-1">
              {#each selectedPersonaDetails.behaviors.behavioralPatterns as pattern}
                <li class="flex items-start">
                  <span class="text-purple-400 mr-2">•</span>
                  {pattern}
                </li>
              {/each}
            </ul>
          </div>
        </div>
      </div>

      <!-- Bottom Section: Presenting Concerns & Style -->
      <div class="mt-6 pt-4 border-t border-neutral-200 dark:border-neutral-600 grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
            🎯 Presenting Concerns
          </h4>
          <div class="flex flex-wrap gap-2">
            {#each selectedPersonaDetails.presentingConcerns as concern}
              <span class="text-xs px-3 py-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800">
                {getConcernIcon(concern)} {concern}
              </span>
            {/each}
          </div>
        </div>

        <div>
          <h4 class="font-medium text-neutral-800 dark:text-neutral-200 mb-2 flex items-center">
            💬 Conversational Style
          </h4>
          <p class="text-sm text-neutral-600 dark:text-neutral-300 italic">
            {selectedPersonaDetails.conversationalStyle}
          </p>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .persona-card:focus {
    outline: 2px solid theme('colors.primary.500');
    outline-offset: 2px;
  }
</style>
