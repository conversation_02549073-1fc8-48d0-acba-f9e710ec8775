import { writable } from 'svelte/store';
import { browser } from '$app/environment';

export type Theme = 'light' | 'dark';

interface ThemeStore {
  current: Theme;
  isSystemDark: boolean;
  useSystemPreference: boolean;
}

// Default to dark mode as requested
const DEFAULT_THEME: Theme = 'dark';

function createThemeStore() {
  // Initialize with default values
  const initialState: ThemeStore = {
    current: DEFAULT_THEME,
    isSystemDark: false,
    useSystemPreference: false
  };

  const { subscribe, set, update } = writable<ThemeStore>(initialState);

  return {
    subscribe,
    
    // Initialize theme system
    init: () => {
      if (!browser) return;

      // Check system preference
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const isSystemDark = mediaQuery.matches;

      // Get stored preference
      const storedTheme = localStorage.getItem('theme') as Theme | null;
      const storedUseSystem = localStorage.getItem('useSystemPreference') === 'true';

      let currentTheme: Theme;
      
      if (storedUseSystem) {
        // Use system preference
        currentTheme = isSystemDark ? 'dark' : 'light';
      } else if (storedTheme) {
        // Use stored preference
        currentTheme = storedTheme;
      } else {
        // Default to dark mode
        currentTheme = DEFAULT_THEME;
      }

      // Update store
      set({
        current: currentTheme,
        isSystemDark,
        useSystemPreference: storedUseSystem
      });

      // Apply theme to document
      applyTheme(currentTheme);

      // Listen for system theme changes
      mediaQuery.addEventListener('change', (e) => {
        update(state => {
          const newIsSystemDark = e.matches;
          const newState = { ...state, isSystemDark: newIsSystemDark };
          
          if (state.useSystemPreference) {
            newState.current = newIsSystemDark ? 'dark' : 'light';
            applyTheme(newState.current);
          }
          
          return newState;
        });
      });
    },

    // Set specific theme
    setTheme: (theme: Theme) => {
      if (!browser) return;

      update(state => {
        const newState = { ...state, current: theme, useSystemPreference: false };
        
        // Store preference
        localStorage.setItem('theme', theme);
        localStorage.setItem('useSystemPreference', 'false');
        
        // Apply theme
        applyTheme(theme);
        
        return newState;
      });
    },

    // Toggle between light and dark
    toggle: () => {
      if (!browser) return;

      update(state => {
        const newTheme: Theme = state.current === 'light' ? 'dark' : 'light';
        const newState = { ...state, current: newTheme, useSystemPreference: false };
        
        // Store preference
        localStorage.setItem('theme', newTheme);
        localStorage.setItem('useSystemPreference', 'false');
        
        // Apply theme
        applyTheme(newTheme);
        
        return newState;
      });
    },

    // Use system preference
    useSystemPreference: () => {
      if (!browser) return;

      update(state => {
        const newTheme: Theme = state.isSystemDark ? 'dark' : 'light';
        const newState = { 
          ...state, 
          current: newTheme, 
          useSystemPreference: true 
        };
        
        // Store preference
        localStorage.setItem('useSystemPreference', 'true');
        localStorage.removeItem('theme');
        
        // Apply theme
        applyTheme(newTheme);
        
        return newState;
      });
    }
  };
}

// Apply theme to document
function applyTheme(theme: Theme) {
  if (!browser) return;

  const root = document.documentElement;
  
  // Remove existing theme classes
  root.classList.remove('light', 'dark');
  
  // Add new theme class
  root.classList.add(theme);
  
  // Update meta theme-color for mobile browsers
  const metaThemeColor = document.querySelector('meta[name="theme-color"]');
  if (metaThemeColor) {
    metaThemeColor.setAttribute('content', theme === 'dark' ? '#171717' : '#ffffff');
  } else {
    // Create meta theme-color if it doesn't exist
    const meta = document.createElement('meta');
    meta.name = 'theme-color';
    meta.content = theme === 'dark' ? '#171717' : '#ffffff';
    document.head.appendChild(meta);
  }
}

export const themeStore = createThemeStore();

// Utility function to get current theme
export function getCurrentTheme(): Theme {
  if (!browser) return DEFAULT_THEME;
  
  let currentTheme: Theme = DEFAULT_THEME;
  themeStore.subscribe(state => {
    currentTheme = state.current;
  })();
  
  return currentTheme;
}

// Utility function to check if dark mode is active
export function isDarkMode(): boolean {
  return getCurrentTheme() === 'dark';
}
