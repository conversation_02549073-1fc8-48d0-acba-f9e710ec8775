version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3000
      - VITE_WS_URL=ws://localhost:3000
    depends_on:
      - backend
    networks:
      - mica-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - PORT=3000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DATABASE_URL=postgresql://${POSTGRES_USER:-mica_user}:${POSTGRES_PASSWORD:-mica_password}@postgres:5432/${POSTGRES_DB:-mica_dev}
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - mica-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mica-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-mica_dev}
      POSTGRES_USER: ${POSTGRES_USER:-mica_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-mica_password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - mica-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-mica_user} -d ${POSTGRES_DB:-mica_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Optional: Redis for caching and session management
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   networks:
  #     - mica-network

networks:
  mica-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
