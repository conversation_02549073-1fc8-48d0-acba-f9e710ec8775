# MiCA - AI Therapy Simulation

A sophisticated single-page application featuring two AI agents (therapist and patient) engaging in therapeutic conversations with real-time insights into their thinking processes.

## 🎯 Features

- **Dual Agent Simulation**: Therapist and patient AI agents powered by OpenAI GPT-5-nano
- **Three-Pane Interface**:
  - Left: Therapist thinking process and analysis
  - Center: Real-time conversation display
  - Right: Patient thinking process and emotional state
- **Real-time Analysis**: Sentiment, motivation, and engagement level tracking
- **Configurable Sessions**: Adjustable conversation parameters
- **Clean Design**: Minimal, professional interface built with Svelte and Tailwind CSS

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   External      │
│   (Svelte)      │◄──►│   (Node.js)     │◄──►│   Services      │
│                 │    │                 │    │                 │
│ - 3-pane UI     │    │ - API Routes    │    │ - OpenAI API    │
│ - Real-time     │    │ - WebSocket     │    │ - Supabase DB   │
│ - Config Panel  │    │ - Agent Logic   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Tech Stack

- **Frontend**: Svelte + SvelteKit + TypeScript + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript + WebSocket
- **Database**: PostgreSQL (Docker)
- **AI**: OpenAI GPT-5-nano
- **Containerization**: Docker + Docker Compose
- **Package Management**: npm

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- OpenAI API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd MiCA
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Install dependencies**
   ```bash
   npm run setup
   ```

4. **Set up the database**
   ```bash
   # Start PostgreSQL container
   npm run db:up

   # Run database migrations
   npm run db:migrate
   ```

5. **Start with Docker (Recommended)**
   ```bash
   npm run docker:up
   ```

6. **Or start development servers locally**
   ```bash
   npm run dev
   ```

7. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000
   - Health Check: http://localhost:3000/health

## 📁 Project Structure

```
MiCA/
├── frontend/                 # Svelte frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   │   ├── ui/         # Basic UI components
│   │   │   ├── chat/       # Chat-related components
│   │   │   └── config/     # Configuration components
│   │   ├── lib/            # Utility libraries
│   │   ├── stores/         # Svelte stores
│   │   ├── types/          # TypeScript type definitions
│   │   └── routes/         # SvelteKit routes
│   ├── Dockerfile
│   └── package.json
├── backend/                  # Node.js backend application
│   ├── src/
│   │   ├── routes/         # API route handlers
│   │   ├── services/       # Business logic services
│   │   │   ├── agents/     # AI agent implementations
│   │   │   ├── database/   # Database operations
│   │   │   └── websocket/  # WebSocket handling
│   │   ├── models/         # Data models
│   │   ├── utils/          # Utility functions
│   │   └── types/          # TypeScript type definitions
│   ├── Dockerfile
│   └── package.json
├── database/                 # Database schemas and migrations
│   ├── migrations/         # SQL migration files
│   └── seeds/              # Seed data
├── tests/                    # Test suites
│   ├── unit/               # Unit tests
│   ├── integration/        # Integration tests
│   └── e2e/                # End-to-end tests
├── docs/                     # Documentation
│   ├── api/                # API documentation
│   ├── user/               # User guides
│   └── developer/          # Developer documentation
├── scripts/                  # Utility scripts
├── docker-compose.yml        # Docker services configuration
├── IMPLEMENTATION_PLAN.md    # Detailed implementation plan
├── DEVELOPMENT_CHECKLIST.md  # Comprehensive development checklist
└── README.md                # This file
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start both frontend and backend in development mode
npm run dev:frontend     # Start only frontend development server
npm run dev:backend      # Start only backend development server

# Building
npm run build            # Build both frontend and backend
npm run build:frontend   # Build only frontend
npm run build:backend    # Build only backend

# Testing
npm run test             # Run all tests
npm run test:frontend    # Run frontend tests
npm run test:backend     # Run backend tests

# Docker
npm run docker:build     # Build Docker images
npm run docker:up        # Start services with Docker Compose
npm run docker:down      # Stop Docker services
npm run docker:logs      # View Docker logs

# Utilities
npm run lint             # Lint all code
npm run format           # Format all code
npm run clean            # Clean build artifacts and dependencies
```

## 📋 Next Steps

1. Review the [Implementation Plan](IMPLEMENTATION_PLAN.md) for detailed development phases
2. Check the [Development Checklist](DEVELOPMENT_CHECKLIST.md) for comprehensive task tracking
3. Set up your development environment using the instructions above
4. Begin with Phase 1: Foundation Setup as outlined in the implementation plan

## 🤝 Contributing

Please refer to the development checklist and implementation plan for contribution guidelines and development workflow.

## 📄 License

MIT License - see LICENSE file for details.