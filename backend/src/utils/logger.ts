// <PERSON> Configuration for MiCA Backend
import winston from 'winston';
import path from 'path';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  debug: 3,
};

// Define colors for console output
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  debug: 'blue',
};

// Add colors to winston
winston.addColors(logColors);

// Custom format for console output with emojis
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, service, operation, ...meta }) => {
    const emoji = getLogEmoji(level);
    const servicePrefix = service ? `[${service}]` : '';
    const operationPrefix = operation ? `{${operation}}` : '';
    const metaString = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
    
    return `${timestamp} ${emoji} ${level}${servicePrefix}${operationPrefix}: ${message}${metaString}`;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Get emoji for log level
function getLogEmoji(level: string): string {
  const emojiMap: { [key: string]: string } = {
    error: '❌',
    warn: '⚠️',
    info: 'ℹ️',
    debug: '🔍',
  };
  return emojiMap[level.toLowerCase()] || 'ℹ️';
}

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Create winston logger instance
const logger = winston.createLogger({
  levels: logLevels,
  level: process.env['LOG_LEVEL'] || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.metadata({ fillExcept: ['message', 'level', 'timestamp', 'service'] })
  ),
  transports: [
    // Console transport
    new winston.transports.Console({
      format: consoleFormat,
      level: process.env['LOG_LEVEL'] || 'info',
    }),

    // File transport for all logs
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),

    // File transport for error logs only
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),

    // File transport for OpenAI service logs
    new winston.transports.File({
      filename: path.join(logsDir, 'openai.log'),
      format: winston.format.combine(
        winston.format((info: any) => {
          // Only log if it's from the openai-service
          return info.service === 'openai-service' ? info : false;
        })(),
        fileFormat
      ),
      maxsize: 5242880, // 5MB
      maxFiles: 3,
    }),
  ],
});

// Create a child logger for OpenAI service
export const openaiLogger = logger.child({ service: 'openai-service' });

// Create a child logger for conversation orchestrator
export const conversationLogger = logger.child({ service: 'conversation-orchestrator' });

// Create a child logger for therapist agent
export const therapistLogger = logger.child({ service: 'therapist-agent' });

// Create a child logger for patient agent
export const patientLogger = logger.child({ service: 'patient-agent' });

// Helper function to log function entry
export function logFunctionEntry(logger: winston.Logger, functionName: string, params?: any): void {
  logger.info(`Starting ${functionName}`, {
    operation: functionName,
    phase: 'entry',
    parameters: params ? sanitizeLogData(params) : undefined,
  });
}

// Helper function to log function success
export function logFunctionSuccess(logger: winston.Logger, functionName: string, result?: any, duration?: number): void {
  logger.info(`Completed ${functionName} successfully`, {
    operation: functionName,
    phase: 'success',
    duration: duration ? `${duration}ms` : undefined,
    result: result ? sanitizeLogData(result) : undefined,
  });
}

// Helper function to log function error
export function logFunctionError(logger: winston.Logger, functionName: string, error: any, duration?: number): void {
  logger.error(`Failed ${functionName}`, {
    operation: functionName,
    phase: 'error',
    duration: duration ? `${duration}ms` : undefined,
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
    },
  });
}

// Helper function to sanitize sensitive data from logs
function sanitizeLogData(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveKeys = ['apiKey', 'password', 'token', 'secret', 'key'];
  const sanitized = { ...data };

  for (const key in sanitized) {
    if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof sanitized[key] === 'object') {
      sanitized[key] = sanitizeLogData(sanitized[key]);
    }
  }

  return sanitized;
}

// Helper function to log full completion message sent to OpenAI
export function logOpenAIRequest(logger: winston.Logger, operation: string, request: any): void {
  logger.info(`OpenAI request for ${operation}`, {
    operation,
    phase: 'request',
    request: {
      model: request.model,
      messages: request.messages,
      temperature: request.temperature,
      maxTokens: request.max_completion_tokens,
    },
  });
}

// Helper function to log OpenAI API usage
export function logOpenAIUsage(logger: winston.Logger, operation: string, usage: any, processingTime: number): void {
  logger.info(`OpenAI API usage for ${operation}`, {
    operation,
    phase: 'usage',
    processingTime: `${processingTime}ms`,
    usage: {
      promptTokens: usage?.prompt_tokens,
      completionTokens: usage?.completion_tokens,
      totalTokens: usage?.total_tokens,
    },
  });
}

// Helper function to log OpenAI response details
export function logOpenAIResponse(logger: winston.Logger, operation: string, response: any): void {
  logger.debug(`OpenAI response details for ${operation}`, {
    operation,
    phase: 'response',
    response: {
      model: response.model,
      finishReason: response.choices?.[0]?.finish_reason,
      contentLength: response.choices?.[0]?.message?.content?.length,
      responseId: response.id,
    },
  });
}

export default logger;
