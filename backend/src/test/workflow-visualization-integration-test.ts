// Workflow Visualization Integration Test
// Tests the enhanced LangGraph integration with visual workflow support

import { OpenAIService } from '../services/openai.js';
import { MultiTherapistAgentService } from '../services/agents/multi-therapist.js';
import { MultiTherapistConversationContext } from '../types/index.js';

/**
 * Test the enhanced workflow integration with visual dashboard support
 */
async function testWorkflowVisualizationIntegration() {
  console.log('🧪 Testing Workflow Visualization Integration...');

  try {
    // Initialize services
    const openaiService = new OpenAIService();
    
    // Test with workflow enabled and enhanced streaming
    const multiTherapistWithWorkflow = new MultiTherapistAgentService(
      openaiService,
      {},
      { useWorkflow: true }
    );

    // Create test conversation context
    const testContext: MultiTherapistConversationContext = {
      id: 'test-workflow-viz-123',
      patientMessages: [
        {
          id: 'msg-1',
          conversationId: 'test-workflow-viz-123',
          content: 'I have been struggling with anxiety and depression. I feel overwhelmed and don\'t know where to start.',
          timestamp: new Date().toISOString()
        }
      ],
      therapistConversations: {
        cbtOnly: {
          id: 'cbt-conv-123',
          messages: [],
          currentTurn: 1,
          maxTurns: 10,
          status: 'active'
        },
        miFixedPretreatment: {
          id: 'mi-conv-123',
          messages: [],
          currentTurn: 1,
          maxTurns: 10,
          status: 'active'
        },
        dynamicAdaptive: {
          id: 'dynamic-conv-123',
          messages: [],
          currentTurn: 1,
          maxTurns: 10,
          status: 'active'
        }
      },
      studyMetadata: {
        startTime: new Date().toISOString(),
        patientPersonaId: 'test-persona',
        studyConfiguration: {
          readinessThresholds: {
            cbtMinimum: 6,
            miMaximum: 5
          },
          allowDynamicSwitching: true
        }
      },
      currentTurn: 1,
      maxTurns: 10,
      status: 'active'
    };

    const testMessage = 'I have been feeling really anxious lately and I am not sure what to do about it.';

    console.log('📊 Testing enhanced workflow streaming...');

    // Test enhanced streaming with workflow visualization support
    const streamingResults = [];
    const streamGenerator = multiTherapistWithWorkflow.streamMultiTherapistResponse(
      testMessage,
      testContext
    );

    let stepCount = 0;
    let hasPatientAnalysis = false;
    let hasApproachSelection = false;
    let hasPersonaResponses = false;
    let hasWorkflowComplete = false;

    for await (const update of streamGenerator) {
      streamingResults.push(update);
      stepCount++;

      console.log(`📨 Received update ${stepCount}: ${update.type}`);

      // Validate different update types
      switch (update.type) {
        case 'workflow_progress':
          console.log(`  🔄 Workflow step: ${update.data.step}`);
          console.log(`  📈 Progress: ${update.data.progress}%`);
          
          // Validate workflow progress data structure
          if (!update.data.step || !update.data.timestamp) {
            throw new Error('Invalid workflow progress data structure');
          }
          break;

        case 'patient_analysis_complete':
          hasPatientAnalysis = true;
          console.log(`  🧠 Patient analysis completed`);
          console.log(`  📊 Sentiment: ${update.data.analysis.sentiment}`);
          console.log(`  📊 Readiness: ${update.data.analysis.readinessScore?.score}/10`);
          
          // Validate patient analysis structure
          if (!update.data.analysis.sentiment || !update.data.analysis.readinessScore) {
            throw new Error('Invalid patient analysis data structure');
          }
          break;

        case 'approaches_selected':
          hasApproachSelection = true;
          console.log(`  🎯 Therapeutic approaches selected`);
          
          const approaches = update.data.approaches;
          const expectedPersonas = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];
          
          for (const persona of expectedPersonas) {
            if (!approaches[persona]) {
              throw new Error(`Missing therapeutic approach for persona: ${persona}`);
            }
            console.log(`    ${persona}: ${approaches[persona].name}`);
          }
          break;

        case 'persona_response_ready':
          hasPersonaResponses = true;
          console.log(`  💬 ${update.data.persona} response ready`);
          console.log(`  📝 Message preview: ${update.data.response.message.substring(0, 100)}...`);
          
          // Validate persona response structure
          if (!update.data.persona || !update.data.response.message) {
            throw new Error('Invalid persona response data structure');
          }
          break;

        case 'workflow_complete':
          hasWorkflowComplete = true;
          console.log(`  ✅ Workflow completed`);
          console.log(`  ⏱️ Total processing time: ${update.data.totalProcessingTime}ms`);
          
          // Validate final response structure
          if (!update.data.response || !update.data.response.cbtOnly) {
            throw new Error('Invalid workflow completion data structure');
          }
          break;

        case 'workflow_error':
          console.error(`  ❌ Workflow error in step: ${update.data.step}`);
          console.error(`  📝 Error message: ${update.data.error}`);
          break;

        default:
          console.log(`  ℹ️ Other update type: ${update.type}`);
      }
    }

    // Validate that all expected workflow events occurred
    console.log('🔍 Validating workflow event sequence...');
    
    if (!hasPatientAnalysis) {
      throw new Error('Patient analysis completion event not received');
    }
    
    if (!hasApproachSelection) {
      throw new Error('Approach selection event not received');
    }
    
    if (!hasPersonaResponses) {
      throw new Error('Persona response events not received');
    }
    
    if (!hasWorkflowComplete) {
      throw new Error('Workflow completion event not received');
    }

    console.log(`✅ Received ${stepCount} workflow updates`);
    console.log('✅ All expected workflow events received');

    // Test workflow state persistence for dashboard
    console.log('📊 Testing workflow state structure for dashboard...');
    
    const workflowProgressUpdates = streamingResults.filter(r => r.type === 'workflow_progress');
    if (workflowProgressUpdates.length === 0) {
      throw new Error('No workflow progress updates received');
    }

    // Validate workflow state structure for dashboard compatibility
    const sampleWorkflowState = workflowProgressUpdates[0].data;
    const requiredFields = ['step', 'progress', 'timestamp'];
    
    for (const field of requiredFields) {
      if (!(field in sampleWorkflowState)) {
        throw new Error(`Missing required field in workflow state: ${field}`);
      }
    }

    console.log('✅ Workflow state structure validated for dashboard compatibility');

    // Test error handling
    console.log('🧪 Testing error handling...');
    
    try {
      // Test with invalid context to trigger error handling
      const invalidContext = { ...testContext, id: '' };
      const errorStreamGenerator = multiTherapistWithWorkflow.streamMultiTherapistResponse(
        testMessage,
        invalidContext as any
      );

      let errorReceived = false;
      for await (const update of errorStreamGenerator) {
        if (update.type === 'workflow_error') {
          errorReceived = true;
          console.log('✅ Error handling working correctly');
          break;
        }
        // Should fallback to original implementation
        if (update.type === 'final_response') {
          console.log('✅ Fallback mechanism working correctly');
          break;
        }
      }
    } catch (error) {
      console.log('✅ Error handling working correctly (exception caught)');
    }

    console.log('🎉 Workflow Visualization Integration Test PASSED');
    return true;

  } catch (error) {
    console.error('❌ Workflow Visualization Integration Test FAILED:', error);
    return false;
  }
}

/**
 * Test workflow dashboard data structures
 */
async function testWorkflowDashboardDataStructures() {
  console.log('🧪 Testing Workflow Dashboard Data Structures...');

  try {
    // Test workflow state structure
    const mockWorkflowState = {
      workflowStep: 'response_generation',
      patientAnalysis: {
        sentiment: 'negative',
        motivationLevel: 'medium',
        engagementLevel: 'high',
        readinessScore: { score: 7, reasoning: 'Patient shows readiness for intervention' }
      },
      therapeuticApproaches: {
        'cbt-only': { name: 'Cognitive Behavioral Therapy', selectedTechnique: { name: 'Cognitive Restructuring' } },
        'mi-fixed-pretreatment': { name: 'Motivational Interviewing', selectedTechnique: { name: 'Reflective Listening' } },
        'dynamic-adaptive': { name: 'Dynamic Adaptive', selectedTechnique: { name: 'Behavioral Activation' } }
      },
      therapistResponses: {
        cbtOnly: { message: 'CBT response...', thinking: 'CBT thinking...', metadata: { confidence: 0.85 } },
        miFixedPretreatment: { message: 'MI response...', thinking: 'MI thinking...', metadata: { confidence: 0.90 } },
        dynamicAdaptive: { message: 'Dynamic response...', thinking: 'Dynamic thinking...', metadata: { confidence: 0.88 } }
      },
      processingMetadata: {
        startTime: new Date().toISOString(),
        stepTimings: {
          analysis: 1200,
          approach_selection: 800,
          response_generation: 3500,
          synthesis: 400
        },
        totalProcessingTime: 5900
      }
    };

    // Validate required fields for dashboard
    const requiredFields = ['workflowStep', 'processingMetadata'];
    for (const field of requiredFields) {
      if (!(field in mockWorkflowState)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Validate step timings structure
    if (!mockWorkflowState.processingMetadata.stepTimings) {
      throw new Error('Missing step timings in processing metadata');
    }

    const expectedSteps = ['analysis', 'approach_selection', 'response_generation', 'synthesis'];
    for (const step of expectedSteps) {
      if (!(step in mockWorkflowState.processingMetadata.stepTimings)) {
        console.warn(`Missing timing for step: ${step}`);
      }
    }

    console.log('✅ Workflow state structure validated');

    // Test workflow history structure
    const mockWorkflowHistory = [
      { ...mockWorkflowState, id: 'workflow-1', workflowStep: 'complete' },
      { ...mockWorkflowState, id: 'workflow-2', workflowStep: 'complete', workflowError: { step: 'analysis', error: 'Test error' } }
    ];

    // Validate history structure
    if (!Array.isArray(mockWorkflowHistory)) {
      throw new Error('Workflow history must be an array');
    }

    console.log('✅ Workflow history structure validated');

    console.log('🎉 Workflow Dashboard Data Structures Test PASSED');
    return true;

  } catch (error) {
    console.error('❌ Workflow Dashboard Data Structures Test FAILED:', error);
    return false;
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  (async () => {
    console.log('🚀 Running Workflow Visualization Integration Tests...\n');
    
    const test1 = await testWorkflowVisualizationIntegration();
    console.log('');
    const test2 = await testWorkflowDashboardDataStructures();
    
    console.log('\n📊 Test Results:');
    console.log(`Workflow Integration: ${test1 ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Dashboard Data Structures: ${test2 ? '✅ PASSED' : '❌ FAILED'}`);
    
    if (test1 && test2) {
      console.log('\n🎉 All Workflow Visualization Tests PASSED!');
      process.exit(0);
    } else {
      console.log('\n❌ Some tests FAILED!');
      process.exit(1);
    }
  })();
}

export { testWorkflowVisualizationIntegration, testWorkflowDashboardDataStructures };
