// LangGraph Integration Test for MiCA Therapy Application
// Tests the Phase 1 integration to ensure existing functionality is preserved

import { OpenAIService } from '../services/openai.js';
import { MultiTherapistAgentService } from '../services/agents/multi-therapist.js';
import { MultiTherapistConversationContext } from '../types/index.js';

/**
 * Test the LangGraph integration with MultiTherapistAgentService
 */
async function testLangGraphIntegration() {
  console.log('🧪 Testing LangGraph Integration...');

  try {
    // Initialize services
    const openaiService = new OpenAIService();
    
    // Test with workflow enabled
    const multiTherapistWithWorkflow = new MultiTherapistAgentService(
      openaiService,
      {},
      { useWorkflow: true }
    );

    // Test with workflow disabled (fallback)
    const multiTherapistWithoutWorkflow = new MultiTherapistAgentService(
      openaiService,
      {},
      { useWorkflow: false }
    );

    // Create test conversation context
    const testContext: MultiTherapistConversationContext = {
      id: 'test-conversation-123',
      patientMessages: [
        {
          id: 'msg-1',
          conversationId: 'test-conversation-123',
          content: 'I have been feeling really anxious lately and I am not sure what to do about it.',
          timestamp: new Date().toISOString()
        }
      ],
      therapistConversations: {
        cbtOnly: {
          id: 'cbt-conv-123',
          messages: [],
          currentTurn: 1,
          maxTurns: 10,
          status: 'active'
        },
        miFixedPretreatment: {
          id: 'mi-conv-123',
          messages: [],
          currentTurn: 1,
          maxTurns: 10,
          status: 'active'
        },
        dynamicAdaptive: {
          id: 'dynamic-conv-123',
          messages: [],
          currentTurn: 1,
          maxTurns: 10,
          status: 'active'
        }
      },
      currentTurn: 1,
      maxTurns: 10,
      status: 'active',
      studyMetadata: {
        startTime: new Date().toISOString(),
        patientPersonaId: 'emma-anxiety',
        studyConfiguration: {
          readinessThresholds: {
            cbtMinimum: 6,
            miMaximum: 5
          },
          allowDynamicSwitching: true
        }
      }
    };

    const testMessage = 'I have been feeling really anxious lately and I am not sure what to do about it.';

    console.log('✅ Test setup complete');

    // Test 1: Workflow enabled
    console.log('🔄 Testing with LangGraph workflow enabled...');
    const workflowResponse = await multiTherapistWithWorkflow.generateMultiTherapistResponse(
      testMessage,
      testContext
    );

    console.log('✅ Workflow-enabled response generated successfully');
    console.log(`   - CBT response length: ${workflowResponse.cbtOnly.message.length} chars`);
    console.log(`   - MI response length: ${workflowResponse.miFixedPretreatment.message.length} chars`);
    console.log(`   - Dynamic response length: ${workflowResponse.dynamicAdaptive.message.length} chars`);
    console.log(`   - Has patient analysis: ${!!workflowResponse.patientAnalysis}`);
    console.log(`   - Has metadata: ${!!workflowResponse.metadata}`);

    // Test 2: Workflow disabled (fallback)
    console.log('🔄 Testing with LangGraph workflow disabled (fallback)...');
    const fallbackResponse = await multiTherapistWithoutWorkflow.generateMultiTherapistResponse(
      testMessage,
      testContext
    );

    console.log('✅ Fallback response generated successfully');
    console.log(`   - CBT response length: ${fallbackResponse.cbtOnly.message.length} chars`);
    console.log(`   - MI response length: ${fallbackResponse.miFixedPretreatment.message.length} chars`);
    console.log(`   - Dynamic response length: ${fallbackResponse.dynamicAdaptive.message.length} chars`);

    // Test 3: Workflow status methods
    console.log('🔄 Testing workflow status methods...');
    console.log(`   - Workflow enabled check: ${multiTherapistWithWorkflow.isWorkflowEnabled()}`);
    console.log(`   - Workflow disabled check: ${multiTherapistWithoutWorkflow.isWorkflowEnabled()}`);

    // Test 4: Workflow toggle
    console.log('🔄 Testing workflow toggle...');
    multiTherapistWithWorkflow.setWorkflowEnabled(false);
    console.log(`   - After disabling: ${multiTherapistWithWorkflow.isWorkflowEnabled()}`);
    multiTherapistWithWorkflow.setWorkflowEnabled(true);
    console.log(`   - After re-enabling: ${multiTherapistWithWorkflow.isWorkflowEnabled()}`);

    console.log('🎉 All LangGraph integration tests passed!');
    return true;

  } catch (error) {
    console.error('❌ LangGraph integration test failed:', error);
    return false;
  }
}

/**
 * Test streaming functionality
 */
async function testStreamingIntegration() {
  console.log('🧪 Testing LangGraph Streaming Integration...');

  try {
    const openaiService = new OpenAIService();
    const multiTherapist = new MultiTherapistAgentService(
      openaiService,
      {},
      { useWorkflow: true }
    );

    const testContext: MultiTherapistConversationContext = {
      id: 'test-stream-123',
      patientMessages: [],
      therapistConversations: {
        cbtOnly: { id: 'cbt-stream-123', messages: [], currentTurn: 1, maxTurns: 10, status: 'active' },
        miFixedPretreatment: { id: 'mi-stream-123', messages: [], currentTurn: 1, maxTurns: 10, status: 'active' },
        dynamicAdaptive: { id: 'dynamic-stream-123', messages: [], currentTurn: 1, maxTurns: 10, status: 'active' }
      },
      currentTurn: 1,
      maxTurns: 10,
      status: 'active',
      studyMetadata: {
        startTime: new Date().toISOString(),
        patientPersonaId: 'emma-anxiety',
        studyConfiguration: {
          readinessThresholds: { cbtMinimum: 6, miMaximum: 5 },
          allowDynamicSwitching: true
        }
      }
    };

    const testMessage = 'I feel overwhelmed and need help managing my stress.';

    console.log('🔄 Testing streaming workflow...');
    let updateCount = 0;
    let finalResponse = null;

    const streamGenerator = multiTherapist.streamMultiTherapistResponse(testMessage, testContext);

    for await (const update of streamGenerator) {
      updateCount++;
      console.log(`   - Stream update ${updateCount}: ${update.type}`);
      
      if (update.type === 'final_response') {
        finalResponse = update.data;
      }
    }

    console.log(`✅ Streaming completed with ${updateCount} updates`);
    console.log(`   - Final response received: ${!!finalResponse}`);

    return true;

  } catch (error) {
    console.error('❌ Streaming integration test failed:', error);
    return false;
  }
}

/**
 * Run all integration tests
 */
async function runAllTests() {
  console.log('🚀 Starting LangGraph Integration Tests...\n');

  const results = {
    basic: await testLangGraphIntegration(),
    streaming: await testStreamingIntegration()
  };

  console.log('\n📊 Test Results Summary:');
  console.log(`   - Basic Integration: ${results.basic ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   - Streaming Integration: ${results.streaming ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  return allPassed;
}

// Export for use in other test files
export {
  testLangGraphIntegration,
  testStreamingIntegration,
  runAllTests
};

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}
