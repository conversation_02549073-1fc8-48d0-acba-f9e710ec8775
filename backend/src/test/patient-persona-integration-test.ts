// Patient Persona Integration Test for LangGraph
// Verifies that therapeutic conversations work correctly with existing patient personas

import { OpenAIService } from '../services/openai.js';
import { MultiTherapistAgentService } from '../services/agents/multi-therapist.js';
import { PatientAgentService } from '../services/agents/patient.js';
import { patientPersonas } from '../data/patient-personas.js';
import { MultiTherapistConversationContext } from '../types/index.js';

/**
 * Test therapeutic conversations with each patient persona
 */
async function testPatientPersonaIntegration() {
  console.log('🧪 Testing Patient Persona Integration with LangGraph...');

  const openaiService = new OpenAIService();
  const multiTherapist = new MultiTherapistAgentService(
    openaiService,
    {},
    { useWorkflow: true }
  );

  let allTestsPassed = true;

  // Test with each patient persona
  for (const persona of patientPersonas.slice(0, 2)) { // Test first 2 personas to save time
    console.log(`\n🎭 Testing with persona: ${persona.name} (${persona.id})`);

    try {
      // Create patient agent with this persona
      const patientAgent = new PatientAgentService(openaiService, {
        id: persona.id,
        persona: {
          name: persona.name,
          age: persona.age,
          background: persona.background,
          currentSituation: persona.currentSituation,
          personalityTraits: []
        },
        emotionalState: {
          primaryMood: 'anxious',
          energyLevel: 'medium',
          openness: 'guarded',
          trustLevel: 50,
          motivationLevel: 'medium'
        },
        concerns: persona.presentingConcerns || [],
        backstory: persona.relevantHistory,
        responsePatterns: {
          tendency: 'cooperative',
          emotionalExpression: 'moderate',
          detailLevel: 'moderate'
        },
        prompts: {
          systemPrompt: '',
          responseTemplate: '',
          emotionalStatePrompt: '',
          thinkingPrompt: ''
        }
      });

      // Create conversation context
      const context: MultiTherapistConversationContext = {
        id: `test-${persona.id}-123`,
        patientMessages: [],
        therapistConversations: {
          cbtOnly: {
            id: `cbt-${persona.id}-123`,
            messages: [],
            currentTurn: 1,
            maxTurns: 10,
            status: 'active'
          },
          miFixedPretreatment: {
            id: `mi-${persona.id}-123`,
            messages: [],
            currentTurn: 1,
            maxTurns: 10,
            status: 'active'
          },
          dynamicAdaptive: {
            id: `dynamic-${persona.id}-123`,
            messages: [],
            currentTurn: 1,
            maxTurns: 10,
            status: 'active'
          }
        },
        currentTurn: 1,
        maxTurns: 10,
        status: 'active',
        studyMetadata: {
          startTime: new Date().toISOString(),
          patientPersonaId: persona.id,
          studyConfiguration: {
            readinessThresholds: {
              cbtMinimum: 6,
              miMaximum: 5
            },
            allowDynamicSwitching: true
          }
        }
      };

      // Generate initial patient message based on persona
      const initialMessage = generatePersonaSpecificMessage(persona);
      console.log(`   📝 Patient message: "${initialMessage.substring(0, 80)}..."`);

      // Test multi-therapist response
      const therapistResponse = await multiTherapist.generateMultiTherapistResponse(
        initialMessage,
        context
      );

      // Verify response structure
      console.log('   ✅ Multi-therapist response generated');
      console.log(`      - CBT approach: ${therapistResponse.cbtOnly.metadata.therapeuticApproach?.name || 'N/A'}`);
      console.log(`      - MI approach: ${therapistResponse.miFixedPretreatment.metadata.therapeuticApproach?.name || 'N/A'}`);
      console.log(`      - Dynamic approach: ${therapistResponse.dynamicAdaptive.metadata.therapeuticApproach?.name || 'N/A'}`);

      // Verify patient analysis includes persona-specific insights
      if (therapistResponse.patientAnalysis) {
        console.log(`      - Patient analysis: ${therapistResponse.patientAnalysis.sentiment} sentiment`);
        console.log(`      - Readiness score: ${therapistResponse.patientAnalysis.readinessScore.score}/10`);
        console.log(`      - Motivation: ${therapistResponse.patientAnalysis.motivationLevel}`);
        console.log(`      - Engagement: ${therapistResponse.patientAnalysis.engagementLevel}`);
      }

      // Test patient response to therapist messages
      const patientResponse = await patientAgent.generateMultiTherapistResponse(
        {
          cbtOnly: {
            content: therapistResponse.cbtOnly.message,
            therapeuticApproach: therapistResponse.cbtOnly.metadata.therapeuticApproach?.name || 'CBT',
            technique: therapistResponse.cbtOnly.metadata.therapeuticApproach?.selectedTechnique.name || 'Standard CBT'
          },
          miFixedPretreatment: {
            content: therapistResponse.miFixedPretreatment.message,
            therapeuticApproach: therapistResponse.miFixedPretreatment.metadata.therapeuticApproach?.name || 'MI',
            technique: therapistResponse.miFixedPretreatment.metadata.therapeuticApproach?.selectedTechnique.name || 'Standard MI'
          },
          dynamicAdaptive: {
            content: therapistResponse.dynamicAdaptive.message,
            therapeuticApproach: therapistResponse.dynamicAdaptive.metadata.therapeuticApproach?.name || 'Dynamic',
            technique: therapistResponse.dynamicAdaptive.metadata.therapeuticApproach?.selectedTechnique.name || 'Adaptive'
          }
        },
        context.therapistConversations
      );

      console.log('   ✅ Patient responses generated');
      console.log(`      - Response to CBT: "${patientResponse.cbtOnly.message.substring(0, 60)}..."`);
      console.log(`      - Response to MI: "${patientResponse.miFixedPretreatment.message.substring(0, 60)}..."`);
      console.log(`      - Response to Dynamic: "${patientResponse.dynamicAdaptive.message.substring(0, 60)}..."`);

      // Verify emotional state tracking
      console.log(`      - Emotional state updated: mood=${patientResponse.cbtOnly.metadata?.confidence || 'N/A'}`);

      console.log(`   🎉 Persona ${persona.name} integration test PASSED`);

    } catch (error) {
      console.error(`   ❌ Persona ${persona.name} integration test FAILED:`, error);
      allTestsPassed = false;
    }
  }

  return allTestsPassed;
}

/**
 * Generate a persona-specific initial message
 */
function generatePersonaSpecificMessage(persona: any): string {
  const messages = [
    `I've been struggling with ${persona.presentingConcerns[0]?.toLowerCase() || 'some issues'} lately.`,
    `I'm feeling ${persona.emotions.primary[0]?.toLowerCase() || 'anxious'} and I'm not sure what to do.`,
    `${persona.automaticThoughts[0] || 'I keep having negative thoughts'} and it's affecting my daily life.`,
    `I've been dealing with ${persona.currentSituation.split('.')[0]?.toLowerCase() || 'stress'}.`
  ];

  return messages[Math.floor(Math.random() * messages.length)];
}

/**
 * Test emotional analysis features
 */
async function testEmotionalAnalysisIntegration() {
  console.log('\n🧪 Testing Emotional Analysis Integration...');

  const openaiService = new OpenAIService();
  const multiTherapist = new MultiTherapistAgentService(
    openaiService,
    {},
    { useWorkflow: true }
  );

  const testCases = [
    {
      message: "I'm feeling really depressed and hopeless. Nothing seems to matter anymore.",
      expectedSentiment: 'negative',
      expectedMood: 'depressed'
    },
    {
      message: "I'm excited about starting therapy and making positive changes in my life!",
      expectedSentiment: 'positive',
      expectedMood: 'hopeful'
    },
    {
      message: "I'm not sure how I feel about this. Sometimes I'm okay, sometimes I'm not.",
      expectedSentiment: 'neutral',
      expectedMood: 'neutral'
    }
  ];

  let allTestsPassed = true;

  for (const testCase of testCases) {
    try {
      console.log(`\n   📝 Testing message: "${testCase.message.substring(0, 50)}..."`);

      const context: MultiTherapistConversationContext = {
        id: 'emotion-test-123',
        patientMessages: [],
        therapistConversations: {
          cbtOnly: { id: 'cbt-emotion-123', messages: [], currentTurn: 1, maxTurns: 10, status: 'active' },
          miFixedPretreatment: { id: 'mi-emotion-123', messages: [], currentTurn: 1, maxTurns: 10, status: 'active' },
          dynamicAdaptive: { id: 'dynamic-emotion-123', messages: [], currentTurn: 1, maxTurns: 10, status: 'active' }
        },
        currentTurn: 1,
        maxTurns: 10,
        status: 'active',
        studyMetadata: {
          startTime: new Date().toISOString(),
          patientPersonaId: 'test-persona',
          studyConfiguration: {
            readinessThresholds: { cbtMinimum: 6, miMaximum: 5 },
            allowDynamicSwitching: true
          }
        }
      };

      const response = await multiTherapist.generateMultiTherapistResponse(testCase.message, context);

      if (response.patientAnalysis) {
        console.log(`      - Detected sentiment: ${response.patientAnalysis.sentiment}`);
        console.log(`      - Motivation level: ${response.patientAnalysis.motivationLevel}`);
        console.log(`      - Engagement level: ${response.patientAnalysis.engagementLevel}`);
        console.log(`      - Readiness score: ${response.patientAnalysis.readinessScore.score}/10`);

        // Verify sentiment analysis is working
        if (response.patientAnalysis.sentiment === testCase.expectedSentiment) {
          console.log(`      ✅ Sentiment analysis correct`);
        } else {
          console.log(`      ⚠️  Sentiment analysis: expected ${testCase.expectedSentiment}, got ${response.patientAnalysis.sentiment}`);
        }
      } else {
        console.log(`      ❌ No patient analysis generated`);
        allTestsPassed = false;
      }

    } catch (error) {
      console.error(`      ❌ Emotional analysis test failed:`, error);
      allTestsPassed = false;
    }
  }

  return allTestsPassed;
}

/**
 * Run all patient persona integration tests
 */
async function runPatientPersonaTests() {
  console.log('🚀 Starting Patient Persona Integration Tests...\n');

  const results = {
    personas: await testPatientPersonaIntegration(),
    emotions: await testEmotionalAnalysisIntegration()
  };

  console.log('\n📊 Patient Persona Test Results:');
  console.log(`   - Persona Integration: ${results.personas ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   - Emotional Analysis: ${results.emotions ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  return allPassed;
}

// Export for use in other test files
export {
  testPatientPersonaIntegration,
  testEmotionalAnalysisIntegration,
  runPatientPersonaTests
};

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runPatientPersonaTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}
