// Test script for centralized prompt configuration system
// Run this to verify that all prompt configurations are loading correctly

import { promptConfigLoader } from '../services/prompt-config-loader.js';
import { promptConfigService } from '../services/prompt-config-service.js';
import { TemplateRenderer } from '../services/template-renderer.js';
import { createConversationConfigWithCentralizedPrompts } from '../config/conversation.js';

async function testPromptConfigurations() {
  console.log('🧪 Testing Centralized Prompt Configuration System');
  console.log('=' .repeat(60));

  let allTestsPassed = true;

  try {
    // Test 1: Load therapist configuration
    console.log('\n1. Testing Therapist Configuration Loading...');
    const therapistConfig = await promptConfigLoader.getTherapistConfig();
    console.log('✅ Therapist config loaded successfully');
    console.log(`   - Version: ${therapistConfig.metadata.version}`);
    console.log(`   - Categories: ${therapistConfig.metadata.categories.join(', ')}`);
    console.log(`   - System prompts: ${Object.keys(therapistConfig.system_prompts).length}`);
    console.log(`   - Therapeutic approaches: ${Object.keys(therapistConfig.therapeutic_approaches).length}`);

    // Test 2: Load patient configuration
    console.log('\n2. Testing Patient Configuration Loading...');
    const patientConfig = await promptConfigLoader.getPatientConfig();
    console.log('✅ Patient config loaded successfully');
    console.log(`   - Version: ${patientConfig.metadata.version}`);
    console.log(`   - Personas: ${Object.keys(patientConfig.personas).length}`);
    console.log(`   - Available personas: ${Object.keys(patientConfig.personas).join(', ')}`);

    // Test 3: Load shared configuration
    console.log('\n3. Testing Shared Configuration Loading...');
    const sharedConfig = await promptConfigLoader.getSharedConfig();
    console.log('✅ Shared config loaded successfully');
    console.log(`   - Version: ${sharedConfig.metadata.version}`);
    console.log(`   - Evaluation prompts: ${Object.keys(sharedConfig.evaluation_prompts).length}`);
    console.log(`   - CBT evaluations: ${Object.keys(sharedConfig.cbt_evaluation).length}`);

    // Test 4: Test prompt service methods
    console.log('\n4. Testing Prompt Service Methods...');
    
    const systemPrompt = await promptConfigService.getTherapistSystemPrompt();
    console.log('✅ System prompt retrieved');
    console.log(`   - Length: ${systemPrompt.length} characters`);

    const initialGreeting = await promptConfigService.getInitialGreeting();
    console.log('✅ Initial greeting retrieved');
    console.log(`   - Content: "${initialGreeting.substring(0, 50)}..."`);

    const analysisPrompts = await promptConfigService.getAnalysisPrompts();
    console.log('✅ Analysis prompts retrieved');
    console.log(`   - Available: ${Object.keys(analysisPrompts).join(', ')}`);

    // Test 5: Test patient persona retrieval
    console.log('\n5. Testing Patient Persona Retrieval...');
    const emmaPersona = await promptConfigService.getPatientPersonaData('emma_anxiety');
    console.log('✅ Emma persona retrieved');
    console.log(`   - Name: ${emmaPersona.name}, Age: ${emmaPersona.age}`);
    console.log(`   - Core beliefs: ${emmaPersona.cognitiveConceptualization.coreBeliefs.length}`);
    console.log(`   - Presenting concerns: ${emmaPersona.presentingConcerns.length}`);

    // Test 6: Test therapeutic technique prompts
    console.log('\n6. Testing Therapeutic Technique Prompts...');
    try {
      const cbtTechniquePrompt = await promptConfigService.getTherapeuticTechniquePrompt('cognitive_behavioral_therapy', 'cognitive_restructuring');
      console.log('✅ CBT technique prompt retrieved');
      console.log(`   - Length: ${cbtTechniquePrompt.length} characters`);
    } catch (error) {
      console.log('⚠️ CBT technique prompt not found (expected for some techniques)');
    }

    // Test 7: Test template renderer integration
    console.log('\n7. Testing Template Renderer Integration...');
    const therapistSystemPrompt = await TemplateRenderer.getTherapistSystemPrompt();
    console.log('✅ Template renderer system prompt retrieved');
    console.log(`   - Length: ${therapistSystemPrompt.length} characters`);

    // Test 8: Test conversation config creation
    console.log('\n8. Testing Conversation Config Creation...');
    const conversationConfig = await createConversationConfigWithCentralizedPrompts();
    console.log('✅ Conversation config created with centralized prompts');
    console.log(`   - Therapist name: ${conversationConfig.therapist.persona.name}`);
    console.log(`   - System prompt length: ${conversationConfig.therapist.prompts.systemPrompt.length}`);

    // Test 9: Test evaluation prompts
    console.log('\n9. Testing Evaluation Prompts...');
    try {
      const cbtValidityPrompt = await promptConfigService.getEvaluationPrompt('cbt_evaluation', 'validity_evaluation');
      console.log('✅ CBT validity evaluation prompt retrieved');
      console.log(`   - Length: ${cbtValidityPrompt.length} characters`);
    } catch (error) {
      console.log('⚠️ Evaluation prompt retrieval failed:', error);
    }

    // Test 10: Test persona-specific prompts
    console.log('\n10. Testing Persona-Specific Prompts...');
    const cbtOnlyPrompt = await promptConfigService.getPersonaSpecificPrompt('cbt_only');
    console.log('✅ CBT-only persona prompt retrieved');
    console.log(`   - Content: "${cbtOnlyPrompt.substring(0, 80)}..."`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    allTestsPassed = false;
  }

  // Summary
  console.log('\n' + '=' .repeat(60));
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Centralized prompt configuration is working correctly.');
  } else {
    console.log('❌ Some tests failed. Please check the configuration files and services.');
  }
  console.log('=' .repeat(60));
}

export { testPromptConfigurations };
