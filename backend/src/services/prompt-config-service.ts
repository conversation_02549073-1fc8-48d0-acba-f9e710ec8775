// Prompt Configuration Service for MiCA Therapy Simulation
// Service layer for accessing centralized prompt configurations with caching and fallbacks

import { promptConfigLoader } from './prompt-config-loader.js';
import { TemplateRenderer } from './template-renderer.js';

export class PromptConfigService {
  private static instance: PromptConfigService;

  private constructor() {}

  static getInstance(): PromptConfigService {
    if (!PromptConfigService.instance) {
      PromptConfigService.instance = new PromptConfigService();
    }
    return PromptConfigService.instance;
  }

  /**
   * Get therapist system prompt with fallback to hardcoded values
   */
  async getTherapistSystemPrompt(promptType: string = 'default_therapist'): Promise<string> {
    try {
      return await TemplateRenderer.getTherapistSystemPrompt(promptType);
    } catch (error) {
      console.warn('⚠️ Using fallback therapist system prompt');
      return this.getFallbackTherapistSystemPrompt();
    }
  }

  /**
   * Get initial greeting prompt
   */
  async getInitialGreeting(): Promise<string> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      return therapistConfig.response_templates.initial_greeting.content;
    } catch (error) {
      console.warn('⚠️ Using fallback initial greeting');
      return "Hello, I'm Dr. Montri. I'm glad you decided to come in today. How are you feeling right now, and what brought you here?";
    }
  }

  /**
   * Get response template
   */
  async getResponseTemplate(templateType: string = 'general_response'): Promise<string> {
    try {
      return await TemplateRenderer.getResponseTemplate(templateType);
    } catch (error) {
      console.warn('⚠️ Using fallback response template');
      return this.getFallbackResponseTemplate();
    }
  }

  /**
   * Get analysis prompts
   */
  async getAnalysisPrompts(): Promise<{
    sentiment: string;
    motivation: string;
    engagement: string;
    riskAssessment: string;
  }> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      return {
        sentiment: therapistConfig.analysis_prompts.sentiment_analysis.content,
        motivation: therapistConfig.analysis_prompts.motivation_analysis.content,
        engagement: therapistConfig.analysis_prompts.engagement_analysis.content,
        riskAssessment: therapistConfig.analysis_prompts.risk_assessment.content
      };
    } catch (error) {
      console.warn('⚠️ Using fallback analysis prompts');
      return this.getFallbackAnalysisPrompts();
    }
  }

  /**
   * Get therapeutic technique prompt
   */
  async getTherapeuticTechniquePrompt(approach: string, technique: string): Promise<string> {
    try {
      return await TemplateRenderer.getTherapeuticTechniquePrompt(approach, technique);
    } catch (error) {
      console.warn('⚠️ Using fallback technique prompt');
      return 'Respond therapeutically with empathy and understanding.';
    }
  }

  /**
   * Get multi-therapist system prompt with variables
   */
  async getMultiTherapistSystemPrompt(): Promise<string> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      return therapistConfig.system_prompts.multi_therapist_base.content;
    } catch (error) {
      console.warn('⚠️ Using fallback multi-therapist system prompt');
      return this.getFallbackMultiTherapistSystemPrompt();
    }
  }

  /**
   * Get persona-specific prompt additions
   */
  async getPersonaSpecificPrompt(personaType: string): Promise<string> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      const persona = therapistConfig.persona_specific[personaType];
      
      if (!persona) {
        throw new Error(`Persona not found: ${personaType}`);
      }

      return persona.system_prompt_addition;
    } catch (error) {
      console.warn(`⚠️ Using fallback prompt for persona: ${personaType}`);
      return this.getFallbackPersonaPrompt(personaType);
    }
  }

  /**
   * Get patient persona data
   */
  async getPatientPersonaData(personaId: string) {
    try {
      return await promptConfigLoader.getPatientPersona(personaId);
    } catch (error) {
      console.error(`❌ Error loading patient persona: ${personaId}`, error);
      throw error;
    }
  }

  /**
   * Get evaluation prompt
   */
  async getEvaluationPrompt(category: string, promptType: string): Promise<string> {
    try {
      const sharedConfig = await promptConfigLoader.getSharedConfig();
      const categoryData = (sharedConfig as any)[category];
      
      if (!categoryData || !categoryData[promptType]) {
        throw new Error(`Evaluation prompt not found: ${category}.${promptType}`);
      }

      return categoryData[promptType].content;
    } catch (error) {
      console.warn(`⚠️ Evaluation prompt not found: ${category}.${promptType}`);
      return 'Evaluate the therapeutic interaction.';
    }
  }

  // Fallback methods
  private getFallbackTherapistSystemPrompt(): string {
    return `You are Dr. Montri, a licensed clinical psychologist with 10 years of experience. You specialize in cognitive behavioral therapy and motivational interviewing interventions. Your approach is warm, empathetic, and solution-focused with a collaborative therapeutic style.

Key characteristics:
- Use active listening and reflective responses
- Ask thoughtful, open-ended questions
- Validate the patient's feelings and experiences
- Gently guide the conversation toward understanding and solutions
- Maintain professional boundaries while being genuinely caring
- Use techniques like cognitive reframing and mindfulness when appropriate

Remember to:
- Keep responses conversational and natural (2-4 sentences typically)
- Show genuine interest and concern
- Avoid being overly clinical or formal
- Build rapport gradually
- Respect the patient's pace and comfort level`;
  }

  private getFallbackResponseTemplate(): string {
    return `Based on the patient's message: "{patientMessage}"

Consider:
- The patient's emotional state and current concerns
- Appropriate therapeutic techniques to use
- How to build rapport and trust
- What questions might help explore their situation deeper

Respond as Dr. Montri would, keeping it natural and therapeutic.`;
  }

  private getFallbackAnalysisPrompts() {
    return {
      sentiment: "Analyze the emotional tone and sentiment of the patient's last message. Consider underlying emotions beyond what's explicitly stated.",
      motivation: "Assess the patient's current motivation level for change and engagement in the therapeutic process.",
      engagement: "Evaluate how engaged and open the patient seems in the conversation.",
      riskAssessment: "Identify any potential risk factors or concerning statements that might require immediate attention."
    };
  }

  private getFallbackMultiTherapistSystemPrompt(): string {
    return `You are {{therapistName}}, participating in a comparative therapy study. Your role is to provide therapeutic responses based on your assigned approach and the patient's current state.

Patient Analysis:
{{patientAnalysis}}

Therapeutic Approach: {{therapeuticApproach}}
Selected Technique: {{selectedTechnique}}
Technique Description: {{techniqueDescription}}

Specific Technique Instructions:
{{techniquePrompt}}

Patient Readiness Score: {{readinessScore}}/10
Recommended Approach: {{recommendedApproach}}

Provide a therapeutic response that demonstrates your assigned approach while being authentic and helpful.`;
  }

  private getFallbackPersonaPrompt(personaType: string): string {
    switch (personaType) {
      case 'cbt_only':
        return 'You are a CBT specialist who believes in the effectiveness of cognitive-behavioral interventions for all patients. Always use CBT techniques and frameworks in your responses.';
      case 'mi_fixed_pretreatment':
        return 'Start with Motivational Interviewing techniques to build motivation and readiness. Once the patient shows sufficient readiness for change, transition to CBT approaches.';
      case 'dynamic_adaptive':
        return 'Continuously assess patient readiness and adapt your approach accordingly. Use MI when readiness is low, CBT when readiness is high, and be flexible to switch as needed.';
      default:
        return 'Provide therapeutic responses appropriate to your assigned approach.';
    }
  }
}

// Export singleton instance
export const promptConfigService = PromptConfigService.getInstance();
