// Template Rendering Service for MiCA Therapy Simulation
// Handles reading and rendering templates with persona data using centralized prompt configurations

import fs from 'fs/promises';
import path from 'path';
import { DetailedPatientPersona } from '../data/patient-personas.js';
import { promptConfigLoader } from './prompt-config-loader.js';

export class TemplateRenderer {
  private static templateCache: Map<string, string> = new Map();

  /**
   * Read and cache the patient prompt template
   */
  private static async getTemplate(templatePath: string): Promise<string> {
    if (this.templateCache.has(templatePath)) {
      return this.templateCache.get(templatePath)!;
    }

    try {
      const template = await fs.readFile(templatePath, 'utf-8');
      this.templateCache.set(templatePath, template);
      return template;
    } catch (error) {
      console.error(`❌ Error reading template from ${templatePath}:`, error);
      throw new Error(`Failed to read template: ${templatePath}`);
    }
  }

  /**
   * Render patient prompt using centralized configuration
   */
  static async renderPatientPrompt(persona: DetailedPatientPersona): Promise<string> {
    try {
      // Get the base patient template from centralized configuration
      const patientConfig = await promptConfigLoader.getPatientConfig();
      const template = patientConfig.persona_templates.base_patient_template.content;

      // Prepare template variables from persona data
      const variables = this.prepareTemplateVariables(persona);

      // Replace all template variables
      let renderedTemplate = template;
      for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`{{${key}}}`, 'g');
        renderedTemplate = renderedTemplate.replace(regex, value);
      }

      // Handle conditional sections (like intermediateBeliefsDepression)
      renderedTemplate = this.handleConditionalSections(renderedTemplate, variables);

      return renderedTemplate;
    } catch (error) {
      console.error('❌ Error rendering patient prompt from configuration:', error);
      // Fallback to original method if configuration fails
      return this.renderPatientPromptFallback(persona);
    }
  }

  /**
   * Fallback method using original PATIENT-Psi.md template
   */
  private static async renderPatientPromptFallback(persona: DetailedPatientPersona): Promise<string> {
    // Use absolute path from project root
    const templatePath = path.resolve(process.cwd(), '../docs/prompts/PATIENT-Psi.md');
    const template = await this.getTemplate(templatePath);

    // Prepare template variables
    const variables = this.prepareTemplateVariables(persona);

    // Replace all template variables
    let renderedTemplate = template;
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      renderedTemplate = renderedTemplate.replace(regex, value);
    }

    // Handle conditional sections (like intermediateBeliefsDepression)
    renderedTemplate = this.handleConditionalSections(renderedTemplate, variables);

    return renderedTemplate;
  }

  /**
   * Prepare all template variables from persona data
   */
  private static prepareTemplateVariables(persona: DetailedPatientPersona): Record<string, string> {
    const variables: Record<string, string> = {
      // Basic persona info
      name: persona.name,
      age: persona.age.toString(),
      background: persona.background,
      sessionContext: persona.sessionContext,

      // History and situation
      relevantHistory: persona.relevantHistory,
      currentSituation: persona.currentSituation,

      // Cognitive conceptualization
      coreBeliefs: persona.cognitiveConceptualization.coreBeliefs.join(', '),
      intermediateBeliefs: persona.cognitiveConceptualization.intermediateBeliefs.join(', '),
      copingStrategies: persona.cognitiveConceptualization.copingStrategies.join(', '),

      // Thoughts and emotions
      automaticThoughts: persona.automaticThoughts.join(', '),
      primaryEmotions: persona.emotions.primary.join(', '),
      secondaryEmotions: persona.emotions.secondary?.join(', ') || '',

      // Behaviors
      maladaptiveBehaviors: persona.behaviors.maladaptive.join(', '),
      copingMechanisms: persona.behaviors.copingMechanisms.join(', '),
      behavioralPatterns: persona.behaviors.behavioralPatterns.join(', '),

      // Style and concerns
      conversationalStyle: persona.conversationalStyle,
      presentingConcerns: persona.presentingConcerns.join(', '),

      // Emotion intensity levels
      emotionIntensityLevels: this.formatEmotionIntensityLevels(persona.emotions.intensityLevels)
    };

    // Handle optional intermediate beliefs for depression
    if (persona.cognitiveConceptualization.intermediateBeliefsDepression) {
      variables.intermediateBeliefsDepression = persona.cognitiveConceptualization.intermediateBeliefsDepression.join(', ');
    }

    return variables;
  }

  /**
   * Format emotion intensity levels for template
   */
  private static formatEmotionIntensityLevels(intensityLevels?: { [emotion: string]: 'low' | 'medium' | 'high' }): string {
    if (!intensityLevels) return 'moderate intensity across emotions';

    const formatted = Object.entries(intensityLevels)
      .map(([emotion, intensity]) => `${emotion}: ${intensity}`)
      .join(', ');

    return formatted || 'moderate intensity across emotions';
  }

  /**
   * Handle conditional sections in the template
   */
  private static handleConditionalSections(template: string, variables: Record<string, string>): string {
    // Handle {{#intermediateBeliefsDepression}} conditional section
    const conditionalRegex = /{{#intermediateBeliefsDepression}}(.*?){{\/intermediateBeliefsDepression}}/gs;
    
    return template.replace(conditionalRegex, (match, content) => {
      if (variables.intermediateBeliefsDepression) {
        // Replace the variable within the conditional content
        return content.replace(/{{intermediateBeliefsDepression}}/g, variables.intermediateBeliefsDepression);
      }
      return ''; // Remove the entire conditional section if variable doesn't exist
    });
  }

  /**
   * Generic template rendering method for simple variable substitution
   * Supports {{variableName}} syntax
   */
  render(template: string, variables: Record<string, string | number>): string {
    let renderedTemplate = template;

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      renderedTemplate = renderedTemplate.replace(regex, String(value));
    }

    return renderedTemplate;
  }

  /**
   * Static version of the render method
   */
  static render(template: string, variables: Record<string, string | number>): string {
    const renderer = new TemplateRenderer();
    return renderer.render(template, variables);
  }

  /**
   * Get therapist system prompt from centralized configuration
   */
  static async getTherapistSystemPrompt(promptType: string = 'default_therapist'): Promise<string> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      const systemPrompt = therapistConfig.system_prompts[promptType];

      if (!systemPrompt) {
        throw new Error(`Therapist system prompt not found: ${promptType}`);
      }

      return systemPrompt.content;
    } catch (error) {
      console.error('❌ Error loading therapist system prompt:', error);
      // Fallback to hardcoded prompt if needed
      return `You are Dr. Montri, a licensed clinical psychologist with 10 years of experience. You specialize in cognitive behavioral therapy and motivational interviewing interventions. Your approach is warm, empathetic, and solution-focused with a collaborative therapeutic style.

Key characteristics:
- Use active listening and reflective responses
- Ask thoughtful, open-ended questions
- Validate the patient's feelings and experiences
- Gently guide the conversation toward understanding and solutions
- Maintain professional boundaries while being genuinely caring
- Use techniques like cognitive reframing and mindfulness when appropriate

Remember to:
- Keep responses conversational and natural (2-4 sentences typically)
- Show genuine interest and concern
- Avoid being overly clinical or formal
- Build rapport gradually
- Respect the patient's pace and comfort level`;
    }
  }

  /**
   * Get therapeutic technique prompt from centralized configuration
   */
  static async getTherapeuticTechniquePrompt(approach: string, technique: string): Promise<string> {
    try {
      return await promptConfigLoader.getTherapeuticTechniquePrompt(approach, technique);
    } catch (error) {
      console.error('❌ Error loading therapeutic technique prompt:', error);
      return 'Respond therapeutically with empathy and understanding.';
    }
  }

  /**
   * Get analysis prompt from centralized configuration
   */
  static async getAnalysisPrompt(analysisType: string): Promise<string> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      const analysisPrompt = therapistConfig.analysis_prompts[analysisType];

      if (!analysisPrompt) {
        throw new Error(`Analysis prompt not found: ${analysisType}`);
      }

      return analysisPrompt.content;
    } catch (error) {
      console.error('❌ Error loading analysis prompt:', error);
      return `Analyze the patient's message for ${analysisType}.`;
    }
  }

  /**
   * Get response template from centralized configuration
   */
  static async getResponseTemplate(templateType: string): Promise<string> {
    try {
      const therapistConfig = await promptConfigLoader.getTherapistConfig();
      const template = therapistConfig.response_templates[templateType];

      if (!template) {
        throw new Error(`Response template not found: ${templateType}`);
      }

      return template.content;
    } catch (error) {
      console.error('❌ Error loading response template:', error);
      return 'Respond as a therapist would, keeping it natural and therapeutic.';
    }
  }

  /**
   * Clear template cache (useful for development/testing)
   */
  static clearCache(): void {
    this.templateCache.clear();
    // Also clear prompt configuration cache
    promptConfigLoader.clearCache();
  }
}
