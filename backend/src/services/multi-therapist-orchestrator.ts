// Multi-Therapist Conversation Orchestrator for Comparative Study
import { v4 as uuidv4 } from 'uuid';
import { OpenAIService } from './openai.js';
import { MultiTherapistAgentService } from './agents/multi-therapist.js';
import { PatientAgentService } from './agents/patient.js';
import { CBTEvaluationObserverService } from './agents/cbt-evaluation-observer.js';
import { databaseService } from './database/index.js';
import {
  MultiTherapistConversationContext,
  ConversationContext,
  MultiTherapistResponse,
  TherapistPersonaResponse,
  PatientResponse,
  WebSocketClient,
  CBTEvaluationRequest,
  ComparativeCBTEvaluationResult,
  TherapistPersonaType,
  SessionMetadata
} from '../types/index.js';
import { ConversationConfig, defaultConversationConfig } from '../config/conversation.js';

export interface MultiTherapistConversationMessage {
  id: string;
  conversationId: string;
  sender: 'patient' | 'therapist';
  content: string;
  thinking?: string;
  metadata?: any;
  timestamp: string;
  // For therapist messages, include persona information
  personaType?: TherapistPersonaType;
  personaName?: string;
}

export class MultiTherapistConversationOrchestrator {
  private openaiService: OpenAIService;
  private multiTherapistAgent: MultiTherapistAgentService;
  private patientAgent: PatientAgentService;
  private evaluationObserver: CBTEvaluationObserverService;
  private config: ConversationConfig;
  private context: MultiTherapistConversationContext;
  private clients: WebSocketClient[] = [];
  private isActive: boolean = false;
  private currentTurn: 'therapist' | 'patient' = 'therapist';
  private sessionStartTime: string;
  private patientPersonaId: string;

  constructor(
    conversationId: string,
    config: ConversationConfig,
    personaId?: string
  ) {
    this.patientPersonaId = personaId || 'default';
    this.sessionStartTime = new Date().toISOString();

    this.openaiService = new OpenAIService();
    this.multiTherapistAgent = new MultiTherapistAgentService(this.openaiService);

    // Set persona ID in patient config if provided
    const patientConfig = personaId ? { ...config.patient, id: personaId } : config.patient;
    this.patientAgent = new PatientAgentService(this.openaiService, patientConfig);

    this.evaluationObserver = new CBTEvaluationObserverService();
    this.config = config;

    // Initialize multi-therapist conversation context
    this.context = {
      id: conversationId,
      patientMessages: [],
      therapistConversations: {
        cbtOnly: this.createEmptyConversationContext(conversationId + '-cbt-only'),
        miFixedPretreatment: this.createEmptyConversationContext(conversationId + '-mi-fixed'),
        dynamicAdaptive: this.createEmptyConversationContext(conversationId + '-dynamic')
      },
      currentTurn: 0,
      maxTurns: config.conversation.maxTurns,
      status: 'active',
      studyMetadata: {
        startTime: this.sessionStartTime,
        patientPersonaId: this.patientPersonaId,
        studyConfiguration: {
          readinessThresholds: config.therapist.therapeuticApproaches.readinessThresholds,
          allowDynamicSwitching: config.therapist.therapeuticApproaches.approachPreferences.allowApproachSwitching
        }
      }
    };

    console.log(`🎭 Multi-Therapist Orchestrator initialized`);
    console.log(`🆔 Conversation ID: ${this.context.id}`);
    console.log(`🎯 Max turns: ${this.context.maxTurns}`);
    console.log(`👤 Patient Persona: ${this.patientPersonaId}`);
  }

  private createEmptyConversationContext(id: string): ConversationContext {
    return {
      id,
      messages: [],
      currentTurn: 0,
      maxTurns: this.config.conversation.maxTurns,
      status: 'active'
    };
  }

  /**
   * Start the multi-therapist conversation
   */
  async startConversation(): Promise<void> {
    console.log('🚀 Starting multi-therapist conversation...');

    if (this.isActive) {
      console.warn('⚠️ Conversation is already active');
      return;
    }

    this.isActive = true;
    this.context.status = 'active';

    // Create conversation record in database
    try {
      const sessionMetadata: SessionMetadata = {
        startTime: this.sessionStartTime,
        patientPersonaId: this.patientPersonaId,
        therapistConfiguration: this.config.therapist,
        studyConfiguration: this.context.studyMetadata.studyConfiguration,
        environmentInfo: {
          platform: 'web',
          sessionId: this.context.id
        }
      };

      await databaseService.createConversation({
        id: this.context.id,
        status: 'active',
        config: this.config,
        sessionMetadata,
        patientPersonaId: this.patientPersonaId,
        therapistMode: 'multi-therapist',
        sessionType: 'comparative-study'
      });

      // Initialize multi-therapist session records
      await databaseService.storeAllPersonaMetrics(this.context.id, {
        cbtOnly: {
          conversationId: this.context.id,
          personaType: 'cbt-only',
          personaName: 'CBT-Only Therapist',
          strategyState: { currentApproach: 'CBT', hasSwitch: false, sessionPhase: 'opening' },
          performanceMetrics: { avgResponseTime: 0, techniqueEffectiveness: {}, patientEngagementScores: [], readinessImprovement: 0 }
        },
        miFixedPretreatment: {
          conversationId: this.context.id,
          personaType: 'mi-fixed-pretreatment',
          personaName: 'MI Fixed Pretreatment Therapist',
          strategyState: { currentApproach: 'MI', hasSwitch: false, sessionPhase: 'opening' },
          performanceMetrics: { avgResponseTime: 0, techniqueEffectiveness: {}, patientEngagementScores: [], readinessImprovement: 0 }
        },
        dynamicAdaptive: {
          conversationId: this.context.id,
          personaType: 'dynamic-adaptive',
          personaName: 'Dynamic Adaptive Therapist',
          strategyState: { currentApproach: 'MI', hasSwitch: false, sessionPhase: 'opening' },
          performanceMetrics: { avgResponseTime: 0, techniqueEffectiveness: {}, patientEngagementScores: [], readinessImprovement: 0 }
        }
      });

      console.log(`✅ Created multi-therapist conversation record in database: ${this.context.id}`);
    } catch (error) {
      console.error('❌ Failed to create multi-therapist conversation record:', error);
      // Continue with conversation even if database storage fails
    }

    // Broadcast conversation started event
    this.broadcastToClients({
      type: 'multi_conversation_started',
      data: {
        conversationId: this.context.id,
        studyMetadata: this.context.studyMetadata
      },
      timestamp: new Date().toISOString()
    });

    // Start with therapist greetings
    await this.processMultiTherapistTurn();
  }

  /**
   * Process multi-therapist turn (all three personas respond)
   */
  private async processMultiTherapistTurn(): Promise<void> {
    console.log('👩‍⚕️ Processing multi-therapist turn...');

    try {
      let multiResponse: MultiTherapistResponse;

      if (this.context.currentTurn === 0) {
        // Initial greetings from all personas
        multiResponse = await this.multiTherapistAgent.generateInitialGreetings();
      } else {
        // Responses to patient message
        const lastPatientMessage = this.getLastPatientMessage();
        if (!lastPatientMessage) {
          console.error('❌ No patient message found for therapist response');
          return;
        }
        multiResponse = await this.multiTherapistAgent.generateMultiTherapistResponse(
          lastPatientMessage.content,
          this.context
        );
      }

      // Create and store messages for each persona
      const therapistMessages = await this.createTherapistMessages(multiResponse);
      
      // Add messages to respective conversation contexts
      therapistMessages.forEach(msg => {
        const personaKey = this.getPersonaKey(msg.personaType!);
        this.context.therapistConversations[personaKey].messages.push({
          id: msg.id,
          conversationId: msg.conversationId,
          sender: 'therapist',
          content: msg.content,
          thinking: msg.thinking || '',
          metadata: msg.metadata || { confidence: 0, processingTime: 0 },
          timestamp: msg.timestamp
        });
        this.context.therapistConversations[personaKey].currentTurn++;
      });

      this.context.currentTurn++;

      // Broadcast multi-therapist messages
      this.broadcastMultiTherapistMessage(multiResponse, therapistMessages);

      // Schedule patient response if conversation should continue
      console.log(`🔍 Checking if conversation should continue: currentTurn=${this.context.currentTurn}, maxTurns=${this.context.maxTurns}, isActive=${this.isActive}`);
      if (this.shouldContinueConversation()) {
        this.currentTurn = 'patient';
        console.log(`⏰ Scheduling patient turn in ${this.config.conversation.responseDelay.patient}ms...`);
        setTimeout(() => {
          this.processPatientTurn();
        }, this.config.conversation.responseDelay.patient);
      } else {
        console.log('🛑 Conversation should not continue, ending...');
        await this.endConversation();
      }

    } catch (error) {
      console.error('❌ Error processing multi-therapist turn:', error);
      this.handleError('Failed to generate multi-therapist response');
    }
  }

  /**
   * Process patient turn (generates differentiated responses to all therapist personas)
   */
  private async processPatientTurn(): Promise<void> {
    console.log('👤 Processing differentiated patient turn...');

    try {
      // Get the last therapist messages from all three personas
      const lastCbtMessage = this.getLastTherapistMessage('cbtOnly');
      const lastMiMessage = this.getLastTherapistMessage('miFixedPretreatment');
      const lastDynamicMessage = this.getLastTherapistMessage('dynamicAdaptive');

      if (!lastCbtMessage || !lastMiMessage || !lastDynamicMessage) {
        console.error('❌ Missing therapist messages for patient response');
        return;
      }

      // Extract therapeutic approach information from therapist messages
      const therapistMessages = {
        cbtOnly: {
          content: lastCbtMessage.content,
          therapeuticApproach: lastCbtMessage.metadata.therapeuticApproach?.name || 'Cognitive Behavioral Therapy',
          technique: lastCbtMessage.metadata.therapeuticApproach?.selectedTechnique.name || 'CBT Technique'
        },
        miFixedPretreatment: {
          content: lastMiMessage.content,
          therapeuticApproach: lastMiMessage.metadata.therapeuticApproach?.name || 'Motivational Interviewing',
          technique: lastMiMessage.metadata.therapeuticApproach?.selectedTechnique.name || 'MI Technique'
        },
        dynamicAdaptive: {
          content: lastDynamicMessage.content,
          therapeuticApproach: lastDynamicMessage.metadata.therapeuticApproach?.name || 'Dynamic Adaptive',
          technique: lastDynamicMessage.metadata.therapeuticApproach?.selectedTechnique.name || 'Adaptive Technique'
        }
      };

      // Generate differentiated patient responses to all three personas
      const multiPatientResponse = await this.patientAgent.generateMultiTherapistResponse(
        therapistMessages,
        this.context.therapistConversations
      );

      // Create and add patient messages to each conversation context
      const patientMessages = {
        cbtOnly: await this.createPatientMessage(multiPatientResponse.cbtOnly),
        miFixedPretreatment: await this.createPatientMessage(multiPatientResponse.miFixedPretreatment),
        dynamicAdaptive: await this.createPatientMessage(multiPatientResponse.dynamicAdaptive)
      };

      // Add to patient messages (using dynamic adaptive as the "main" message for simplicity)
      this.context.patientMessages.push({
        id: patientMessages.dynamicAdaptive.id,
        conversationId: patientMessages.dynamicAdaptive.conversationId,
        content: patientMessages.dynamicAdaptive.content,
        timestamp: patientMessages.dynamicAdaptive.timestamp
      });

      // Add differentiated patient messages to their respective therapist conversation contexts
      Object.entries(this.context.therapistConversations).forEach(([personaKey, ctx]) => {
        const patientMessage = patientMessages[personaKey as keyof typeof patientMessages];
        ctx.messages.push({
          id: patientMessage.id,
          conversationId: ctx.id,
          sender: 'patient',
          content: patientMessage.content,
          thinking: patientMessage.thinking || '',
          metadata: patientMessage.metadata || { confidence: 0, processingTime: 0 },
          timestamp: patientMessage.timestamp
        });
        ctx.currentTurn++;
      });

      // Broadcast differentiated patient messages
      this.broadcastDifferentiatedPatientMessages(patientMessages);

      // Schedule multi-therapist response if conversation should continue
      if (this.shouldContinueConversation()) {
        this.currentTurn = 'therapist';
        setTimeout(() => {
          this.processMultiTherapistTurn();
        }, this.config.conversation.responseDelay.therapist);
      } else {
        await this.endConversation();
      }

    } catch (error) {
      console.error('❌ Error processing differentiated patient turn:', error);
      this.handleError('Failed to generate differentiated patient responses');
    }
  }

  private async createTherapistMessages(multiResponse: MultiTherapistResponse): Promise<MultiTherapistConversationMessage[]> {
    const timestamp = new Date().toISOString();

    const messages = [
      {
        id: uuidv4(),
        conversationId: this.context.id,
        sender: 'therapist' as const,
        content: multiResponse.cbtOnly.message,
        thinking: multiResponse.cbtOnly.thinking,
        metadata: multiResponse.cbtOnly.metadata,
        timestamp,
        personaType: 'cbt-only' as const,
        personaName: multiResponse.cbtOnly.personaName
      },
      {
        id: uuidv4(),
        conversationId: this.context.id,
        sender: 'therapist' as const,
        content: multiResponse.miFixedPretreatment.message,
        thinking: multiResponse.miFixedPretreatment.thinking,
        metadata: multiResponse.miFixedPretreatment.metadata,
        timestamp,
        personaType: 'mi-fixed-pretreatment' as const,
        personaName: multiResponse.miFixedPretreatment.personaName
      },
      {
        id: uuidv4(),
        conversationId: this.context.id,
        sender: 'therapist' as const,
        content: multiResponse.dynamicAdaptive.message,
        thinking: multiResponse.dynamicAdaptive.thinking,
        metadata: multiResponse.dynamicAdaptive.metadata,
        timestamp,
        personaType: 'dynamic-adaptive' as const,
        personaName: multiResponse.dynamicAdaptive.personaName
      }
    ];

    // Store each therapist message in database
    try {
      const storePromises = messages.map(async (msg) => {
        const messageData = {
          id: msg.id,
          conversationId: msg.conversationId,
          sender: msg.sender,
          content: msg.content,
          metadata: { ...msg.metadata, personaType: msg.personaType, personaName: msg.personaName },
          thinking: msg.thinking,
          processingTime: msg.metadata.processingTime,
          confidenceScore: msg.metadata.confidence
        };

        // Extract AI analysis and therapeutic approach from the response
        const personaResponse = msg.personaType === 'cbt-only' ? multiResponse.cbtOnly :
                              msg.personaType === 'mi-fixed-pretreatment' ? multiResponse.miFixedPretreatment :
                              multiResponse.dynamicAdaptive;

        await databaseService.storeMessage(
          messageData,
          personaResponse.metadata.patientAnalysis,
          personaResponse.metadata.therapeuticApproach
        );
      });

      await Promise.all(storePromises);
      console.log(`✅ Stored ${messages.length} therapist messages in database`);
    } catch (error) {
      console.error('❌ Failed to store therapist messages in database:', error);
      // Continue with conversation even if database storage fails
    }

    return messages;
  }

  private async createPatientMessage(response: PatientResponse): Promise<MultiTherapistConversationMessage> {
    const messageId = uuidv4();
    const timestamp = new Date().toISOString();

    const message = {
      id: messageId,
      conversationId: this.context.id,
      sender: 'patient' as const,
      content: response.message,
      thinking: response.thinking,
      metadata: response.metadata,
      timestamp
    };

    // Store patient message in database
    try {
      const messageData = {
        id: messageId,
        conversationId: this.context.id,
        sender: 'patient' as const,
        content: response.message,
        metadata: response.metadata,
        thinking: response.thinking,
        processingTime: response.metadata.processingTime,
        confidenceScore: response.metadata.confidence
      };

      await databaseService.storeMessage(messageData);
      console.log(`✅ Stored patient message in database: ${messageId}`);
    } catch (error) {
      console.error('❌ Failed to store patient message in database:', error);
      // Continue with conversation even if database storage fails
    }

    return message;
  }

  private getPersonaKey(personaType: TherapistPersonaType): 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive' {
    switch (personaType) {
      case 'cbt-only': return 'cbtOnly';
      case 'mi-fixed-pretreatment': return 'miFixedPretreatment';
      case 'dynamic-adaptive': return 'dynamicAdaptive';
      default: throw new Error(`Unknown persona type: ${personaType}`);
    }
  }

  private getLastPatientMessage() {
    return this.context.patientMessages[this.context.patientMessages.length - 1];
  }

  private getLastTherapistMessage(personaKey: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive') {
    const messages = this.context.therapistConversations[personaKey].messages;
    return messages.filter(m => m.sender === 'therapist').pop();
  }

  private shouldContinueConversation(): boolean {
    const shouldContinue = this.context.currentTurn < this.context.maxTurns && this.isActive;
    console.log(`🔍 shouldContinueConversation: currentTurn=${this.context.currentTurn}, maxTurns=${this.context.maxTurns}, isActive=${this.isActive}, result=${shouldContinue}`);
    return shouldContinue;
  }

  private async endConversation(): Promise<void> {
    console.log('🏁 Ending multi-therapist conversation...');
    
    this.isActive = false;
    this.context.status = 'completed';

    // Broadcast conversation ended
    this.broadcastToClients({
      type: 'multi_conversation_ended',
      data: {
        conversationId: this.context.id,
        totalTurns: this.context.currentTurn,
        studyMetadata: this.context.studyMetadata
      },
      timestamp: new Date().toISOString()
    });

    // Trigger comparative evaluation
    await this.performComparativeEvaluation();
  }

  private async performComparativeEvaluation(): Promise<void> {
    console.log('📊 Performing comparative CBT evaluation...');
    
    try {
      // Create evaluation requests for each persona
      const evaluationRequests = Object.entries(this.context.therapistConversations).map(([key, ctx]) => ({
        conversationId: ctx.id,
        messages: ctx.messages.map(m => ({
          sender: m.sender,
          content: m.content,
          timestamp: m.timestamp
        })),
        sessionMetadata: {
          duration: Math.round((Date.now() - new Date(this.context.studyMetadata.startTime).getTime()) / 60000),
          patientPersona: this.context.studyMetadata.patientPersonaId,
          therapeuticApproaches: [key]
        }
      }));

      // Perform evaluations in parallel
      const evaluations = await Promise.all(
        evaluationRequests.map(req => this.evaluationObserver.evaluateSession(req))
      );

      // Create comparative evaluation result
      const comparativeResult: ComparativeCBTEvaluationResult = {
        id: uuidv4(),
        conversationId: this.context.id,
        evaluationTimestamp: new Date().toISOString(),
        evaluations: {
          cbtOnly: evaluations[0],
          miFixedPretreatment: evaluations[1],
          dynamicAdaptive: evaluations[2]
        },
        comparison: this.generateComparison(evaluations),
        studyMetadata: {
          patientPersonaId: this.context.studyMetadata.patientPersonaId,
          sessionDuration: Math.round((Date.now() - new Date(this.context.studyMetadata.startTime).getTime()) / 60000),
          totalPatientMessages: this.context.patientMessages.length,
          readinessScoreProgression: {
            initial: 5, // Would be calculated from actual data
            final: 7,   // Would be calculated from actual data
            average: 6  // Would be calculated from actual data
          }
        }
      };

      // Broadcast comparative evaluation result
      this.broadcastToClients({
        type: 'comparative_evaluation_complete',
        data: comparativeResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error performing comparative evaluation:', error);
    }
  }

  private generateComparison(evaluations: any[]) {
    const scores = evaluations.map(e => e.overallScore);
    const bestIndex = scores.indexOf(Math.max(...scores));
    const personaTypes: TherapistPersonaType[] = ['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'];

    return {
      bestPerforming: personaTypes[bestIndex],
      scoreComparison: {
        cbtOnly: scores[0],
        miFixedPretreatment: scores[1],
        dynamicAdaptive: scores[2]
      },
      dimensionComparison: {}, // Would be populated with detailed dimension analysis
      insights: [
        `Best performing strategy: ${personaTypes[bestIndex]}`,
        `Score difference: ${Math.max(...scores) - Math.min(...scores)} points`
      ],
      recommendations: [
        'Consider the context-specific effectiveness of each approach',
        'Analyze patient readiness progression patterns'
      ]
    };
  }

  private broadcastMultiTherapistMessage(
    multiResponse: MultiTherapistResponse, 
    messages: MultiTherapistConversationMessage[]
  ): void {
    this.broadcastToClients({
      type: 'multi_therapist_message',
      data: {
        responses: {
          cbtOnly: {
            message: messages[0],
            thinking: {
              agent: 'therapist',
              content: messages[0].thinking,
              timestamp: messages[0].timestamp,
              personaType: 'cbt-only',
              personaName: messages[0].personaName
            }
          },
          miFixedPretreatment: {
            message: messages[1],
            thinking: {
              agent: 'therapist',
              content: messages[1].thinking,
              timestamp: messages[1].timestamp,
              personaType: 'mi-fixed-pretreatment',
              personaName: messages[1].personaName
            }
          },
          dynamicAdaptive: {
            message: messages[2],
            thinking: {
              agent: 'therapist',
              content: messages[2].thinking,
              timestamp: messages[2].timestamp,
              personaType: 'dynamic-adaptive',
              personaName: messages[2].personaName
            }
          }
        }
      },
      conversationId: this.context.id,
      timestamp: new Date().toISOString()
    });
  }

  private broadcastPatientMessage(message: MultiTherapistConversationMessage): void {
    this.broadcastToClients({
      type: 'patient_message',
      data: {
        message: {
          sender: message.sender,
          content: message.content,
          timestamp: message.timestamp
        },
        thinking: {
          agent: 'patient',
          content: message.thinking,
          timestamp: message.timestamp
        }
      },
      conversationId: this.context.id,
      timestamp: message.timestamp
    });
  }

  private broadcastDifferentiatedPatientMessages(patientMessages: {
    cbtOnly: MultiTherapistConversationMessage;
    miFixedPretreatment: MultiTherapistConversationMessage;
    dynamicAdaptive: MultiTherapistConversationMessage;
  }): void {
    this.broadcastToClients({
      type: 'differentiated_patient_messages',
      data: {
        cbtOnly: {
          message: {
            sender: patientMessages.cbtOnly.sender,
            content: patientMessages.cbtOnly.content,
            timestamp: patientMessages.cbtOnly.timestamp
          },
          thinking: {
            agent: 'patient',
            content: patientMessages.cbtOnly.thinking,
            timestamp: patientMessages.cbtOnly.timestamp
          }
        },
        miFixedPretreatment: {
          message: {
            sender: patientMessages.miFixedPretreatment.sender,
            content: patientMessages.miFixedPretreatment.content,
            timestamp: patientMessages.miFixedPretreatment.timestamp
          },
          thinking: {
            agent: 'patient',
            content: patientMessages.miFixedPretreatment.thinking,
            timestamp: patientMessages.miFixedPretreatment.timestamp
          }
        },
        dynamicAdaptive: {
          message: {
            sender: patientMessages.dynamicAdaptive.sender,
            content: patientMessages.dynamicAdaptive.content,
            timestamp: patientMessages.dynamicAdaptive.timestamp
          },
          thinking: {
            agent: 'patient',
            content: patientMessages.dynamicAdaptive.thinking,
            timestamp: patientMessages.dynamicAdaptive.timestamp
          }
        }
      },
      conversationId: this.context.id,
      timestamp: new Date().toISOString()
    });
  }

  private broadcastToClients(message: any): void {
    console.log(`📢 Broadcasting to ${this.clients.length} clients:`, message.type);
    this.clients.forEach(client => {
      if (client.socket.readyState === 1) { // WebSocket.OPEN
        client.socket.send(JSON.stringify(message));
      }
    });
  }

  private handleError(message: string): void {
    console.error(`❌ ${message}`);
    this.broadcastToClients({
      type: 'error',
      message,
      timestamp: new Date().toISOString()
    });
  }

  // Client management methods
  addClient(client: WebSocketClient): void {
    this.clients.push(client);
    client.conversationId = this.context.id;
    console.log(`👥 Client ${client.id} added to multi-therapist conversation ${this.context.id}`);
  }

  removeClient(clientId: string): void {
    this.clients = this.clients.filter(c => c.id !== clientId);
    console.log(`👥 Client ${clientId} removed from multi-therapist conversation ${this.context.id}`);
  }

  getContext(): MultiTherapistConversationContext {
    return this.context;
  }

  isConversationActive(): boolean {
    return this.isActive;
  }
}
