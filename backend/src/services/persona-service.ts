// Persona Service for MiCA Therapy Simulation
// Handles patient persona data and conversion to PatientConfig format

import { patientPersonas, DetailedPatientPersona } from '../data/patient-personas.js';
import { PatientConfig } from '../config/conversation.js';

export class PersonaService {
  /**
   * Get all available patient personas
   */
  static getAllPersonas(): DetailedPatientPersona[] {
    return patientPersonas;
  }

  /**
   * Get a specific persona by ID
   */
  static getPersonaById(id: string): DetailedPatientPersona | null {
    return patientPersonas.find(persona => persona.id === id) || null;
  }

  /**
   * Convert a DetailedPatientPersona to PatientConfig format
   */
  static convertToPatientConfig(persona: DetailedPatientPersona): PatientConfig {
    // Map primary emotion to primaryMood
    const primaryMoodMap: { [key: string]: PatientConfig['emotionalState']['primaryMood'] } = {
      'anxious': 'anxious',
      'worried': 'anxious',
      'depressed': 'depressed',
      'hopeless': 'depressed',
      'fearful': 'anxious',
      'frustrated': 'frustrated',
      'confused': 'frustrated',
      'angry': 'angry'
    };

    const primaryEmotion = persona.emotions.primary[0] || 'neutral';
    const primaryMood = primaryMoodMap[primaryEmotion] || 'neutral';

    // Map emotional intensity to energy level
    const getEnergyLevel = (): 'low' | 'medium' | 'high' => {
      if (primaryEmotion === 'depressed' || primaryEmotion === 'hopeless') return 'low';
      if (primaryEmotion === 'anxious' || primaryEmotion === 'fearful') return 'medium';
      return 'medium';
    };

    // Determine openness level based on persona characteristics
    const getOpennessLevel = (): 'closed' | 'guarded' | 'open' | 'very_open' => {
      if (persona.conversationalStyle.includes('guarded') || persona.conversationalStyle.includes('withdrawn')) {
        return 'guarded';
      }
      if (persona.conversationalStyle.includes('hesitant') || persona.conversationalStyle.includes('minimizes')) {
        return 'guarded';
      }
      if (persona.conversationalStyle.includes('expressive') || persona.conversationalStyle.includes('animated')) {
        return 'open';
      }
      return 'guarded'; // Default for therapy context
    };

    // Determine trust level based on session context and persona
    const getTrustLevel = (): number => {
      if (persona.sessionContext.includes('First session')) return 30;
      if (persona.sessionContext.includes('Second session')) return 40;
      if (persona.sessionContext.includes('Third session')) return 50;
      if (persona.sessionContext.includes('Fourth session')) return 60;
      if (persona.sessionContext.includes('Sixth session')) return 70;
      return 45; // Default
    };

    // Determine motivation level
    const getMotivationLevel = (): 'low' | 'medium' | 'high' => {
      if (persona.sessionContext.includes('reluctant') || persona.sessionContext.includes('mandated')) {
        return 'low';
      }
      if (persona.sessionContext.includes('engaged') || persona.sessionContext.includes('accepting')) {
        return 'medium';
      }
      return 'medium'; // Default
    };

    // Map response tendency
    const getResponseTendency = (): 'withdrawn' | 'talkative' | 'defensive' | 'cooperative' => {
      if (persona.conversationalStyle.includes('withdrawn') || persona.conversationalStyle.includes('quiet')) {
        return 'withdrawn';
      }
      if (persona.conversationalStyle.includes('defensive')) {
        return 'defensive';
      }
      if (persona.conversationalStyle.includes('expressive') || persona.conversationalStyle.includes('animated')) {
        return 'talkative';
      }
      return 'cooperative';
    };

    return {
      id: persona.id,
      persona: {
        name: persona.name,
        age: persona.age,
        background: persona.background,
        currentSituation: persona.currentSituation,
        personalityTraits: [] // Will be derived from conversational style
      },
      emotionalState: {
        primaryMood,
        energyLevel: getEnergyLevel(),
        openness: getOpennessLevel(),
        trustLevel: getTrustLevel(),
        motivationLevel: getMotivationLevel()
      },
      concerns: persona.presentingConcerns,
      backstory: persona.relevantHistory,
      responsePatterns: {
        tendency: getResponseTendency(),
        emotionalExpression: 'moderate', // Default
        detailLevel: 'moderate' // Default
      },
      // Enhanced persona data
      relevantHistory: persona.relevantHistory,
      cognitiveConceptualization: persona.cognitiveConceptualization,
      automaticThoughts: persona.automaticThoughts,
      emotions: persona.emotions,
      behaviors: persona.behaviors,
      conversationalStyle: persona.conversationalStyle,
      presentingConcerns: persona.presentingConcerns,
      sessionContext: persona.sessionContext,
      prompts: {
        systemPrompt: `You are ${persona.name}, a ${persona.age}-year-old individual seeking therapy. ${persona.background}

Your current situation: ${persona.currentSituation}

Your characteristics and background:
${persona.relevantHistory}

Your presenting concerns:
${persona.presentingConcerns.join(', ')}

Your conversational style: ${persona.conversationalStyle}

Respond naturally as ${persona.name} would, showing gradual trust-building with the therapist.`,

        responseTemplate: `The therapist just said: "{therapistMessage}"

As ${persona.name}, consider:
- Your current emotional state and comfort level
- How much you're willing to share at this point
- Your personality and conversational style
- Your automatic thoughts and emotional reactions

Respond authentically as ${persona.name} would.`,

        emotionalStatePrompt: `Describe ${persona.name}'s current emotional state and what they might be thinking or feeling internally.`,
        thinkingPrompt: `What is going through ${persona.name}'s mind right now? Include internal thoughts, hesitations, or reactions they might not express directly.`
      }
    };
  }

  /**
   * Get persona summary for frontend display
   */
  static getPersonaSummary(persona: DetailedPatientPersona) {
    return {
      id: persona.id,
      name: persona.name,
      age: persona.age,
      background: persona.background,
      primaryConcerns: persona.presentingConcerns.slice(0, 3), // Top 3 concerns
      sessionContext: persona.sessionContext,
      primaryEmotions: persona.emotions.primary,
      coreBeliefs: persona.cognitiveConceptualization.coreBeliefs.slice(0, 2) // Top 2 core beliefs
    };
  }

  /**
   * Get all persona summaries for frontend selection
   */
  static getAllPersonaSummaries() {
    return patientPersonas.map(persona => this.getPersonaSummary(persona));
  }
}
