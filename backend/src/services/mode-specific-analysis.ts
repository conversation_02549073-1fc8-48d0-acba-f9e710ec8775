// Mode-Specific Client State Analysis Service
// Provides distinct analytical frameworks for CBT, DBT, and psychodynamic therapeutic modes

import { 
  CBTClientStateAnalysis, 
  DBTClientStateAnalysis, 
  PsychodynamicClientStateAnalysis,
  TherapeuticMode,
  EnhancedPatientAnalysis,
  PatientAnalysis
} from '../types/index.js';
import { OpenAIService } from './openai.js';
import { 
  openaiLogger,
  logFunctionEntry,
  logFunctionSuccess,
  logFunctionError
} from '../utils/logger.js';

export class ModeSpecificAnalysisService {
  private openaiService: OpenAIService;

  constructor(openaiService: OpenAIService) {
    this.openaiService = openaiService;
  }

  /**
   * Perform mode-specific client state analysis
   */
  async analyzeClientState(
    message: string, 
    mode: TherapeuticMode,
    baseAnalysis: PatientAnalysis
  ): Promise<EnhancedPatientAnalysis> {
    const operation = 'analyzeClientState';
    
    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length,
      mode,
      sentiment: baseAnalysis.sentiment,
      motivationLevel: baseAnalysis.motivationLevel
    });

    try {
      let modeSpecificAnalysis;

      switch (mode) {
        case 'CBT':
          modeSpecificAnalysis = await this.analyzeCBTClientState(message, baseAnalysis);
          break;
        case 'DBT':
          modeSpecificAnalysis = await this.analyzeDBTClientState(message, baseAnalysis);
          break;
        case 'psychodynamic':
          modeSpecificAnalysis = await this.analyzePsychodynamicClientState(message, baseAnalysis);
          break;
        default:
          throw new Error(`Unknown therapeutic mode: ${mode}`);
      }

      const enhancedAnalysis: EnhancedPatientAnalysis = {
        ...baseAnalysis,
        modeSpecificAnalysis,
        therapistMode: mode
      };

      logFunctionSuccess(openaiLogger, operation, {
        mode,
        analysisCompleted: true
      });

      return enhancedAnalysis;
    } catch (error) {
      logFunctionError(openaiLogger, operation, error as Error);
      throw error;
    }
  }

  /**
   * CBT-specific client state analysis
   * Focuses on cognitive patterns, thought distortions, behavioral triggers, and mood tracking
   */
  private async analyzeCBTClientState(
    message: string, 
    baseAnalysis: PatientAnalysis
  ): Promise<CBTClientStateAnalysis> {
    const operation = 'analyzeCBTClientState';
    
    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length
    });

    try {
      // Analyze cognitive patterns in parallel
      const [thoughtDistortions, automaticThoughts, cognitiveFlexibility, insightLevel] = await Promise.all([
        this.identifyThoughtDistortions(message),
        this.identifyAutomaticThoughts(message),
        this.assessCognitiveFlexibility(message),
        this.assessInsightLevel(message)
      ]);

      // Analyze behavioral aspects in parallel
      const [behavioralTriggers, avoidanceBehaviors, copingStrategies, behavioralActivation] = await Promise.all([
        this.identifyBehavioralTriggers(message),
        this.identifyAvoidanceBehaviors(message),
        this.identifyCopingStrategies(message),
        this.assessBehavioralActivation(message)
      ]);

      // Analyze mood and readiness
      const [moodTriggers, homeworkCompliance, structuredApproachAcceptance, goalOrientation] = await Promise.all([
        this.identifyMoodTriggers(message),
        this.assessHomeworkCompliance(message),
        this.assessStructuredApproachAcceptance(message),
        this.assessGoalOrientation(message)
      ]);

      const analysis: CBTClientStateAnalysis = {
        cognitivePatterns: {
          thoughtDistortions,
          automaticThoughts,
          cognitiveFlexibility,
          insightLevel
        },
        behavioralTriggers: {
          identifiedTriggers: behavioralTriggers,
          avoidanceBehaviors,
          copingStrategies,
          behavioralActivation
        },
        moodTracking: {
          currentMood: this.mapSentimentToMood(baseAnalysis.sentiment),
          moodStability: this.assessMoodStability(message),
          moodTriggers
        },
        readinessForCBT: {
          homeworkCompliance,
          structuredApproachAcceptance,
          goalOrientation
        }
      };

      logFunctionSuccess(openaiLogger, operation, {
        thoughtDistortionsCount: thoughtDistortions.length,
        behavioralTriggersCount: behavioralTriggers.length,
        cognitiveFlexibility,
        insightLevel
      });

      return analysis;
    } catch (error) {
      logFunctionError(openaiLogger, operation, error as Error);
      throw error;
    }
  }

  /**
   * DBT-specific client state analysis
   * Emphasizes emotional regulation, distress tolerance, interpersonal effectiveness, and mindfulness
   */
  private async analyzeDBTClientState(
    message: string, 
    baseAnalysis: PatientAnalysis
  ): Promise<DBTClientStateAnalysis> {
    const operation = 'analyzeDBTClientState';
    
    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length
    });

    try {
      // Analyze emotional regulation in parallel
      const [emotionalIntensity, emotionalLability, regulationSkills, dysregulationTriggers] = await Promise.all([
        this.assessEmotionalIntensity(message),
        this.assessEmotionalLability(message),
        this.identifyRegulationSkills(message),
        this.identifyDysregulationTriggers(message)
      ]);

      // Analyze distress tolerance in parallel
      const [currentDistressLevel, toleranceCapacity, crisisSkillsUsage, selfHarmRisk] = await Promise.all([
        this.assessCurrentDistressLevel(message),
        this.assessToleranceCapacity(message),
        this.identifyCrisisSkillsUsage(message),
        this.assessSelfHarmRisk(message)
      ]);

      // Analyze interpersonal effectiveness in parallel
      const [relationshipPatterns, boundaryIssues, communicationStyle, interpersonalGoals] = await Promise.all([
        this.identifyRelationshipPatterns(message),
        this.identifyBoundaryIssues(message),
        this.assessCommunicationStyle(message),
        this.identifyInterpersonalGoals(message)
      ]);

      // Analyze mindfulness states in parallel
      const [presentMomentAwareness, mindfulnessSkills, dissociationLevel, wiseMindAccess] = await Promise.all([
        this.assessPresentMomentAwareness(message),
        this.identifyMindfulnessSkills(message),
        this.assessDissociationLevel(message),
        this.assessWiseMindAccess(message)
      ]);

      const analysis: DBTClientStateAnalysis = {
        emotionalRegulation: {
          emotionalIntensity,
          emotionalLability,
          regulationSkills,
          dysregulationTriggers
        },
        distressTolerance: {
          currentDistressLevel,
          toleranceCapacity,
          crisisSkillsUsage,
          selfHarmRisk
        },
        interpersonalEffectiveness: {
          relationshipPatterns,
          boundaryIssues,
          communicationStyle,
          interpersonalGoals
        },
        mindfulnessStates: {
          presentMomentAwareness,
          mindfulnessSkills,
          dissociationLevel,
          wiseMindAccess
        }
      };

      logFunctionSuccess(openaiLogger, operation, {
        emotionalIntensity,
        currentDistressLevel,
        communicationStyle,
        presentMomentAwareness
      });

      return analysis;
    } catch (error) {
      logFunctionError(openaiLogger, operation, error as Error);
      throw error;
    }
  }

  /**
   * Psychodynamic-specific client state analysis
   * Focuses on unconscious patterns, defense mechanisms, transference dynamics, and relational themes
   */
  private async analyzePsychodynamicClientState(
    message: string, 
    baseAnalysis: PatientAnalysis
  ): Promise<PsychodynamicClientStateAnalysis> {
    const operation = 'analyzePsychodynamicClientState';
    
    logFunctionEntry(openaiLogger, operation, {
      messageLength: message.length
    });

    try {
      // Analyze unconscious patterns in parallel
      const [repeatingThemes, conflictualPatterns, unconsciousMotivations, symbolism] = await Promise.all([
        this.identifyRepeatingThemes(message),
        this.identifyConflictualPatterns(message),
        this.identifyUnconsciousMotivations(message),
        this.identifySymbolism(message)
      ]);

      // Analyze defense mechanisms in parallel
      const [primaryDefenses, adaptiveDefenses, maladaptiveDefenses, defenseFlexibility] = await Promise.all([
        this.identifyPrimaryDefenses(message),
        this.identifyAdaptiveDefenses(message),
        this.identifyMaladaptiveDefenses(message),
        this.assessDefenseFlexibility(message)
      ]);

      // Analyze transference dynamics in parallel
      const [transferencePatterns, countertransferenceIndicators, relationshipProjections, therapeuticAllianceQuality] = await Promise.all([
        this.identifyTransferencePatterns(message),
        this.identifyCountertransferenceIndicators(message),
        this.identifyRelationshipProjections(message),
        this.assessTherapeuticAllianceQuality(message)
      ]);

      // Analyze relational themes in parallel
      const [attachmentStyle, interpersonalPatterns, earlyRelationalExperiences, currentRelationalDynamics] = await Promise.all([
        this.assessAttachmentStyle(message),
        this.identifyInterpersonalPatterns(message),
        this.identifyEarlyRelationalExperiences(message),
        this.identifyCurrentRelationalDynamics(message)
      ]);

      const analysis: PsychodynamicClientStateAnalysis = {
        unconsciousPatterns: {
          repeatingThemes,
          conflictualPatterns,
          unconsciousMotivations,
          symbolism
        },
        defenseMechanisms: {
          primaryDefenses,
          adaptiveDefenses,
          maladaptiveDefenses,
          defenseFlexibility
        },
        transferencedynamics: {
          transferencePatterns,
          countertransferenceIndicators,
          relationshipProjections,
          therapeuticAllianceQuality
        },
        relationalThemes: {
          attachmentStyle,
          interpersonalPatterns,
          earlyRelationalExperiences,
          currentRelationalDynamics
        }
      };

      logFunctionSuccess(openaiLogger, operation, {
        repeatingThemesCount: repeatingThemes.length,
        primaryDefensesCount: primaryDefenses.length,
        attachmentStyle,
        therapeuticAllianceQuality
      });

      return analysis;
    } catch (error) {
      logFunctionError(openaiLogger, operation, error as Error);
      throw error;
    }
  }

  // CBT Analysis Helper Methods
  private async identifyThoughtDistortions(message: string): Promise<string[]> {
    const prompt = `Analyze this patient message for cognitive distortions common in CBT. Identify specific thought distortions present (e.g., all-or-nothing thinking, catastrophizing, mind reading, fortune telling, emotional reasoning, should statements, labeling, personalization, mental filter, disqualifying the positive). Return only the distortion names as a comma-separated list, or "none" if no clear distortions are present.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_completion_tokens: 100
      });

      const distortions = response.message.trim().toLowerCase();
      if (distortions === 'none') return [];
      return distortions.split(',').map((d: string) => d.trim()).filter((d: string) => d.length > 0);
    } catch (error) {
      console.error('Error identifying thought distortions:', error);
      return [];
    }
  }

  private async identifyAutomaticThoughts(message: string): Promise<string[]> {
    const prompt = `Identify automatic thoughts expressed or implied in this patient message. Look for immediate, spontaneous thoughts that seem to pop into the patient's mind. Extract the specific thoughts as they might appear in the patient's internal dialogue. Return as a comma-separated list, or "none" if no clear automatic thoughts are present.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_completion_tokens: 150
      });

      const thoughts = response.message.trim().toLowerCase();
      if (thoughts === 'none') return [];
      return thoughts.split(',').map((t: string) => t.trim()).filter((t: string) => t.length > 0);
    } catch (error) {
      console.error('Error identifying automatic thoughts:', error);
      return [];
    }
  }

  private async assessCognitiveFlexibility(message: string): Promise<'low' | 'medium' | 'high'> {
    const prompt = `Assess the cognitive flexibility shown in this patient message. Consider: ability to see alternative perspectives, willingness to consider different viewpoints, openness to changing thoughts, ability to think in nuanced ways rather than black-and-white. Respond with only one word: low, medium, or high.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_completion_tokens: 10
      });

      const flexibility = response.message.trim().toLowerCase();
      if (['low', 'medium', 'high'].includes(flexibility)) {
        return flexibility as 'low' | 'medium' | 'high';
      }
      return 'medium';
    } catch (error) {
      console.error('Error assessing cognitive flexibility:', error);
      return 'medium';
    }
  }

  private async assessInsightLevel(message: string): Promise<'low' | 'medium' | 'high'> {
    const prompt = `Assess the level of psychological insight shown in this patient message. Consider: self-awareness, understanding of patterns, recognition of connections between thoughts/feelings/behaviors, ability to reflect on internal processes. Respond with only one word: low, medium, or high.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_completion_tokens: 10
      });

      const insight = response.message.trim().toLowerCase();
      if (['low', 'medium', 'high'].includes(insight)) {
        return insight as 'low' | 'medium' | 'high';
      }
      return 'medium';
    } catch (error) {
      console.error('Error assessing insight level:', error);
      return 'medium';
    }
  }

  private async identifyBehavioralTriggers(message: string): Promise<string[]> {
    const prompt = `Identify behavioral triggers mentioned or implied in this patient message. Look for situations, people, places, thoughts, or feelings that seem to trigger problematic behaviors or emotional responses. Return as a comma-separated list, or "none" if no clear triggers are present.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_completion_tokens: 100
      });

      const triggers = response.message.trim().toLowerCase();
      if (triggers === 'none') return [];
      return triggers.split(',').map((t: string) => t.trim()).filter((t: string) => t.length > 0);
    } catch (error) {
      console.error('Error identifying behavioral triggers:', error);
      return [];
    }
  }

  private async identifyAvoidanceBehaviors(message: string): Promise<string[]> {
    const prompt = `Identify avoidance behaviors mentioned or implied in this patient message. Look for ways the patient avoids situations, people, feelings, or activities. Include both active avoidance (doing something to avoid) and passive avoidance (not doing something). Return as a comma-separated list, or "none" if no clear avoidance behaviors are present.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_completion_tokens: 100
      });

      const behaviors = response.message.trim().toLowerCase();
      if (behaviors === 'none') return [];
      return behaviors.split(',').map((b: string) => b.trim()).filter((b: string) => b.length > 0);
    } catch (error) {
      console.error('Error identifying avoidance behaviors:', error);
      return [];
    }
  }

  private async identifyCopingStrategies(message: string): Promise<string[]> {
    const prompt = `Identify coping strategies mentioned or implied in this patient message. Look for ways the patient manages stress, difficult emotions, or challenging situations. Include both healthy and unhealthy coping mechanisms. Return as a comma-separated list, or "none" if no clear coping strategies are present.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
        max_completion_tokens: 100
      });

      const strategies = response.message.trim().toLowerCase();
      if (strategies === 'none') return [];
      return strategies.split(',').map((s: string) => s.trim()).filter((s: string) => s.length > 0);
    } catch (error) {
      console.error('Error identifying coping strategies:', error);
      return [];
    }
  }

  private async assessBehavioralActivation(message: string): Promise<'low' | 'medium' | 'high'> {
    const prompt = `Assess the level of behavioral activation shown in this patient message. Consider: engagement in activities, willingness to try new things, energy for action, motivation to participate in life. Respond with only one word: low, medium, or high.

Patient message: "${message}"`;

    try {
      const response = await this.openaiService.generateResponse({
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.1,
        max_completion_tokens: 10
      });

      const activation = response.message.trim().toLowerCase();
      if (['low', 'medium', 'high'].includes(activation)) {
        return activation as 'low' | 'medium' | 'high';
      }
      return 'medium';
    } catch (error) {
      console.error('Error assessing behavioral activation:', error);
      return 'medium';
    }
  }

  // Additional helper methods will be implemented in separate files
  // to keep this file manageable and focused on core analysis logic

  // Placeholder methods for remaining CBT analysis functions
  private identifyMoodTriggers(message: string): Promise<string[]> {
    // Implementation will be added
    return Promise.resolve([]);
  }

  private assessHomeworkCompliance(message: string): Promise<'low' | 'medium' | 'high'> {
    return Promise.resolve('medium');
  }

  private assessStructuredApproachAcceptance(message: string): Promise<'low' | 'medium' | 'high'> {
    return Promise.resolve('medium');
  }

  private assessGoalOrientation(message: string): Promise<'low' | 'medium' | 'high'> {
    return Promise.resolve('medium');
  }

  private mapSentimentToMood(sentiment: 'positive' | 'negative' | 'neutral'): 'depressed' | 'anxious' | 'neutral' | 'elevated' | 'mixed' {
    switch (sentiment) {
      case 'positive': return 'elevated';
      case 'negative': return 'depressed';
      case 'neutral': return 'neutral';
      default: return 'neutral';
    }
  }

  private assessMoodStability(message: string): 'stable' | 'fluctuating' | 'volatile' {
    // Simple implementation - can be enhanced later
    return 'stable';
  }

  // Placeholder methods for DBT analysis functions
  private assessEmotionalIntensity(message: string): Promise<'low' | 'medium' | 'high' | 'extreme'> {
    return Promise.resolve('medium');
  }

  private assessEmotionalLability(message: string): Promise<'stable' | 'moderate' | 'high'> {
    return Promise.resolve('stable');
  }

  private identifyRegulationSkills(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyDysregulationTriggers(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessCurrentDistressLevel(message: string): Promise<'low' | 'medium' | 'high' | 'crisis'> {
    return Promise.resolve('medium');
  }

  private assessToleranceCapacity(message: string): Promise<'low' | 'medium' | 'high'> {
    return Promise.resolve('medium');
  }

  private identifyCrisisSkillsUsage(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessSelfHarmRisk(message: string): Promise<'none' | 'low' | 'medium' | 'high'> {
    return Promise.resolve('none');
  }

  private identifyRelationshipPatterns(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyBoundaryIssues(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessCommunicationStyle(message: string): Promise<'passive' | 'aggressive' | 'assertive' | 'passive-aggressive'> {
    return Promise.resolve('assertive');
  }

  private identifyInterpersonalGoals(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessPresentMomentAwareness(message: string): Promise<'low' | 'medium' | 'high'> {
    return Promise.resolve('medium');
  }

  private identifyMindfulnessSkills(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessDissociationLevel(message: string): Promise<'none' | 'mild' | 'moderate' | 'severe'> {
    return Promise.resolve('none');
  }

  private assessWiseMindAccess(message: string): Promise<'low' | 'medium' | 'high'> {
    return Promise.resolve('medium');
  }

  // Placeholder methods for Psychodynamic analysis functions
  private identifyRepeatingThemes(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyConflictualPatterns(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyUnconsciousMotivations(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifySymbolism(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyPrimaryDefenses(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyAdaptiveDefenses(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyMaladaptiveDefenses(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessDefenseFlexibility(message: string): Promise<'rigid' | 'moderate' | 'flexible'> {
    return Promise.resolve('moderate');
  }

  private identifyTransferencePatterns(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyCountertransferenceIndicators(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyRelationshipProjections(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private assessTherapeuticAllianceQuality(message: string): Promise<'poor' | 'developing' | 'good' | 'strong'> {
    return Promise.resolve('good');
  }

  private assessAttachmentStyle(message: string): Promise<'secure' | 'anxious' | 'avoidant' | 'disorganized'> {
    return Promise.resolve('secure');
  }

  private identifyInterpersonalPatterns(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyEarlyRelationalExperiences(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }

  private identifyCurrentRelationalDynamics(message: string): Promise<string[]> {
    return Promise.resolve([]);
  }
}
