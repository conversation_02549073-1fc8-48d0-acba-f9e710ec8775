// Multi-Therapist Agent Service for Comparative Study
// Enhanced with LangGraph workflow integration for Phase 1
import { OpenAIService } from '../openai.js';
import {
  MultiTherapistResponse,
  TherapistPersonaResponse,
  PatientAnalysis,
  ConversationContext,
  TherapistPersonaType,
  MultiTherapistConversationContext
} from '../../types/index.js';
import { TherapistConfig, defaultConversationConfig } from '../../config/conversation.js';
import {
  BaseTherapistPersonaStrategy,
  createTherapistPersonaStrategy,
  PersonaStrategyContext
} from '../therapist-persona-strategies.js';
import { TemplateRenderer } from '../template-renderer.js';
import { TechniqueSelector } from '../technique-selector.js';
// LangGraph integration imports
import { TherapeuticWorkflowService } from '../langgraph/therapeutic-workflow.js';
import { TherapeuticWorkflowConfig } from '../langgraph/workflow-state.js';
import { generateWorkflowConfig } from '../langgraph/workflow-utils.js';
import { TherapistPersonaStrategies } from '../langgraph/workflow-nodes.js';

export class MultiTherapistAgentService {
  private openaiService: OpenAIService;
  private config: TherapistConfig;
  private templateRenderer: TemplateRenderer;
  private techniqueSelector: TechniqueSelector;

  // Three therapist persona strategies
  private strategies: {
    cbtOnly: BaseTherapistPersonaStrategy;
    miFixedPretreatment: BaseTherapistPersonaStrategy;
    dynamicAdaptive: BaseTherapistPersonaStrategy;
  };

  // Track previous techniques for each persona
  private previousTechniques: {
    cbtOnly: string[];
    miFixedPretreatment: string[];
    dynamicAdaptive: string[];
  };

  // LangGraph workflow service for enhanced orchestration
  private workflowService: TherapeuticWorkflowService;
  private useWorkflow: boolean;

  constructor(
    openaiService: OpenAIService,
    config?: Partial<TherapistConfig>,
    options?: { useWorkflow?: boolean }
  ) {
    this.openaiService = openaiService;
    this.config = { ...defaultConversationConfig.therapist, ...config };
    this.templateRenderer = new TemplateRenderer();
    this.techniqueSelector = new TechniqueSelector();

    // Initialize the three therapist persona strategies
    this.strategies = {
      cbtOnly: createTherapistPersonaStrategy('cbt-only'),
      miFixedPretreatment: createTherapistPersonaStrategy('mi-fixed-pretreatment', {
        cbtThreshold: this.config.therapeuticApproaches.readinessThresholds.cbtMinimum
      }),
      dynamicAdaptive: createTherapistPersonaStrategy('dynamic-adaptive', {
        cbtThreshold: this.config.therapeuticApproaches.readinessThresholds.cbtMinimum,
        miThreshold: this.config.therapeuticApproaches.readinessThresholds.miMaximum
      })
    };

    // Initialize technique tracking
    this.previousTechniques = {
      cbtOnly: [],
      miFixedPretreatment: [],
      dynamicAdaptive: []
    };

    // Initialize LangGraph workflow service
    this.useWorkflow = options?.useWorkflow ?? true; // Default to using workflow
    if (this.useWorkflow) {
      // Create strategies object compatible with workflow service
      const workflowStrategies: TherapistPersonaStrategies = {
        'cbt-only': this.strategies.cbtOnly,
        'mi-fixed-pretreatment': this.strategies.miFixedPretreatment,
        'dynamic-adaptive': this.strategies.dynamicAdaptive
      };

      this.workflowService = new TherapeuticWorkflowService(
        this.openaiService,
        workflowStrategies
      );
    }
  }

  /**
   * Generate responses from all three therapist personas for a patient message
   * Enhanced with LangGraph workflow integration
   */
  async generateMultiTherapistResponse(
    patientMessage: string,
    conversationContext: MultiTherapistConversationContext,
    workflowConfig?: Partial<TherapeuticWorkflowConfig>
  ): Promise<MultiTherapistResponse> {
    console.log('🧠 Generating multi-therapist response...');

    try {
      // Use LangGraph workflow if enabled, otherwise fall back to original implementation
      if (this.useWorkflow && this.workflowService) {
        console.log('🔄 Using LangGraph workflow for multi-therapist response generation');

        // Generate workflow configuration
        const config = generateWorkflowConfig(conversationContext, workflowConfig);

        // Process through LangGraph workflow
        const workflowResponse = await this.workflowService.processPatientMessage(
          patientMessage,
          conversationContext,
          config
        );

        console.log('✅ LangGraph workflow completed successfully');
        return workflowResponse;
      } else {
        console.log('🔄 Using original implementation for multi-therapist response generation');
        return await this.generateMultiTherapistResponseOriginal(patientMessage, conversationContext);
      }

    } catch (error) {
      console.error('❌ Error generating multi-therapist response:', error);

      // Fallback to original implementation if workflow fails
      if (this.useWorkflow) {
        console.log('🔄 Falling back to original implementation due to workflow error');
        try {
          return await this.generateMultiTherapistResponseOriginal(patientMessage, conversationContext);
        } catch (fallbackError) {
          console.error('❌ Fallback implementation also failed:', fallbackError);
          throw fallbackError;
        }
      }

      throw error;
    }
  }

  /**
   * Original multi-therapist response generation (preserved for fallback)
   */
  private async generateMultiTherapistResponseOriginal(
    patientMessage: string,
    conversationContext: MultiTherapistConversationContext
  ): Promise<MultiTherapistResponse> {
    // Perform comprehensive patient analysis (shared across all personas)
    const patientAnalysis = await this.openaiService.analyzePatient(patientMessage);

    // Create strategy context for all personas
    const strategyContext: PersonaStrategyContext = {
      messageCount: conversationContext.currentTurn,
      previousTechniques: [], // Will be set per persona
      sessionPhase: this.getSessionPhase(conversationContext.currentTurn),
      conversationHistory: this.buildConversationHistory(conversationContext)
    };

    // Generate responses from all three personas in parallel
    const [cbtOnlyResponse, miFixedResponse, dynamicResponse] = await Promise.all([
      this.generatePersonaResponse(
        'cbtOnly',
        patientMessage,
        patientAnalysis,
        { ...strategyContext, previousTechniques: this.previousTechniques.cbtOnly },
        conversationContext.therapistConversations.cbtOnly
      ),
      this.generatePersonaResponse(
        'miFixedPretreatment',
        patientMessage,
        patientAnalysis,
        { ...strategyContext, previousTechniques: this.previousTechniques.miFixedPretreatment },
        conversationContext.therapistConversations.miFixedPretreatment
      ),
      this.generatePersonaResponse(
        'dynamicAdaptive',
        patientMessage,
        patientAnalysis,
        { ...strategyContext, previousTechniques: this.previousTechniques.dynamicAdaptive },
        conversationContext.therapistConversations.dynamicAdaptive
      )
    ]);

    return {
      cbtOnly: cbtOnlyResponse,
      miFixedPretreatment: miFixedResponse,
      dynamicAdaptive: dynamicResponse,
      patientMessage,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Generate response from a specific therapist persona
   */
  private async generatePersonaResponse(
    personaKey: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive',
    patientMessage: string,
    patientAnalysis: PatientAnalysis,
    strategyContext: PersonaStrategyContext,
    conversationContext: ConversationContext
  ): Promise<TherapistPersonaResponse> {
    const strategy = this.strategies[personaKey];
    
    // Select therapeutic approach and technique based on persona strategy
    const therapeuticApproachWithState = strategy.selectApproachAndTechnique(
      patientAnalysis,
      strategyContext
    );

    // Update technique tracking
    this.previousTechniques[personaKey].push(therapeuticApproachWithState.selectedTechnique.id);

    // Get technique-specific prompt
    const techniquePrompt = this.getTechniquePrompt(therapeuticApproachWithState.selectedTechnique.id);

    // Build system prompt with therapeutic approach
    const systemPrompt = this.buildSystemPromptWithApproach(
      patientAnalysis,
      therapeuticApproachWithState,
      techniquePrompt,
      strategy.name
    );

    // Build conversation messages for this persona
    const conversationMessages = this.buildConversationMessages(patientMessage, conversationContext);

    // Generate the main response using therapeutic approach
    const response = await this.openaiService.generateResponse({
      messages: [
        { role: 'system', content: systemPrompt },
        ...conversationMessages
      ],
      temperature: 0.7,
      max_completion_tokens: 1000
    });

    // Generate thinking process
    const thinking = await this.openaiService.generateThinking(
      'therapist',
      `Patient said: "${patientMessage}". Using ${therapeuticApproachWithState.name} with ${therapeuticApproachWithState.selectedTechnique.name} technique.`,
      `Analyzing patient readiness (${patientAnalysis.readinessScore.score}/10) and selecting appropriate therapeutic response as ${strategy.name}.`
    );

    return {
      message: response.message,
      thinking: thinking,
      metadata: {
        ...response.metadata,
        patientAnalysis,
        therapeuticApproach: {
          id: therapeuticApproachWithState.id,
          name: therapeuticApproachWithState.name,
          selectedTechnique: therapeuticApproachWithState.selectedTechnique
        }
      },
      personaType: strategy.personaType,
      personaName: strategy.name,
      strategyState: therapeuticApproachWithState.strategyState
    };
  }

  /**
   * Generate initial greetings from all three personas
   */
  async generateInitialGreetings(): Promise<MultiTherapistResponse> {
    console.log('👋 Generating initial greetings from all personas...');

    const greetingPrompt = this.templateRenderer.render(this.config.prompts.initialGreeting, {
      therapistName: 'Dr. Montri',
      sessionType: 'therapy session'
    });

    // Generate greetings from all three personas in parallel
    const [cbtOnlyGreeting, miFixedGreeting, dynamicGreeting] = await Promise.all([
      this.generatePersonaGreeting('cbtOnly', greetingPrompt),
      this.generatePersonaGreeting('miFixedPretreatment', greetingPrompt),
      this.generatePersonaGreeting('dynamicAdaptive', greetingPrompt)
    ]);

    return {
      cbtOnly: cbtOnlyGreeting,
      miFixedPretreatment: miFixedGreeting,
      dynamicAdaptive: dynamicGreeting,
      patientMessage: '',
      timestamp: new Date().toISOString()
    };
  }

  private async generatePersonaGreeting(
    personaKey: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive',
    greetingPrompt: string
  ): Promise<TherapistPersonaResponse> {
    const strategy = this.strategies[personaKey];
    
    const systemPrompt = `You are ${strategy.name}. ${strategy.description}. 
    
    ${greetingPrompt}
    
    Keep your greeting warm, professional, and consistent with your therapeutic approach. Limit to 2-3 sentences.`;

    const response = await this.openaiService.generateResponse({
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: 'Please provide your initial greeting to start the therapy session.' }
      ],
      temperature: 0.7,
      max_completion_tokens: 1000
    });

    const thinking = await this.openaiService.generateThinking(
      'therapist',
      'Starting therapy session',
      `Providing initial greeting as ${strategy.name} with focus on ${strategy.description.toLowerCase()}.`
    );

    return {
      message: response.message,
      thinking: thinking,
      metadata: response.metadata,
      personaType: strategy.personaType,
      personaName: strategy.name,
      strategyState: strategy.getStrategyState()
    };
  }

  private buildSystemPromptWithApproach(
    patientAnalysis: PatientAnalysis,
    therapeuticApproach: any,
    techniquePrompt: string,
    personaName: string
  ): string {
    return this.templateRenderer.render(this.config.prompts.systemPrompt, {
      therapistName: personaName,
      patientAnalysis: JSON.stringify(patientAnalysis, null, 2),
      therapeuticApproach: therapeuticApproach.name,
      selectedTechnique: therapeuticApproach.selectedTechnique.name,
      techniqueDescription: therapeuticApproach.selectedTechnique.description,
      techniquePrompt,
      readinessScore: patientAnalysis.readinessScore.score,
      recommendedApproach: patientAnalysis.readinessScore.recommendedApproach
    });
  }

  private buildConversationMessages(patientMessage: string, conversationContext: ConversationContext) {
    const messages = conversationContext.messages.map(msg => ({
      role: msg.sender === 'therapist' ? 'assistant' : 'user' as 'assistant' | 'user',
      content: msg.content
    }));

    messages.push({
      role: 'user',
      content: patientMessage
    });

    return messages;
  }

  private buildConversationHistory(conversationContext: MultiTherapistConversationContext) {
    return conversationContext.patientMessages.map(msg => ({
      sender: 'patient' as 'therapist' | 'patient',
      content: msg.content,
      timestamp: msg.timestamp
    }));
  }

  private getSessionPhase(messageCount: number): 'opening' | 'middle' | 'closing' {
    if (messageCount <= 6) return 'opening';
    if (messageCount <= 16) return 'middle';
    return 'closing';
  }

  private getTechniquePrompt(techniqueId: string): string {
    return this.techniqueSelector.getTechniquePrompt(techniqueId);
  }

  /**
   * Stream multi-therapist response generation using LangGraph workflow
   */
  async *streamMultiTherapistResponse(
    patientMessage: string,
    conversationContext: MultiTherapistConversationContext,
    workflowConfig?: Partial<TherapeuticWorkflowConfig>
  ): AsyncGenerator<any, MultiTherapistResponse, unknown> {
    if (!this.useWorkflow || !this.workflowService) {
      // If workflow is not enabled, fall back to regular response and yield it once
      const response = await this.generateMultiTherapistResponse(patientMessage, conversationContext);
      yield { type: 'final_response', data: response };
      return response;
    }

    console.log('🔄 Streaming multi-therapist response using LangGraph workflow');

    try {
      // Generate workflow configuration
      const config = generateWorkflowConfig(conversationContext, {
        ...workflowConfig,
        streamingEnabled: true
      });

      // Stream the workflow execution
      const streamGenerator = this.workflowService.streamPatientMessage(
        patientMessage,
        conversationContext,
        config
      );

      let finalResponse: MultiTherapistResponse | null = null;

      for await (const chunk of streamGenerator) {
        // Calculate detailed progress for visual dashboard
        const progress = this.calculateProgress(chunk);

        // Yield detailed workflow updates for visual dashboard
        yield {
          type: 'workflow_progress',
          data: {
            step: chunk.workflowStep,
            progress,
            timestamp: new Date().toISOString(),
            patientAnalysis: chunk.patientAnalysis,
            therapeuticApproaches: chunk.therapeuticApproaches,
            therapistResponses: chunk.therapistResponses,
            processingMetadata: chunk.processingMetadata,
            workflowError: chunk.workflowError
          }
        };

        // Yield step-specific updates for enhanced visualization
        if (chunk.workflowStep === 'analysis' && chunk.patientAnalysis) {
          yield {
            type: 'patient_analysis_complete',
            data: {
              analysis: chunk.patientAnalysis,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (chunk.workflowStep === 'approach_selection' && chunk.therapeuticApproaches) {
          yield {
            type: 'approaches_selected',
            data: {
              approaches: chunk.therapeuticApproaches,
              timestamp: new Date().toISOString()
            }
          };
        }

        if (chunk.workflowStep === 'response_generation' && chunk.therapistResponses) {
          // Yield individual persona responses as they become available
          for (const [persona, response] of Object.entries(chunk.therapistResponses)) {
            if (response) {
              yield {
                type: 'persona_response_ready',
                data: {
                  persona,
                  response,
                  timestamp: new Date().toISOString()
                }
              };
            }
          }
        }

        if (chunk.workflowStep === 'complete') {
          // For complete step, we should have the final response in the chunk
          // Let's extract it from the workflow service
          break;
        }
      }

      // Get the final response from the last chunk or generate it
      if (!finalResponse) {
        // Fallback to generating the response from the current workflow state
        const lastChunk = await streamGenerator.next();
        if (lastChunk.done) {
          finalResponse = lastChunk.value;
        }
      }

      if (finalResponse) {
        yield { type: 'final_response', data: finalResponse };
        return finalResponse;
      } else {
        throw new Error('Workflow streaming did not produce final response');
      }

    } catch (error) {
      console.error('❌ Error streaming multi-therapist response:', error);

      // Fallback to regular response
      const fallbackResponse = await this.generateMultiTherapistResponseOriginal(patientMessage, conversationContext);
      yield { type: 'final_response', data: fallbackResponse };
      return fallbackResponse;
    }
  }

  /**
   * Enable or disable LangGraph workflow
   */
  setWorkflowEnabled(enabled: boolean): void {
    this.useWorkflow = enabled;
    console.log(`🔧 LangGraph workflow ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Check if workflow is enabled
   */
  isWorkflowEnabled(): boolean {
    return this.useWorkflow && !!this.workflowService;
  }

  /**
   * Get workflow service for direct access (if needed)
   */
  getWorkflowService(): TherapeuticWorkflowService | undefined {
    return this.workflowService;
  }

  /**
   * Calculate workflow progress percentage
   */
  private calculateProgress(state: any): number {
    const stepWeights = {
      analysis: 25,
      approach_selection: 15,
      response_generation: 50,
      synthesis: 10
    };

    let completedWeight = 0;
    const totalWeight = Object.values(stepWeights).reduce((sum, weight) => sum + weight, 0);

    if (state.patientAnalysis) completedWeight += stepWeights.analysis;
    if (state.therapeuticApproaches) completedWeight += stepWeights.approach_selection;
    if (state.therapistResponses) completedWeight += stepWeights.response_generation;
    if (state.workflowStep === 'complete') completedWeight += stepWeights.synthesis;

    return Math.round((completedWeight / totalWeight) * 100);
  }

  /**
   * Reset all persona strategies (useful for new conversations)
   */
  resetStrategies(): void {
    Object.values(this.strategies).forEach(strategy => strategy.reset());
    this.previousTechniques = {
      cbtOnly: [],
      miFixedPretreatment: [],
      dynamicAdaptive: []
    };

    // Clear workflow state if workflow is enabled
    if (this.useWorkflow && this.workflowService) {
      // Note: Workflow state clearing would need conversation ID
      console.log('🔧 Workflow state should be cleared for new conversation');
    }
  }
}
