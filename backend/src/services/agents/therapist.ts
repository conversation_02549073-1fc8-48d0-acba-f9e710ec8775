// Therapist Agent Service for MiCA Therapy Simulation
import { OpenAIService } from '../openai.js';
import { AgentResponse, TherapistResponse, ConversationContext, PatientAnalysis, TherapeuticApproachInfo } from '../../types/index.js';
import { TherapistConfig, defaultConversationConfig } from '../../config/conversation.js';
import { TechniqueSelector } from '../technique-selector.js';

export class TherapistAgentService {
  private openaiService: OpenAIService;
  private config: TherapistConfig;
  private conversationHistory: Array<{ role: string; content: string; timestamp: string }> = [];
  private techniqueSelector: TechniqueSelector;
  private previousTechniques: string[] = [];

  constructor(openaiService: OpenAIService, config?: TherapistConfig) {
    this.openaiService = openaiService;
    this.config = config || defaultConversationConfig.therapist;
    this.techniqueSelector = new TechniqueSelector();

    console.log(`👩‍⚕️ Therapist Agent initialized: ${this.config.persona.name}`);
    console.log(`🎯 Specialties: ${this.config.persona.specialties.join(', ')}`);
  }

  /**
   * Generate initial greeting message
   */
  async generateInitialGreeting(): Promise<AgentResponse> {
    console.log('👋 Generating therapist initial greeting...');

    const thinking = await this.generateThinking(
      'Starting the first session with a new patient. Need to create a welcoming, safe environment while gathering initial information.',
      'What should I say to make the patient feel comfortable and begin building rapport?'
    );

    return {
      message: this.config.prompts.initialGreeting,
      thinking,
      metadata: {
        confidence: 0.9,
        processingTime: 0
      }
    };
  }

  /**
   * Generate response to patient message
   */
  async generateResponse(
    patientMessage: string,
    conversationContext: ConversationContext
  ): Promise<TherapistResponse> {
    console.log('👩‍⚕️ Therapist generating response to patient message...');
    console.log(`📝 Patient said: "${patientMessage.substring(0, 100)}..."`);

    const startTime = Date.now();

    try {
      // Perform comprehensive patient analysis including readiness score
      const patientAnalysis = await this.openaiService.analyzePatient(patientMessage);

      // Select therapeutic approach and technique based on analysis
      const therapeuticApproach = this.techniqueSelector.selectApproachAndTechnique(
        patientAnalysis,
        {
          messageCount: conversationContext.messages.length,
          previousTechniques: this.previousTechniques,
          sessionPhase: this.getSessionPhase(conversationContext.messages.length)
        }
      );

      // Get technique-specific prompt
      const techniquePrompt = this.techniqueSelector.getTechniquePrompt(therapeuticApproach.selectedTechnique.id);

      // Build conversation context with therapeutic approach
      const systemPrompt = this.buildSystemPromptWithApproach(
        patientAnalysis,
        therapeuticApproach,
        techniquePrompt
      );
      const conversationMessages = this.buildConversationMessages(patientMessage, conversationContext);

      // Generate the main response using therapeutic approach
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: systemPrompt },
          ...conversationMessages
        ],
        temperature: 0.7,
        max_completion_tokens: 1000
      });

      // Generate thinking process with therapeutic context
      const thinking = await this.generateThinking(
        `Patient: "${patientMessage}" | Analysis: ${patientAnalysis.sentiment} sentiment, ${patientAnalysis.motivationLevel} motivation, ${patientAnalysis.engagementLevel} engagement, readiness score: ${patientAnalysis.readinessScore.score}/10 | Approach: ${therapeuticApproach.name} | Technique: ${therapeuticApproach.selectedTechnique.name}`,
        'What is the patient communicating? How does my chosen therapeutic approach and technique address their current state? What is my therapeutic reasoning?'
      );

      const processingTime = Date.now() - startTime;

      // Update conversation history and technique tracking
      this.conversationHistory.push({
        role: 'patient',
        content: patientMessage,
        timestamp: new Date().toISOString()
      });
      this.conversationHistory.push({
        role: 'therapist',
        content: response.message,
        timestamp: new Date().toISOString()
      });

      // Track used technique
      this.previousTechniques.push(therapeuticApproach.selectedTechnique.id);
      if (this.previousTechniques.length > 5) {
        this.previousTechniques.shift(); // Keep only last 5 techniques
      }

      console.log(`✅ Therapist response generated in ${processingTime}ms`);
      console.log(`💭 Analysis: ${patientAnalysis.sentiment} sentiment, ${patientAnalysis.motivationLevel} motivation, ${patientAnalysis.engagementLevel} engagement`);
      console.log(`🎯 Readiness Score: ${patientAnalysis.readinessScore.score}/10 (${patientAnalysis.readinessScore.recommendedApproach})`);
      console.log(`🔧 Approach: ${therapeuticApproach.name} | Technique: ${therapeuticApproach.selectedTechnique.name}`);

      return {
        message: response.message,
        thinking,
        metadata: {
          confidence: response.metadata.confidence,
          processingTime,
          patientAnalysis,
          therapeuticApproach
        }
      };

    } catch (error) {
      console.error('❌ Error generating therapist response:', error);
      const processingTime = Date.now() - startTime;

      // Create default analysis for error case
      const defaultReadinessScore = this.openaiService.calculateReadinessScore('neutral', 'medium', 'medium', 'medium', 'mixed', ['neutral']);
      const defaultAnalysis: PatientAnalysis = {
        sentiment: 'neutral',
        sentimentIntensity: 'medium',
        motivationLevel: 'medium',
        motivationType: 'mixed',
        engagementLevel: 'medium',
        engagementPatterns: ['neutral'],
        readinessScore: defaultReadinessScore
      };

      const defaultApproach: TherapeuticApproachInfo = {
        id: 'motivational-interviewing',
        name: 'Motivational Interviewing',
        selectedTechnique: {
          id: 'mi-reflective-listening',
          name: 'Reflective Listening',
          description: 'Reflecting back what the patient has said to demonstrate understanding'
        }
      };

      return {
        message: "I hear you, and I want to make sure I understand what you're sharing with me. Could you tell me a bit more about that?",
        thinking: "I'm having some difficulty processing right now, but I want to stay present and supportive for the patient.",
        metadata: {
          confidence: 0.5,
          processingTime,
          patientAnalysis: defaultAnalysis,
          therapeuticApproach: defaultApproach
        }
      };
    }
  }

  /**
   * Generate thinking process for the therapist
   */
  private async generateThinking(context: string, question: string): Promise<string> {
    const thinkingPrompt = `
As ${this.config.persona.name}, a therapist:

Context: ${context}

Key considerations: patient's emotional state, therapeutic techniques, rapport building, pacing.

Question: ${question}

Provide concise internal therapeutic reasoning (under 100 tokens):`;

    try {
      return await this.openaiService.generateThinking('therapist', context, thinkingPrompt);
    } catch (error) {
      console.error('❌ Error generating therapist thinking:', error);
      return 'Analyzing the patient\'s words and considering the most therapeutic response approach...';
    }
  }

  /**
   * Build system prompt for the therapist
   */
  // @ts-ignore - Method kept for potential future use
  private buildSystemPrompt(
    patientSentiment?: 'positive' | 'negative' | 'neutral',
    patientMotivation?: 'low' | 'medium' | 'high',
    patientEngagement?: 'low' | 'medium' | 'high'
  ): string {
    let prompt = `${this.config.prompts.systemPrompt}

Current session context:
- Patient concerns: ${defaultConversationConfig.patient.concerns.join(', ')}
- Patient emotional state: ${defaultConversationConfig.patient.emotionalState.primaryMood}
- Patient openness level: ${defaultConversationConfig.patient.emotionalState.openness}
- Session progress: Turn ${this.conversationHistory.length / 2 + 1}`;

    // Add patient analysis if available
    if (patientSentiment && patientMotivation && patientEngagement) {
      prompt += `

PATIENT ANALYSIS (from their most recent message):
- Sentiment: ${patientSentiment} - The patient's emotional tone and feelings
- Motivation Level: ${patientMotivation} - Their willingness to engage in therapy and change
- Engagement Level: ${patientEngagement} - How actively they're participating in the conversation

Use this analysis to tailor your response appropriately. For example:
- If sentiment is negative, provide more support and validation
- If motivation is low, focus on building hope and finding small steps
- If engagement is low, try to increase connection and interest`;
    }

    prompt += `

Your therapeutic approach should be:
- Empathy level: ${this.config.behavior.empathyLevel}
- Directness: ${this.config.behavior.directness}
- Questioning style: ${this.config.behavior.questioningStyle}
- Response length: ${this.config.behavior.responseLength}

Available techniques: ${this.config.techniques.join(', ')}

Remember to maintain professional boundaries while being genuinely caring and supportive.`;

    return prompt;
  }

  /**
   * Get session phase based on message count
   */
  private getSessionPhase(messageCount: number): 'opening' | 'middle' | 'closing' {
    if (messageCount <= 4) return 'opening';
    if (messageCount >= 16) return 'closing';
    return 'middle';
  }

  /**
   * Build system prompt with therapeutic approach
   */
  private buildSystemPromptWithApproach(
    patientAnalysis: PatientAnalysis,
    therapeuticApproach: TherapeuticApproachInfo,
    techniquePrompt: string
  ): string {
    let prompt = `${this.config.prompts.systemPrompt}

CURRENT PATIENT ANALYSIS:
- Emotional sentiment: ${patientAnalysis.sentiment}
- Motivation level: ${patientAnalysis.motivationLevel}
- Engagement level: ${patientAnalysis.engagementLevel}
- Readiness score: ${patientAnalysis.readinessScore.score}/10
- Readiness reasoning: ${patientAnalysis.readinessScore.reasoning}

SELECTED THERAPEUTIC APPROACH: ${therapeuticApproach.name}
SELECTED TECHNIQUE: ${therapeuticApproach.selectedTechnique.name}
- Description: ${therapeuticApproach.selectedTechnique.description}

TECHNIQUE GUIDANCE:
${techniquePrompt}

Use this analysis and therapeutic framework to inform your response. Be empathetic, professional, and implement the selected technique appropriately based on the patient's current state and readiness level.`;

    return prompt;
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    currentPatientMessage: string,
    _context: ConversationContext
  ): Array<{ role: 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'user' | 'assistant'; content: string }> = [];

    // Add recent conversation history (last 6 messages to stay within token limits)
    const recentHistory = this.conversationHistory.slice(-6);
    
    for (const msg of recentHistory) {
      messages.push({
        role: msg.role === 'patient' ? 'user' : 'assistant',
        content: msg.content
      });
    }

    // Add current patient message
    messages.push({
      role: 'user',
      content: currentPatientMessage
    });

    return messages;
  }

  /**
   * Analyze conversation progress
   */
  async analyzeProgress(): Promise<{
    rapportLevel: 'low' | 'medium' | 'high';
    issuesExplored: string[];
    recommendedFocus: string;
  }> {
    console.log('📊 Analyzing conversation progress...');

    try {
      const conversationSummary = this.conversationHistory
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      const analysisPrompt = `
Analyze this therapy conversation and provide:
1. Rapport level (low/medium/high)
2. Issues explored so far
3. Recommended focus for next steps

Conversation:
${conversationSummary}

Respond in JSON format:
{
  "rapportLevel": "low|medium|high",
  "issuesExplored": ["issue1", "issue2"],
  "recommendedFocus": "description"
}`;

      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are analyzing a therapy conversation. Respond only with valid JSON.' },
          { role: 'user', content: analysisPrompt }
        ],
        temperature: 0.3,
        max_completion_tokens: 1000
      });

      const analysis = JSON.parse(response.message);
      console.log('📈 Progress analysis completed:', analysis);
      
      return analysis;

    } catch (error) {
      console.error('❌ Error analyzing progress:', error);
      return {
        rapportLevel: 'medium',
        issuesExplored: ['initial assessment'],
        recommendedFocus: 'Continue building rapport and exploring patient concerns'
      };
    }
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): Array<{ role: string; content: string; timestamp: string }> {
    return [...this.conversationHistory];
  }

  /**
   * Clear conversation history
   */
  clearHistory(): void {
    console.log('🗑️ Clearing therapist conversation history');
    this.conversationHistory = [];
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<TherapistConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Therapist configuration updated');
  }
}
