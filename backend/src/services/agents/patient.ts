// Patient Agent Service for MiCA Therapy Simulation
import { OpenAIService } from '../openai.js';
import { PatientResponse, ConversationContext } from '../../types/index.js';
import { PatientConfig, defaultConversationConfig } from '../../config/conversation.js';
import { TemplateRenderer } from '../template-renderer.js';
import { PersonaService } from '../persona-service.js';

export class PatientAgentService {
  private openaiService: OpenAIService;
  private config: PatientConfig;
  private conversationHistory: Array<{ role: string; content: string; timestamp: string }> = [];
  private emotionalState: PatientConfig['emotionalState'];
  private sessionProgress: {
    trustBuilding: number; // 0-100
    openness: number; // 0-100
    engagement: number; // 0-100
  };
  private renderedPrompt: string | null = null;

  constructor(openaiService: OpenAIService, config?: PatientConfig) {
    this.openaiService = openaiService;
    this.config = config || defaultConversationConfig.patient;
    this.emotionalState = { ...this.config.emotionalState };

    this.sessionProgress = {
      trustBuilding: this.config.emotionalState.trustLevel,
      openness: this.getOpennessScore(this.config.emotionalState.openness),
      engagement: this.getEngagementScore(this.config.emotionalState.motivationLevel)
    };

    // Note: Rendered prompt will be initialized lazily when needed

    console.log(`👤 Patient Agent initialized: ${this.config.persona.name}`);
    console.log(`😔 Current mood: ${this.emotionalState.primaryMood}`);
    console.log(`🔒 Openness level: ${this.emotionalState.openness}`);
  }

  /**
   * Generate response to therapist message
   */
  async generateResponse(
    therapistMessage: string,
    conversationContext: ConversationContext
  ): Promise<PatientResponse> {
    console.log('👤 Patient generating response to therapist message...');
    console.log(`👩‍⚕️ Therapist said: "${therapistMessage.substring(0, 100)}..."`);

    const startTime = Date.now();

    try {
      // Update emotional state based on conversation progress
      this.updateEmotionalState(therapistMessage);

      // Build conversation context for the AI
      const systemPrompt = await this.buildSystemPrompt();
      const conversationMessages = this.buildConversationMessages(therapistMessage, conversationContext);

      // Generate the main response
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: systemPrompt },
          ...conversationMessages
        ],
        temperature: 0.8, // Higher temperature for more varied patient responses
        max_completion_tokens: 200
      });

      // Generate thinking process
      const thinking = await this.generateThinking(
        `Therapist just said: "${therapistMessage}"`,
        'How do I feel about this? What should I share? How much can I trust this person?'
      );

      const processingTime = Date.now() - startTime;

      // Update conversation history
      this.conversationHistory.push({
        role: 'therapist',
        content: therapistMessage,
        timestamp: new Date().toISOString()
      });
      this.conversationHistory.push({
        role: 'patient',
        content: response.message,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Patient response generated in ${processingTime}ms`);
      console.log(`😊 Current emotional state: ${this.emotionalState.primaryMood} (${this.emotionalState.energyLevel} energy)`);

      return {
        message: response.message,
        thinking,
        metadata: {
          confidence: response.metadata.confidence,
          processingTime
        }
      };

    } catch (error) {
      console.error('❌ Error generating patient response:', error);
      
      return {
        message: "I... I'm not sure how to put this into words right now.",
        thinking: "I'm feeling overwhelmed and having trouble expressing myself clearly.",
        metadata: {
          confidence: 0.3,
          processingTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Generate differentiated responses to multiple therapist personas
   */
  async generateMultiTherapistResponse(
    therapistMessages: {
      cbtOnly: { content: string; therapeuticApproach: string; technique: string };
      miFixedPretreatment: { content: string; therapeuticApproach: string; technique: string };
      dynamicAdaptive: { content: string; therapeuticApproach: string; technique: string };
    },
    conversationContexts: {
      cbtOnly: ConversationContext;
      miFixedPretreatment: ConversationContext;
      dynamicAdaptive: ConversationContext;
    }
  ): Promise<{
    cbtOnly: PatientResponse;
    miFixedPretreatment: PatientResponse;
    dynamicAdaptive: PatientResponse;
  }> {
    const startTime = Date.now();
    console.log('👤 Generating differentiated patient responses to all therapist personas...');

    try {
      // Update emotional state based on conversation progress (using dynamic adaptive as reference)
      this.updateEmotionalState(therapistMessages.dynamicAdaptive.content);

      // Generate responses to each therapist persona in parallel
      const [cbtResponse, miResponse, dynamicResponse] = await Promise.all([
        this.generatePersonaSpecificResponse(
          'cbt-only',
          therapistMessages.cbtOnly,
          conversationContexts.cbtOnly
        ),
        this.generatePersonaSpecificResponse(
          'mi-fixed-pretreatment',
          therapistMessages.miFixedPretreatment,
          conversationContexts.miFixedPretreatment
        ),
        this.generatePersonaSpecificResponse(
          'dynamic-adaptive',
          therapistMessages.dynamicAdaptive,
          conversationContexts.dynamicAdaptive
        )
      ]);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Multi-therapist patient responses generated in ${processingTime}ms`);

      return {
        cbtOnly: cbtResponse,
        miFixedPretreatment: miResponse,
        dynamicAdaptive: dynamicResponse
      };

    } catch (error) {
      console.error('❌ Error generating multi-therapist patient responses:', error);

      // Return fallback responses
      const fallbackResponse: PatientResponse = {
        message: "I... I'm not sure how to put this into words right now.",
        thinking: "I'm feeling overwhelmed and having trouble expressions myself clearly.",
        metadata: {
          confidence: 0.3,
          processingTime: Date.now() - startTime
        }
      };

      return {
        cbtOnly: fallbackResponse,
        miFixedPretreatment: fallbackResponse,
        dynamicAdaptive: fallbackResponse
      };
    }
  }

  /**
   * Generate thinking process for the patient
   */
  private async generateThinking(context: string, question: string): Promise<string> {
    const thinkingPrompt = `
As ${this.config.persona.name} (${this.config.persona.age}):

Emotional state: ${this.emotionalState.primaryMood}, energy: ${this.emotionalState.energyLevel}, trust: ${this.emotionalState.trustLevel}/100
Concerns: ${this.config.concerns.slice(0, 2).join(', ')}

Context: ${context}

Question: ${question}

Provide concise internal thoughts and feelings (under 100 tokens):`;

    try {
      return await this.openaiService.generateThinking('patient', context, thinkingPrompt);
    } catch (error) {
      console.error('❌ Error generating patient thinking:', error);
      return 'I\'m trying to process what the therapist said and figure out how much I want to share...';
    }
  }

  /**
   * Generate a response specific to a therapist persona and their therapeutic approach
   */
  private async generatePersonaSpecificResponse(
    personaType: 'cbt-only' | 'mi-fixed-pretreatment' | 'dynamic-adaptive',
    therapistMessage: { content: string; therapeuticApproach: string; technique: string },
    conversationContext: ConversationContext
  ): Promise<PatientResponse> {
    const startTime = Date.now();

    // Build persona-specific system prompt
    const systemPrompt = await this.buildPersonaSpecificSystemPrompt(
      personaType,
      therapistMessage.therapeuticApproach,
      therapistMessage.technique
    );

    // Build conversation messages
    const conversationMessages = this.buildConversationMessages(therapistMessage.content, conversationContext);

    // Generate the main response
    const response = await this.openaiService.generateResponse({
      messages: [
        { role: 'system', content: systemPrompt },
        ...conversationMessages
      ],
      temperature: 0.8, // Higher temperature for more varied patient responses
      max_completion_tokens: 200
    });

    // Generate persona-specific thinking
    const thinking = await this.generatePersonaSpecificThinking(
      personaType,
      therapistMessage,
      conversationContext
    );

    const processingTime = Date.now() - startTime;

    return {
      message: response.message,
      thinking,
      metadata: {
        confidence: response.metadata.confidence,
        processingTime
      }
    };
  }

  /**
   * Initialize the rendered prompt from template if persona ID is available
   */
  private async initializeRenderedPrompt(): Promise<void> {
    if (this.config.id) {
      try {
        const persona = PersonaService.getPersonaById(this.config.id);
        if (persona) {
          this.renderedPrompt = await TemplateRenderer.renderPatientPrompt(persona);
          console.log(`✅ Patient prompt rendered for persona: ${persona.name}`);
        }
      } catch (error) {
        console.error('❌ Error rendering patient prompt:', error);
        this.renderedPrompt = null;
      }
    }
  }

  /**
   * Build persona-specific system prompt that considers the therapeutic approach being used
   */
  private async buildPersonaSpecificSystemPrompt(
    personaType: 'cbt-only' | 'mi-fixed-pretreatment' | 'dynamic-adaptive',
    therapeuticApproach: string,
    technique: string
  ): Promise<string> {
    // Initialize rendered prompt if not already done and we have a persona ID
    if (!this.renderedPrompt && this.config.id) {
      await this.initializeRenderedPrompt();
    }

    // Get base prompt
    const basePrompt = this.renderedPrompt || this.config.prompts.systemPrompt;

    // Add therapeutic approach awareness
    const therapeuticContext = this.getTherapeuticContextPrompt(therapeuticApproach, technique);
    const responseStyle = this.getResponseStyleForApproach(therapeuticApproach);

    return `${basePrompt}

Current emotional state:
- Primary mood: ${this.emotionalState.primaryMood}
- Energy level: ${this.emotionalState.energyLevel}
- Openness: ${this.emotionalState.openness}
- Trust level: ${this.emotionalState.trustLevel}/100
- Motivation: ${this.emotionalState.motivationLevel}

Response patterns:
- Tendency: ${this.config.responsePatterns.tendency}
- Emotional expression: ${this.config.responsePatterns.emotionalExpression}
- Detail level: ${this.config.responsePatterns.detailLevel}

Session progress:
- Trust building: ${this.sessionProgress.trustBuilding}/100
- Openness: ${this.sessionProgress.openness}/100
- Engagement: ${this.sessionProgress.engagement}/100

THERAPEUTIC CONTEXT:
The therapist is using ${therapeuticApproach} with the specific technique: ${technique}
${therapeuticContext}

RESPONSE STYLE FOR THIS APPROACH:
${responseStyle}

Remember to respond authentically as ${this.config.persona.name}, but adapt your response style and content to reflect how you would naturally react to this specific therapeutic approach and technique.`;
  }

  /**
   * Build system prompt for the patient
   */
  private async buildSystemPrompt(): Promise<string> {
    // Initialize rendered prompt if not already done and we have a persona ID
    if (!this.renderedPrompt && this.config.id) {
      await this.initializeRenderedPrompt();
    }

    // Use rendered template prompt if available, otherwise fall back to config prompt
    if (this.renderedPrompt) {
      return this.renderedPrompt;
    }

    return `${this.config.prompts.systemPrompt}

Current emotional state:
- Primary mood: ${this.emotionalState.primaryMood}
- Energy level: ${this.emotionalState.energyLevel}
- Openness: ${this.emotionalState.openness}
- Trust level: ${this.emotionalState.trustLevel}/100
- Motivation: ${this.emotionalState.motivationLevel}

Response patterns:
- Tendency: ${this.config.responsePatterns.tendency}
- Emotional expression: ${this.config.responsePatterns.emotionalExpression}
- Detail level: ${this.config.responsePatterns.detailLevel}

Session progress:
- Trust building: ${this.sessionProgress.trustBuilding}/100
- Openness: ${this.sessionProgress.openness}/100
- Engagement: ${this.sessionProgress.engagement}/100

Remember:
- You're in therapy for the first time and may feel nervous or uncertain
- Your responses should reflect your current emotional state and trust level
- Be authentic to your personality while showing gradual progress
- Don't reveal everything at once - trust builds slowly`;
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    currentTherapistMessage: string,
    _context: ConversationContext
  ): Array<{ role: 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'user' | 'assistant'; content: string }> = [];

    // Add recent conversation history (last 6 messages to stay within token limits)
    const recentHistory = this.conversationHistory.slice(-6);
    
    for (const msg of recentHistory) {
      messages.push({
        role: msg.role === 'therapist' ? 'user' : 'assistant',
        content: msg.content
      });
    }

    // Add current therapist message
    messages.push({
      role: 'user',
      content: currentTherapistMessage
    });

    return messages;
  }

  /**
   * Update emotional state based on conversation progress
   */
  private updateEmotionalState(therapistMessage: string): void {
    // Simple emotional state evolution based on therapist interaction
    const isEmpathetic = therapistMessage.toLowerCase().includes('understand') ||
                        therapistMessage.toLowerCase().includes('hear you') ||
                        therapistMessage.toLowerCase().includes('feel');
    
    // Gradually increase trust if therapist is empathetic
    if (isEmpathetic && this.emotionalState.trustLevel < 80) {
      this.emotionalState.trustLevel = Math.min(100, this.emotionalState.trustLevel + 5);
      this.sessionProgress.trustBuilding = this.emotionalState.trustLevel;
    }

    // Adjust openness based on trust level
    if (this.emotionalState.trustLevel > 60 && this.emotionalState.openness === 'guarded') {
      this.emotionalState.openness = 'open';
      this.sessionProgress.openness = this.getOpennessScore('open');
    } else if (this.emotionalState.trustLevel > 80 && this.emotionalState.openness === 'open') {
      this.emotionalState.openness = 'very_open';
      this.sessionProgress.openness = this.getOpennessScore('very_open');
    }

    // Slight mood improvement over time with good therapeutic interaction
    if (isEmpathetic && this.conversationHistory.length > 4) {
      if (this.emotionalState.primaryMood === 'anxious' && Math.random() > 0.7) {
        this.emotionalState.primaryMood = 'neutral';
      } else if (this.emotionalState.primaryMood === 'neutral' && Math.random() > 0.8) {
        this.emotionalState.primaryMood = 'hopeful';
      }
    }

    console.log(`📊 Emotional state updated - Trust: ${this.emotionalState.trustLevel}, Openness: ${this.emotionalState.openness}, Mood: ${this.emotionalState.primaryMood}`);
  }



  /**
   * Convert openness to numeric score
   */
  private getOpennessScore(openness: PatientConfig['emotionalState']['openness']): number {
    switch (openness) {
      case 'closed': return 20;
      case 'guarded': return 40;
      case 'open': return 70;
      case 'very_open': return 90;
      default: return 40;
    }
  }

  /**
   * Convert motivation to engagement score
   */
  private getEngagementScore(motivation: PatientConfig['emotionalState']['motivationLevel']): number {
    switch (motivation) {
      case 'low': return 30;
      case 'medium': return 60;
      case 'high': return 90;
      default: return 60;
    }
  }

  /**
   * Get current emotional state
   */
  getCurrentEmotionalState(): PatientConfig['emotionalState'] {
    return { ...this.emotionalState };
  }

  /**
   * Get session progress
   */
  getSessionProgress(): typeof this.sessionProgress {
    return { ...this.sessionProgress };
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): Array<{ role: string; content: string; timestamp: string }> {
    return [...this.conversationHistory];
  }

  /**
   * Clear conversation history and reset state
   */
  clearHistory(): void {
    console.log('🗑️ Clearing patient conversation history and resetting state');
    this.conversationHistory = [];
    this.emotionalState = { ...this.config.emotionalState };
    this.sessionProgress = {
      trustBuilding: this.config.emotionalState.trustLevel,
      openness: this.getOpennessScore(this.config.emotionalState.openness),
      engagement: this.getEngagementScore(this.config.emotionalState.motivationLevel)
    };
  }

  /**
   * Generate persona-specific thinking based on therapeutic approach
   */
  private async generatePersonaSpecificThinking(
    personaType: 'cbt-only' | 'mi-fixed-pretreatment' | 'dynamic-adaptive',
    therapistMessage: { content: string; therapeuticApproach: string; technique: string },
    conversationContext: ConversationContext
  ): Promise<string> {
    const context = `Therapist (using ${therapistMessage.therapeuticApproach} - ${therapistMessage.technique}) said: "${therapistMessage.content}"`;
    const approachSpecificPrompt = this.getThinkingPromptForApproach(therapistMessage.therapeuticApproach, therapistMessage.technique);

    const thinkingPrompt = `
As ${this.config.persona.name} (${this.config.persona.age}):

Emotional state: ${this.emotionalState.primaryMood}, energy: ${this.emotionalState.energyLevel}, trust: ${this.emotionalState.trustLevel}/100
Concerns: ${this.config.concerns.slice(0, 2).join(', ')}

Context: ${context}

${approachSpecificPrompt}

Provide concise internal thoughts and feelings (under 100 tokens):`;

    try {
      return await this.openaiService.generateThinking('patient', context, thinkingPrompt);
    } catch (error) {
      console.error('❌ Error generating persona-specific patient thinking:', error);
      return 'I\'m trying to process what the therapist said and figure out how much I want to share...';
    }
  }

  /**
   * Get therapeutic context prompt based on approach and technique
   */
  private getTherapeuticContextPrompt(therapeuticApproach: string, technique: string): string {
    if (therapeuticApproach.toLowerCase().includes('motivational')) {
      return `The therapist is being collaborative and non-confrontational, trying to help you explore your own motivations and resolve any ambivalence about change. They're not pushing you but rather helping you discover your own reasons for change.`;
    } else if (therapeuticApproach.toLowerCase().includes('cognitive') || therapeuticApproach.toLowerCase().includes('cbt')) {
      return `The therapist is being more structured and goal-oriented, focusing on identifying and examining your thought patterns and behaviors. They may challenge your thinking or ask you to consider alternative perspectives.`;
    } else {
      return `The therapist is adapting their approach based on your readiness and needs, switching between collaborative exploration and more structured interventions.`;
    }
  }

  /**
   * Get response style guidance based on therapeutic approach
   */
  private getResponseStyleForApproach(therapeuticApproach: string): string {
    if (therapeuticApproach.toLowerCase().includes('motivational')) {
      return `- Be more exploratory and tentative in your responses
- Express ambivalence and mixed feelings when appropriate
- Show gradual opening up as you feel heard and understood
- Focus more on feelings, values, and personal motivations
- Use phrases like "I'm not sure, but maybe..." or "Part of me feels..."`;
    } else if (therapeuticApproach.toLowerCase().includes('cognitive') || therapeuticApproach.toLowerCase().includes('cbt')) {
      return `- Be more analytical and specific about thoughts and behaviors
- Initially show some resistance to challenges or alternative perspectives
- Gradually engage with the structured approach as trust builds
- Focus on concrete examples and specific situations
- Use phrases like "But what if..." or "I never thought of it that way..."`;
    } else {
      return `- Adapt your response style based on the specific technique being used
- Show awareness of the therapist's changing approach
- Respond authentically to whatever style they're currently using`;
    }
  }

  /**
   * Get thinking prompt specific to therapeutic approach
   */
  private getThinkingPromptForApproach(therapeuticApproach: string, technique: string): string {
    if (therapeuticApproach.toLowerCase().includes('motivational')) {
      return `The therapist is using a collaborative approach (${technique}). How does this non-confrontational style make you feel? Are you feeling more or less willing to open up? What internal conflicts or ambivalence are you experiencing?`;
    } else if (therapeuticApproach.toLowerCase().includes('cognitive') || therapeuticApproach.toLowerCase().includes('cbt')) {
      return `The therapist is using a structured CBT approach (${technique}). How do you feel about being asked to examine your thoughts or behaviors? Are you feeling challenged or supported? What resistance or curiosity is this bringing up?`;
    } else {
      return `The therapist is adapting their approach (${technique}). How are you responding to this particular style? What thoughts and feelings is this specific technique bringing up for you?`;
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<PatientConfig>): void {
    this.config = { ...this.config, ...newConfig };
    if (newConfig.emotionalState) {
      this.emotionalState = { ...this.emotionalState, ...newConfig.emotionalState };
    }
    console.log('⚙️ Patient configuration updated');
  }
}
