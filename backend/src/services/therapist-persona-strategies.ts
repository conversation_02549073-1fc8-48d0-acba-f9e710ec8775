// Therapist Persona Strategy Classes for Comparative Study
import { PatientAnalysis, TherapeuticApproachInfo, TherapistPersonaType, TherapeuticStrategy } from '../types/index.js';
import { TechniqueSelector } from './technique-selector.js';
import { therapeuticApproaches } from '../data/therapeutic-approaches.js';

export interface PersonaStrategyContext {
  messageCount: number;
  previousTechniques: string[];
  sessionPhase: 'opening' | 'middle' | 'closing';
  conversationHistory: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
  }>;
}

export interface StrategyState {
  currentApproach: 'CBT' | 'MI';
  hasSwitch: boolean;
  switchReason?: string;
  sessionPhase: 'opening' | 'middle' | 'closing';
  switchHistory: Array<{
    fromApproach: 'CBT' | 'MI';
    toApproach: 'CBT' | 'MI';
    readinessScore: number;
    timestamp: string;
    reason: string;
  }>;
}

export abstract class BaseTherapistPersonaStrategy {
  protected techniqueSelector: TechniqueSelector;
  protected strategyState: StrategyState;
  
  constructor(
    public readonly personaType: TherapistPersonaType,
    public readonly name: string,
    public readonly description: string,
    protected readonly strategy: TherapeuticStrategy
  ) {
    this.techniqueSelector = new TechniqueSelector();
    this.strategyState = {
      currentApproach: strategy.initialApproach,
      hasSwitch: false,
      sessionPhase: 'opening',
      switchHistory: []
    };
  }

  abstract selectApproachAndTechnique(
    patientAnalysis: PatientAnalysis,
    context: PersonaStrategyContext
  ): TherapeuticApproachInfo & { strategyState: StrategyState };

  protected updateSessionPhase(messageCount: number): void {
    if (messageCount <= 6) {
      this.strategyState.sessionPhase = 'opening';
    } else if (messageCount <= 16) {
      this.strategyState.sessionPhase = 'middle';
    } else {
      this.strategyState.sessionPhase = 'closing';
    }
  }

  protected recordSwitch(
    fromApproach: 'CBT' | 'MI',
    toApproach: 'CBT' | 'MI',
    readinessScore: number,
    reason: string
  ): void {
    this.strategyState.switchHistory.push({
      fromApproach,
      toApproach,
      readinessScore,
      timestamp: new Date().toISOString(),
      reason
    });
    this.strategyState.hasSwitch = true;
    this.strategyState.switchReason = reason;
    this.strategyState.currentApproach = toApproach;
  }

  getStrategyState(): StrategyState {
    return { ...this.strategyState };
  }

  reset(): void {
    this.strategyState = {
      currentApproach: this.strategy.initialApproach,
      hasSwitch: false,
      sessionPhase: 'opening',
      switchHistory: []
    };
  }
}

/**
 * CBT-Only Therapist: Always uses CBT regardless of readiness score
 */
export class CBTOnlyStrategy extends BaseTherapistPersonaStrategy {
  constructor() {
    super(
      'cbt-only',
      'CBT-Only Therapist',
      'A therapist who exclusively uses Cognitive Behavioral Therapy techniques regardless of patient readiness',
      {
        type: 'cbt-only',
        allowSwitching: false,
        initialApproach: 'CBT'
      }
    );
  }

  selectApproachAndTechnique(
    patientAnalysis: PatientAnalysis,
    context: PersonaStrategyContext
  ): TherapeuticApproachInfo & { strategyState: StrategyState } {
    this.updateSessionPhase(context.messageCount);

    // Always select CBT approach
    const cbtApproach = therapeuticApproaches.find(a => a.id === 'cognitive-behavioral-therapy')!;
    
    // Select appropriate CBT technique
    const selectedTechnique = this.techniqueSelector.selectTechnique(
      cbtApproach,
      patientAnalysis,
      context
    );

    return {
      id: 'cognitive-behavioral-therapy',
      name: cbtApproach.name,
      selectedTechnique: {
        id: selectedTechnique.id,
        name: selectedTechnique.name,
        description: selectedTechnique.description
      },
      strategyState: this.getStrategyState()
    };
  }
}

/**
 * MI Fixed Pretreatment: Starts with MI, switches to CBT once readiness threshold is met, never switches back
 */
export class MIFixedPretreatmentStrategy extends BaseTherapistPersonaStrategy {
  constructor(readinessThreshold: number = 8) {
    super(
      'mi-fixed-pretreatment',
      'MI Fixed Pretreatment Therapist',
      'A therapist who uses MI initially, switches to CBT once readiness threshold is met, and continues with CBT',
      {
        type: 'mi-fixed-pretreatment',
        readinessThreshold,
        allowSwitching: true,
        initialApproach: 'MI',
        switchingRules: {
          cbtThreshold: readinessThreshold,
          miThreshold: 0,
          allowReverseSwitch: false
        }
      }
    );
  }

  selectApproachAndTechnique(
    patientAnalysis: PatientAnalysis,
    context: PersonaStrategyContext
  ): TherapeuticApproachInfo & { strategyState: StrategyState } {
    this.updateSessionPhase(context.messageCount);

    const readinessScore = patientAnalysis.readinessScore.score;
    const threshold = this.strategy.readinessThreshold!;

    // Check if we should switch from MI to CBT (one-way switch only)
    if (this.strategyState.currentApproach === 'MI' && readinessScore >= threshold) {
      this.recordSwitch(
        'MI',
        'CBT',
        readinessScore,
        `Readiness score ${readinessScore} exceeded threshold ${threshold}`
      );
    }

    // Select approach based on current state
    const approachId = this.strategyState.currentApproach === 'CBT' 
      ? 'cognitive-behavioral-therapy' 
      : 'motivational-interviewing';
    
    const selectedApproach = therapeuticApproaches.find(a => a.id === approachId)!;
    
    // Select appropriate technique
    const selectedTechnique = this.techniqueSelector.selectTechnique(
      selectedApproach,
      patientAnalysis,
      context
    );

    return {
      id: approachId as 'motivational-interviewing' | 'cognitive-behavioral-therapy',
      name: selectedApproach.name,
      selectedTechnique: {
        id: selectedTechnique.id,
        name: selectedTechnique.name,
        description: selectedTechnique.description
      },
      strategyState: this.getStrategyState()
    };
  }
}

/**
 * Dynamic Adaptive: Continuously switches between MI and CBT based on real-time readiness score
 */
export class DynamicAdaptiveStrategy extends BaseTherapistPersonaStrategy {
  constructor(cbtThreshold: number = 8, miThreshold: number = 7) {
    super(
      'dynamic-adaptive',
      'Dynamic Adaptive Therapist',
      'A therapist who dynamically switches between MI and CBT based on real-time readiness assessment',
      {
        type: 'dynamic-adaptive',
        allowSwitching: true,
        initialApproach: 'MI',
        switchingRules: {
          cbtThreshold,
          miThreshold,
          allowReverseSwitch: true
        }
      }
    );
  }

  selectApproachAndTechnique(
    patientAnalysis: PatientAnalysis,
    context: PersonaStrategyContext
  ): TherapeuticApproachInfo & { strategyState: StrategyState } {
    this.updateSessionPhase(context.messageCount);

    const readinessScore = patientAnalysis.readinessScore.score;
    const cbtThreshold = this.strategy.switchingRules!.cbtThreshold;
    const miThreshold = this.strategy.switchingRules!.miThreshold;

    const previousApproach = this.strategyState.currentApproach;
    let newApproach: 'CBT' | 'MI' = previousApproach;

    // Dynamic switching logic
    if (readinessScore >= cbtThreshold && this.strategyState.currentApproach === 'MI') {
      newApproach = 'CBT';
      this.recordSwitch(
        'MI',
        'CBT',
        readinessScore,
        `Readiness score ${readinessScore} exceeded CBT threshold ${cbtThreshold}`
      );
    } else if (readinessScore <= miThreshold && this.strategyState.currentApproach === 'CBT') {
      newApproach = 'MI';
      this.recordSwitch(
        'CBT',
        'MI',
        readinessScore,
        `Readiness score ${readinessScore} fell below MI threshold ${miThreshold}`
      );
    }

    // Select approach based on current state
    const approachId = newApproach === 'CBT' 
      ? 'cognitive-behavioral-therapy' 
      : 'motivational-interviewing';
    
    const selectedApproach = therapeuticApproaches.find(a => a.id === approachId)!;
    
    // Select appropriate technique
    const selectedTechnique = this.techniqueSelector.selectTechnique(
      selectedApproach,
      patientAnalysis,
      context
    );

    return {
      id: approachId as 'motivational-interviewing' | 'cognitive-behavioral-therapy',
      name: selectedApproach.name,
      selectedTechnique: {
        id: selectedTechnique.id,
        name: selectedTechnique.name,
        description: selectedTechnique.description
      },
      strategyState: this.getStrategyState()
    };
  }
}

// Factory function to create strategy instances
export function createTherapistPersonaStrategy(
  personaType: TherapistPersonaType,
  config?: { cbtThreshold?: number; miThreshold?: number }
): BaseTherapistPersonaStrategy {
  switch (personaType) {
    case 'cbt-only':
      return new CBTOnlyStrategy();
    case 'mi-fixed-pretreatment':
      return new MIFixedPretreatmentStrategy(config?.cbtThreshold);
    case 'dynamic-adaptive':
      return new DynamicAdaptiveStrategy(config?.cbtThreshold, config?.miThreshold);
    default:
      throw new Error(`Unknown therapist persona type: ${personaType}`);
  }
}
