// Mode-Specific Multi-Therapist Service
// Provides three distinct therapeutic modes: CBT, DBT, and Psychodynamic

import { 
  TherapeuticMode,
  EnhancedPatientAnalysis,
  PatientAnalysis,
  ConversationContext,
  TherapistPersonaResponse,
  TherapeuticApproachInfo
} from '../types/index.js';
import { OpenAIService } from './openai.js';
import { ModeSpecificAnalysisService } from './mode-specific-analysis.js';
import { therapeuticApproaches } from '../data/therapeutic-approaches.js';
import { 
  openaiLogger,
  logFunctionEntry,
  logFunctionSuccess,
  logFunctionError
} from '../utils/logger.js';

export interface ModeSpecificMultiTherapistResponse {
  cbtMode: TherapistPersonaResponse;
  dbtMode: TherapistPersonaResponse;
  psychodynamicMode: TherapistPersonaResponse;
  patientMessage: string;
  timestamp: string;
}

export class ModeSpecificMultiTherapistService {
  private openaiService: OpenAIService;
  private analysisService: ModeSpecificAnalysisService;

  constructor(openaiService: OpenAIService) {
    this.openaiService = openaiService;
    this.analysisService = new ModeSpecificAnalysisService(openaiService);
  }

  /**
   * Generate responses from all three therapeutic modes
   */
  async generateMultiModeResponse(
    patientMessage: string,
    conversationContexts: {
      cbt: ConversationContext;
      dbt: ConversationContext;
      psychodynamic: ConversationContext;
    }
  ): Promise<ModeSpecificMultiTherapistResponse> {
    const operation = 'generateMultiModeResponse';
    
    logFunctionEntry(openaiLogger, operation, {
      messageLength: patientMessage.length,
      cbtMessagesCount: conversationContexts.cbt.messages.length,
      dbtMessagesCount: conversationContexts.dbt.messages.length,
      psychodynamicMessagesCount: conversationContexts.psychodynamic.messages.length
    });

    try {
      // First, perform base patient analysis
      const baseAnalysis = await this.openaiService.analyzePatient(patientMessage);

      // Generate mode-specific analyses and responses in parallel
      const [cbtResponse, dbtResponse, psychodynamicResponse] = await Promise.all([
        this.generateModeSpecificResponse('CBT', patientMessage, baseAnalysis, conversationContexts.cbt),
        this.generateModeSpecificResponse('DBT', patientMessage, baseAnalysis, conversationContexts.dbt),
        this.generateModeSpecificResponse('psychodynamic', patientMessage, baseAnalysis, conversationContexts.psychodynamic)
      ]);

      const response: ModeSpecificMultiTherapistResponse = {
        cbtMode: cbtResponse,
        dbtMode: dbtResponse,
        psychodynamicMode: psychodynamicResponse,
        patientMessage,
        timestamp: new Date().toISOString()
      };

      logFunctionSuccess(openaiLogger, operation, {
        cbtResponseLength: cbtResponse.message.length,
        dbtResponseLength: dbtResponse.message.length,
        psychodynamicResponseLength: psychodynamicResponse.message.length
      });

      return response;
    } catch (error) {
      logFunctionError(openaiLogger, operation, error as Error);
      throw error;
    }
  }

  /**
   * Generate response from a specific therapeutic mode
   */
  private async generateModeSpecificResponse(
    mode: TherapeuticMode,
    patientMessage: string,
    baseAnalysis: PatientAnalysis,
    conversationContext: ConversationContext
  ): Promise<TherapistPersonaResponse> {
    const operation = `generateModeSpecificResponse_${mode}`;
    
    logFunctionEntry(openaiLogger, operation, {
      mode,
      messageLength: patientMessage.length,
      messagesCount: conversationContext.messages.length
    });

    try {
      // Perform mode-specific analysis
      const enhancedAnalysis = await this.analysisService.analyzeClientState(
        patientMessage, 
        mode, 
        baseAnalysis
      );

      // Select therapeutic approach and technique for this mode
      const therapeuticApproach = this.selectTherapeuticApproach(mode, enhancedAnalysis);

      // Build system prompt with mode-specific context
      const systemPrompt = this.buildModeSpecificSystemPrompt(mode, enhancedAnalysis, therapeuticApproach);

      // Build conversation messages
      const conversationMessages = this.buildConversationMessages(patientMessage, conversationContext);

      // Generate the therapeutic response
      const response = await this.openaiService.generateResponse({
        messages: [
          { role: 'system', content: systemPrompt },
          ...conversationMessages
        ],
        temperature: 0.7,
        max_completion_tokens: 1000
      });

      // Generate mode-specific thinking process
      const thinking = await this.generateModeSpecificThinking(
        mode,
        patientMessage,
        enhancedAnalysis,
        therapeuticApproach
      );

      const therapistResponse: TherapistPersonaResponse = {
        message: response.message,
        thinking,
        metadata: {
          confidence: response.metadata.confidence,
          processingTime: response.metadata.processingTime,
          patientAnalysis: enhancedAnalysis,
          therapeuticApproach
        },
        personaType: this.mapModeToPersonaType(mode),
        personaName: this.getModeDisplayName(mode)
      };

      logFunctionSuccess(openaiLogger, operation, {
        mode,
        responseLength: response.message.length,
        thinkingLength: thinking.length,
        therapeuticApproach: therapeuticApproach.name
      });

      return therapistResponse;
    } catch (error) {
      logFunctionError(openaiLogger, operation, error as Error);
      throw error;
    }
  }

  /**
   * Select therapeutic approach based on mode and analysis
   */
  private selectTherapeuticApproach(
    mode: TherapeuticMode, 
    analysis: EnhancedPatientAnalysis
  ): TherapeuticApproachInfo {
    let approachId: string;
    
    switch (mode) {
      case 'CBT':
        approachId = 'cognitive-behavioral-therapy';
        break;
      case 'DBT':
        approachId = 'dialectical-behavior-therapy';
        break;
      case 'psychodynamic':
        approachId = 'psychodynamic-therapy';
        break;
      default:
        throw new Error(`Unknown therapeutic mode: ${mode}`);
    }

    const approach = therapeuticApproaches.find(a => a.id === approachId);
    if (!approach) {
      throw new Error(`Therapeutic approach not found: ${approachId}`);
    }

    // Select appropriate technique based on mode-specific analysis
    const selectedTechnique = this.selectModeSpecificTechnique(mode, analysis, approach);

    return {
      id: approachId as 'motivational-interviewing' | 'cognitive-behavioral-therapy',
      name: approach.name,
      selectedTechnique: {
        id: selectedTechnique.id,
        name: selectedTechnique.name,
        description: selectedTechnique.description
      }
    };
  }

  /**
   * Select technique based on mode-specific analysis
   */
  private selectModeSpecificTechnique(
    mode: TherapeuticMode,
    analysis: EnhancedPatientAnalysis,
    approach: any
  ): any {
    // For now, select the first technique - this can be enhanced with mode-specific logic
    if (approach.techniques && approach.techniques.length > 0) {
      return approach.techniques[0];
    }
    
    // Fallback
    return {
      id: `${mode.toLowerCase()}-default`,
      name: `${mode} Default Technique`,
      description: `Default therapeutic technique for ${mode} mode`
    };
  }

  /**
   * Build mode-specific system prompt
   */
  private buildModeSpecificSystemPrompt(
    mode: TherapeuticMode,
    analysis: EnhancedPatientAnalysis,
    therapeuticApproach: TherapeuticApproachInfo
  ): string {
    const basePrompt = `You are a skilled ${mode} therapist conducting a therapy session. `;
    
    let modeSpecificGuidance = '';
    
    switch (mode) {
      case 'CBT':
        modeSpecificGuidance = `Focus on identifying and challenging cognitive distortions, examining thought patterns, and developing behavioral strategies. Pay attention to the patient's automatic thoughts, behavioral triggers, and mood patterns. Use structured, goal-oriented interventions.`;
        break;
      case 'DBT':
        modeSpecificGuidance = `Focus on emotional regulation, distress tolerance, interpersonal effectiveness, and mindfulness skills. Pay attention to the patient's emotional intensity, crisis situations, relationship patterns, and present-moment awareness. Balance acceptance and change strategies.`;
        break;
      case 'psychodynamic':
        modeSpecificGuidance = `Focus on unconscious patterns, defense mechanisms, transference dynamics, and early relational experiences. Pay attention to recurring themes, symbolic content, relationship patterns, and what might be happening between you and the patient. Explore deeper meanings and connections.`;
        break;
    }

    const analysisContext = this.buildAnalysisContext(mode, analysis);
    
    return `${basePrompt}${modeSpecificGuidance}

Current patient analysis from ${mode} perspective:
${analysisContext}

Therapeutic approach: ${therapeuticApproach.name}
Selected technique: ${therapeuticApproach.selectedTechnique.name}
Technique description: ${therapeuticApproach.selectedTechnique.description}

Provide a therapeutic response that demonstrates the ${mode} approach and incorporates the selected technique. Keep your response natural, empathetic, and therapeutically appropriate. Display both the overarching therapeutic approach (${therapeuticApproach.name}) and the specific technique (${therapeuticApproach.selectedTechnique.name}) you're using.`;
  }

  /**
   * Build analysis context string for system prompt
   */
  private buildAnalysisContext(mode: TherapeuticMode, analysis: EnhancedPatientAnalysis): string {
    const baseContext = `Sentiment: ${analysis.sentiment}, Motivation: ${analysis.motivationLevel}, Engagement: ${analysis.engagementLevel}`;
    
    // Add mode-specific analysis context
    let modeContext = '';
    
    if (mode === 'CBT' && 'cognitivePatterns' in analysis.modeSpecificAnalysis) {
      const cbtAnalysis = analysis.modeSpecificAnalysis as any;
      modeContext = `Cognitive flexibility: ${cbtAnalysis.cognitivePatterns?.cognitiveFlexibility || 'unknown'}, Behavioral activation: ${cbtAnalysis.behavioralTriggers?.behavioralActivation || 'unknown'}`;
    } else if (mode === 'DBT' && 'emotionalRegulation' in analysis.modeSpecificAnalysis) {
      const dbtAnalysis = analysis.modeSpecificAnalysis as any;
      modeContext = `Emotional intensity: ${dbtAnalysis.emotionalRegulation?.emotionalIntensity || 'unknown'}, Distress level: ${dbtAnalysis.distressTolerance?.currentDistressLevel || 'unknown'}`;
    } else if (mode === 'psychodynamic' && 'unconsciousPatterns' in analysis.modeSpecificAnalysis) {
      const psychoAnalysis = analysis.modeSpecificAnalysis as any;
      modeContext = `Defense flexibility: ${psychoAnalysis.defenseMechanisms?.defenseFlexibility || 'unknown'}, Alliance quality: ${psychoAnalysis.transferencedynamics?.therapeuticAllianceQuality || 'unknown'}`;
    }
    
    return `${baseContext}. ${modeContext}`;
  }

  /**
   * Generate mode-specific thinking process
   */
  private async generateModeSpecificThinking(
    mode: TherapeuticMode,
    patientMessage: string,
    analysis: EnhancedPatientAnalysis,
    therapeuticApproach: TherapeuticApproachInfo
  ): Promise<string> {
    const context = `Patient said: "${patientMessage}". Using ${mode} approach with ${therapeuticApproach.selectedTechnique.name} technique.`;

    let thinkingPrompt = '';

    switch (mode) {
      case 'CBT':
        thinkingPrompt = `As a CBT therapist, analyze: What cognitive patterns or distortions do I notice? What behavioral interventions might help? How can I structure this therapeutically?`;
        break;
      case 'DBT':
        thinkingPrompt = `As a DBT therapist, analyze: What emotional regulation needs do I see? What skills might be most helpful? How can I balance acceptance and change?`;
        break;
      case 'psychodynamic':
        thinkingPrompt = `As a psychodynamic therapist, analyze: What unconscious patterns emerge? What might this reveal about internal conflicts? What is happening in our therapeutic relationship?`;
        break;
    }

    const fullPrompt = `${context}

${thinkingPrompt}

Provide concise internal therapeutic reasoning (under 100 tokens):`;

    try {
      return await this.openaiService.generateThinking('therapist', context, fullPrompt);
    } catch (error) {
      console.error(`❌ Error generating ${mode} thinking:`, error);
      return `Analyzing the patient's words from a ${mode} perspective and considering the most therapeutic response approach...`;
    }
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    patientMessage: string,
    conversationContext: ConversationContext
  ): Array<{ role: 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'user' | 'assistant'; content: string }> = [];

    // Add recent conversation history (last 10 messages to stay within token limits)
    const recentMessages = conversationContext.messages.slice(-10);

    for (const msg of recentMessages) {
      if (msg.sender === 'patient') {
        messages.push({ role: 'user', content: msg.content });
      } else if (msg.sender === 'therapist') {
        messages.push({ role: 'assistant', content: msg.content });
      }
    }

    // Add current patient message
    messages.push({ role: 'user', content: patientMessage });

    return messages;
  }

  /**
   * Map therapeutic mode to persona type for compatibility
   */
  private mapModeToPersonaType(mode: TherapeuticMode): any {
    switch (mode) {
      case 'CBT':
        return 'cbt-mode';
      case 'DBT':
        return 'dbt-mode';
      case 'psychodynamic':
        return 'psychodynamic-mode';
      default:
        return 'unknown-mode';
    }
  }

  /**
   * Get display name for therapeutic mode
   */
  private getModeDisplayName(mode: TherapeuticMode): string {
    switch (mode) {
      case 'CBT':
        return 'CBT Therapist';
      case 'DBT':
        return 'DBT Therapist';
      case 'psychodynamic':
        return 'Psychodynamic Therapist';
      default:
        return 'Unknown Therapist';
    }
  }
}
