// LangGraph Workflow State Definitions for MiCA Therapy Application
// Defines state interfaces that extend LangGraph's MessagesState for therapeutic workflows

import { BaseMessage } from '@langchain/core/messages';
import { 
  PatientAnalysis, 
  TherapeuticApproachInfo, 
  MultiTherapistConversationContext,
  SessionMetadata,
  TherapistPersonaType,
  TherapistPersonaResponse,
  PatientResponse,
  EmotionalState
} from '../../types/index.js';

/**
 * Core therapeutic workflow state with messages and therapy-specific context
 * This maintains conversation messages while adding therapy-specific context
 */
export interface TherapeuticState {
  // Messages array for LangGraph compatibility
  messages: BaseMessage[];
  // Patient analysis from OpenAI service
  patientAnalysis?: PatientAnalysis;
  
  // Therapeutic approaches selected for each persona
  therapeuticApproaches?: Record<TherapistPersonaType, TherapeuticApproachInfo>;
  
  // Multi-therapist conversation contexts
  conversationContexts?: MultiTherapistConversationContext;
  
  // Session metadata and configuration
  sessionMetadata?: SessionMetadata;
  
  // Current workflow step for tracking progress
  workflowStep?: 'analysis' | 'approach_selection' | 'response_generation' | 'synthesis' | 'complete';
  
  // Input patient message being processed
  currentPatientMessage?: string;
  
  // Generated responses from each therapist persona
  therapistResponses?: {
    cbtOnly?: TherapistPersonaResponse;
    miFixedPretreatment?: TherapistPersonaResponse;
    dynamicAdaptive?: TherapistPersonaResponse;
  };
  
  // Patient responses to each therapist (for patient agent workflow)
  patientResponses?: {
    cbtOnly?: PatientResponse;
    miFixedPretreatment?: PatientResponse;
    dynamicAdaptive?: PatientResponse;
  };
  
  // Current emotional state (for patient agent)
  emotionalState?: EmotionalState;
  
  // Error information for workflow debugging
  workflowError?: {
    step: string;
    error: string;
    timestamp: string;
  };
  
  // Processing metadata
  processingMetadata?: {
    startTime: string;
    stepTimings: Record<string, number>;
    totalProcessingTime?: number;
  };
}

/**
 * State for patient analysis workflow node
 */
export interface PatientAnalysisState extends TherapeuticState {
  patientAnalysis: PatientAnalysis;
  workflowStep: 'analysis';
}

/**
 * State for therapeutic approach selection workflow node
 */
export interface ApproachSelectionState extends TherapeuticState {
  patientAnalysis: PatientAnalysis;
  therapeuticApproaches: Record<TherapistPersonaType, TherapeuticApproachInfo>;
  workflowStep: 'approach_selection';
}

/**
 * State for multi-therapist response generation workflow node
 */
export interface ResponseGenerationState extends TherapeuticState {
  patientAnalysis: PatientAnalysis;
  therapeuticApproaches: Record<TherapistPersonaType, TherapeuticApproachInfo>;
  therapistResponses: {
    cbtOnly: TherapistPersonaResponse;
    miFixedPretreatment: TherapistPersonaResponse;
    dynamicAdaptive: TherapistPersonaResponse;
  };
  workflowStep: 'response_generation';
}

/**
 * State for final synthesis workflow node
 */
export interface SynthesisState extends TherapeuticState {
  patientAnalysis: PatientAnalysis;
  therapeuticApproaches: Record<TherapistPersonaType, TherapeuticApproachInfo>;
  therapistResponses: {
    cbtOnly: TherapistPersonaResponse;
    miFixedPretreatment: TherapistPersonaResponse;
    dynamicAdaptive: TherapistPersonaResponse;
  };
  workflowStep: 'synthesis';
}

/**
 * Configuration for therapeutic workflow execution
 */
export interface TherapeuticWorkflowConfig {
  // Conversation ID for database persistence
  conversationId: string;
  
  // Patient persona ID if using specific persona
  patientPersonaId?: string;
  
  // Enable/disable specific workflow features
  enablePatientAnalysis: boolean;
  enableApproachSelection: boolean;
  enableMultiTherapistGeneration: boolean;
  
  // Timeout settings for workflow nodes
  nodeTimeouts: {
    analysis: number;
    approachSelection: number;
    responseGeneration: number;
    synthesis: number;
  };
  
  // Retry configuration
  retryConfig: {
    maxRetries: number;
    retryDelay: number;
  };
  
  // Streaming configuration
  streamingEnabled: boolean;
  streamingMode?: 'values' | 'updates' | 'debug';
}

/**
 * Default workflow configuration
 */
export const defaultWorkflowConfig: TherapeuticWorkflowConfig = {
  conversationId: '',
  enablePatientAnalysis: true,
  enableApproachSelection: true,
  enableMultiTherapistGeneration: true,
  nodeTimeouts: {
    analysis: 30000,        // 30 seconds
    approachSelection: 15000, // 15 seconds
    responseGeneration: 60000, // 60 seconds
    synthesis: 10000        // 10 seconds
  },
  retryConfig: {
    maxRetries: 2,
    retryDelay: 1000
  },
  streamingEnabled: true,
  streamingMode: 'values'
};

/**
 * Workflow node identifiers
 */
export const WORKFLOW_NODES = {
  ANALYZE_PATIENT: 'analyze_patient',
  SELECT_APPROACHES: 'select_approaches', 
  GENERATE_RESPONSES: 'generate_responses',
  SYNTHESIZE_RESULTS: 'synthesize_results',
  START: '__start__',
  END: '__end__'
} as const;

/**
 * Workflow edge conditions
 */
export const WORKFLOW_CONDITIONS = {
  ANALYSIS_COMPLETE: 'analysis_complete',
  APPROACHES_SELECTED: 'approaches_selected',
  RESPONSES_GENERATED: 'responses_generated',
  SYNTHESIS_COMPLETE: 'synthesis_complete',
  ERROR_OCCURRED: 'error_occurred'
} as const;

/**
 * Type guards for workflow state validation
 */
export function isPatientAnalysisState(state: TherapeuticState): state is PatientAnalysisState {
  return state.workflowStep === 'analysis' && !!state.patientAnalysis;
}

export function isApproachSelectionState(state: TherapeuticState): state is ApproachSelectionState {
  return state.workflowStep === 'approach_selection' && 
         !!state.patientAnalysis && 
         !!state.therapeuticApproaches;
}

export function isResponseGenerationState(state: TherapeuticState): state is ResponseGenerationState {
  return state.workflowStep === 'response_generation' && 
         !!state.patientAnalysis && 
         !!state.therapeuticApproaches && 
         !!state.therapistResponses;
}

export function isSynthesisState(state: TherapeuticState): state is SynthesisState {
  return state.workflowStep === 'synthesis' && 
         !!state.patientAnalysis && 
         !!state.therapeuticApproaches && 
         !!state.therapistResponses;
}

/**
 * Utility function to create initial therapeutic state
 */
export function createInitialTherapeuticState(
  patientMessage: string,
  conversationContexts: MultiTherapistConversationContext,
  config: Partial<TherapeuticWorkflowConfig> = {}
): TherapeuticState {
  const workflowConfig = { ...defaultWorkflowConfig, ...config };
  
  return {
    messages: [], // Will be populated by LangGraph
    currentPatientMessage: patientMessage,
    conversationContexts,
    workflowStep: 'analysis',
    processingMetadata: {
      startTime: new Date().toISOString(),
      stepTimings: {}
    },
    sessionMetadata: {
      // Use only the fields that exist in SessionMetadata
      // For now, we'll create a minimal session metadata
    } as any // Temporary fix - should be properly typed later
  };
}

/**
 * Utility function to update workflow step timing
 */
export function updateStepTiming(
  state: TherapeuticState, 
  step: string, 
  duration: number
): TherapeuticState {
  if (!state.processingMetadata) {
    state.processingMetadata = {
      startTime: new Date().toISOString(),
      stepTimings: {}
    };
  }
  
  state.processingMetadata.stepTimings[step] = duration;
  
  return state;
}
