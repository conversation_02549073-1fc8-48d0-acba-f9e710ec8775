// LangGraph Workflow Nodes for MiCA Therapy Application
// Individual workflow nodes for therapeutic conversation processing

import { 
  TherapeuticState, 
  PatientAnalysisState,
  ApproachSelectionState,
  ResponseGenerationState,
  updateStepTiming
} from './workflow-state.js';
import {
  PatientAnalysis,
  TherapeuticApproachInfo,
  TherapistPersonaType,
  TherapistPersonaResponse
} from '../../types/index.js';
import { OpenAIService } from '../openai.js';
import {
  BaseTherapistPersonaStrategy,
  PersonaStrategyContext
} from '../therapist-persona-strategies.js';

// Define TherapistPersonaStrategies type
export interface TherapistPersonaStrategies {
  'cbt-only': BaseTherapistPersonaStrategy;
  'mi-fixed-pretreatment': BaseTherapistPersonaStrategy;
  'dynamic-adaptive': BaseTherapistPersonaStrategy;
}
import { 
  openaiLogger,
  logFunctionEntry,
  logFunctionSuccess,
  logFunctionError
} from '../../utils/logger.js';

/**
 * Workflow node for analyzing patient messages
 * Processes the patient's input and generates comprehensive analysis
 */
export async function analyzePatientNode(
  state: TherapeuticState,
  openaiService: OpenAIService
): Promise<Partial<TherapeuticState>> {
  const operation = 'analyzePatientNode';
  const startTime = Date.now();
  
  logFunctionEntry(openaiLogger, operation, {
    patientMessage: state.currentPatientMessage?.substring(0, 100) + '...',
    conversationId: state.conversationContexts?.id
  });

  try {
    if (!state.currentPatientMessage) {
      throw new Error('No patient message provided for analysis');
    }

    // Perform comprehensive patient analysis using existing OpenAI service
    const patientAnalysis = await openaiService.analyzePatient(state.currentPatientMessage);

    const duration = Date.now() - startTime;
    const updatedState = updateStepTiming(state, 'analysis', duration);

    logFunctionSuccess(openaiLogger, operation, {
      analysisGenerated: true,
      sentiment: patientAnalysis.sentiment,
      readinessScore: patientAnalysis.readinessScore,
      processingTime: duration
    });

    return {
      ...updatedState,
      patientAnalysis,
      workflowStep: 'approach_selection'
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    logFunctionError(openaiLogger, operation, error as Error);
    
    return {
      ...updateStepTiming(state, 'analysis', duration),
      workflowError: {
        step: 'analysis',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      },
      workflowStep: 'complete'
    };
  }
}

/**
 * Workflow node for selecting therapeutic approaches
 * Determines appropriate therapeutic approaches for each persona based on patient analysis
 */
export async function selectApproachesNode(
  state: TherapeuticState,
  strategies: TherapistPersonaStrategies
): Promise<Partial<TherapeuticState>> {
  const operation = 'selectApproachesNode';
  const startTime = Date.now();
  
  logFunctionEntry(openaiLogger, operation, {
    patientAnalysisAvailable: !!state.patientAnalysis,
    conversationId: state.conversationContexts?.id
  });

  try {
    if (!state.patientAnalysis) {
      throw new Error('Patient analysis not available for approach selection');
    }

    if (!state.conversationContexts) {
      throw new Error('Conversation contexts not available for approach selection');
    }

    // Create strategy context for all personas
    const strategyContext: PersonaStrategyContext = {
      messageCount: state.conversationContexts.currentTurn,
      previousTechniques: [], // Will be set per persona
      sessionPhase: getSessionPhase(state.conversationContexts.currentTurn),
      conversationHistory: buildConversationHistory(state.conversationContexts)
    };

    // Select therapeutic approaches for each persona
    const therapeuticApproaches: Record<TherapistPersonaType, TherapeuticApproachInfo> = {
      'cbt-only': strategies['cbt-only'].selectApproachAndTechnique(
        state.patientAnalysis,
        { ...strategyContext, previousTechniques: [] } // TODO: Get from conversation history
      ),
      'mi-fixed-pretreatment': strategies['mi-fixed-pretreatment'].selectApproachAndTechnique(
        state.patientAnalysis,
        { ...strategyContext, previousTechniques: [] } // TODO: Get from conversation history
      ),
      'dynamic-adaptive': strategies['dynamic-adaptive'].selectApproachAndTechnique(
        state.patientAnalysis,
        { ...strategyContext, previousTechniques: [] } // TODO: Get from conversation history
      )
    };

    const duration = Date.now() - startTime;
    const updatedState = updateStepTiming(state, 'approach_selection', duration);

    logFunctionSuccess(openaiLogger, operation, {
      approachesSelected: Object.keys(therapeuticApproaches).length,
      cbtApproach: therapeuticApproaches['cbt-only'].name,
      miApproach: therapeuticApproaches['mi-fixed-pretreatment'].name,
      dynamicApproach: therapeuticApproaches['dynamic-adaptive'].name,
      processingTime: duration
    });

    return {
      ...updatedState,
      therapeuticApproaches,
      workflowStep: 'response_generation'
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    logFunctionError(openaiLogger, operation, error as Error);
    
    return {
      ...updateStepTiming(state, 'approach_selection', duration),
      workflowError: {
        step: 'approach_selection',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      },
      workflowStep: 'complete'
    };
  }
}

/**
 * Workflow node for generating multi-therapist responses
 * Creates responses from all three therapeutic personas in parallel
 */
export async function generateResponsesNode(
  state: TherapeuticState,
  openaiService: OpenAIService,
  strategies: TherapistPersonaStrategies
): Promise<Partial<TherapeuticState>> {
  const operation = 'generateResponsesNode';
  const startTime = Date.now();
  
  logFunctionEntry(openaiLogger, operation, {
    patientAnalysisAvailable: !!state.patientAnalysis,
    therapeuticApproachesAvailable: !!state.therapeuticApproaches,
    conversationId: state.conversationContexts?.id
  });

  try {
    if (!state.patientAnalysis || !state.therapeuticApproaches || !state.conversationContexts) {
      throw new Error('Required state not available for response generation');
    }

    if (!state.currentPatientMessage) {
      throw new Error('Patient message not available for response generation');
    }

    // Generate responses from all three personas in parallel (maintaining existing pattern)
    const [cbtOnlyResponse, miFixedResponse, dynamicResponse] = await Promise.all([
      generatePersonaResponse(
        'cbt-only',
        state.currentPatientMessage,
        state.patientAnalysis,
        state.therapeuticApproaches['cbt-only'],
        state.conversationContexts.therapistConversations.cbtOnly,
        openaiService,
        strategies
      ),
      generatePersonaResponse(
        'mi-fixed-pretreatment',
        state.currentPatientMessage,
        state.patientAnalysis,
        state.therapeuticApproaches['mi-fixed-pretreatment'],
        state.conversationContexts.therapistConversations.miFixedPretreatment,
        openaiService,
        strategies
      ),
      generatePersonaResponse(
        'dynamic-adaptive',
        state.currentPatientMessage,
        state.patientAnalysis,
        state.therapeuticApproaches['dynamic-adaptive'],
        state.conversationContexts.therapistConversations.dynamicAdaptive,
        openaiService,
        strategies
      )
    ]);

    const therapistResponses = {
      cbtOnly: cbtOnlyResponse,
      miFixedPretreatment: miFixedResponse,
      dynamicAdaptive: dynamicResponse
    };

    const duration = Date.now() - startTime;
    const updatedState = updateStepTiming(state, 'response_generation', duration);

    logFunctionSuccess(openaiLogger, operation, {
      responsesGenerated: 3,
      cbtConfidence: cbtOnlyResponse.metadata.confidence,
      miConfidence: miFixedResponse.metadata.confidence,
      dynamicConfidence: dynamicResponse.metadata.confidence,
      processingTime: duration
    });

    return {
      ...updatedState,
      therapistResponses,
      workflowStep: 'synthesis'
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    logFunctionError(openaiLogger, operation, error as Error);
    
    return {
      ...updateStepTiming(state, 'response_generation', duration),
      workflowError: {
        step: 'response_generation',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      },
      workflowStep: 'complete'
    };
  }
}

/**
 * Workflow node for synthesizing final results
 * Combines all generated responses and prepares final output
 */
export async function synthesizeResultsNode(
  state: TherapeuticState
): Promise<Partial<TherapeuticState>> {
  const operation = 'synthesizeResultsNode';
  const startTime = Date.now();
  
  logFunctionEntry(openaiLogger, operation, {
    therapistResponsesAvailable: !!state.therapistResponses,
    conversationId: state.conversationContexts?.id
  });

  try {
    if (!state.therapistResponses || !state.processingMetadata) {
      throw new Error('Required state not available for synthesis');
    }

    // Calculate total processing time
    const totalProcessingTime = Date.now() - new Date(state.processingMetadata.startTime).getTime();
    
    const duration = Date.now() - startTime;
    const updatedState = updateStepTiming(state, 'synthesis', duration);

    // Update total processing time
    if (updatedState.processingMetadata) {
      updatedState.processingMetadata.totalProcessingTime = totalProcessingTime;
    }

    logFunctionSuccess(openaiLogger, operation, {
      synthesisComplete: true,
      totalProcessingTime,
      stepTimings: updatedState.processingMetadata?.stepTimings
    });

    return {
      ...updatedState,
      workflowStep: 'complete'
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    logFunctionError(openaiLogger, operation, error as Error);
    
    return {
      ...updateStepTiming(state, 'synthesis', duration),
      workflowError: {
        step: 'synthesis',
        error: (error as Error).message,
        timestamp: new Date().toISOString()
      },
      workflowStep: 'complete'
    };
  }
}

// Helper functions

/**
 * Generate response from a specific therapist persona
 * Replicates the logic from MultiTherapistAgentService.generatePersonaResponse
 */
async function generatePersonaResponse(
  personaKey: TherapistPersonaType,
  patientMessage: string,
  patientAnalysis: PatientAnalysis,
  therapeuticApproach: TherapeuticApproachInfo,
  conversationContext: any,
  openaiService: OpenAIService,
  strategies: TherapistPersonaStrategies
): Promise<TherapistPersonaResponse> {
  // This will delegate to the existing persona strategy implementation
  // to maintain consistency with current behavior
  const strategy = strategies[personaKey];

  // Build system prompt with therapeutic approach
  const systemPrompt = `You are ${strategy.name}. ${strategy.description}

  Current therapeutic approach: ${therapeuticApproach.name}
  Selected technique: ${therapeuticApproach.selectedTechnique.name}
  Technique description: ${therapeuticApproach.selectedTechnique.description}

  Patient analysis summary:
  - Sentiment: ${patientAnalysis.sentiment}
  - Readiness score: ${patientAnalysis.readinessScore.score}/10
  - Motivation level: ${patientAnalysis.motivationLevel}
  - Engagement level: ${patientAnalysis.engagementLevel}

  Respond as a professional therapist using the specified approach and technique.`;

  // Generate response using OpenAI service
  const response = await openaiService.generateResponse({
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: patientMessage }
    ],
    temperature: 0.7,
    max_completion_tokens: 1000
  });

  return {
    message: response.message,
    thinking: `Using ${therapeuticApproach.name} with ${therapeuticApproach.selectedTechnique.name}`,
    metadata: {
      confidence: 0.85, // Default confidence
      processingTime: 0, // Will be calculated by caller
      patientAnalysis,
      therapeuticApproach
    },
    personaType: personaKey,
    personaName: strategy.name,
    strategyState: strategy.getStrategyState()
  };
}

/**
 * Determine session phase based on message count
 */
function getSessionPhase(messageCount: number): 'opening' | 'middle' | 'closing' {
  if (messageCount <= 2) return 'opening';
  if (messageCount >= 8) return 'closing';
  return 'middle';
}

/**
 * Build conversation history from multi-therapist context
 */
function buildConversationHistory(context: any): Array<{
  sender: 'therapist' | 'patient';
  content: string;
  timestamp: string;
}> {
  // Simplified implementation - can be enhanced based on actual context structure
  return [];
}
