// LangGraph Workflow Utilities for MiCA Therapy Application
// Utility functions for workflow management and state handling

import { TherapeuticState, TherapeuticWorkflowConfig } from './workflow-state.js';
import { MultiTherapistConversationContext, ConversationContext } from '../../types/index.js';

/**
 * Validate therapeutic state for workflow execution
 */
export function validateTherapeuticState(state: TherapeuticState): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!state.currentPatientMessage) {
    errors.push('Patient message is required');
  }

  if (!state.conversationContexts) {
    errors.push('Conversation contexts are required');
  }

  if (state.workflowStep === 'approach_selection' && !state.patientAnalysis) {
    errors.push('Patient analysis is required for approach selection');
  }

  if (state.workflowStep === 'response_generation' && (!state.patientAnalysis || !state.therapeuticApproaches)) {
    errors.push('Patient analysis and therapeutic approaches are required for response generation');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Extract conversation history from multi-therapist context
 */
export function extractConversationHistory(
  context: MultiTherapistConversationContext,
  personaType?: 'cbtOnly' | 'miFixedPretreatment' | 'dynamicAdaptive'
): string[] {
  const history: string[] = [];

  if (personaType) {
    // Extract history for specific persona
    const personaContext = context.therapistConversations[personaType];
    if (personaContext && personaContext.messages) {
      personaContext.messages.forEach(message => {
        history.push(`${message.sender}: ${message.content}`);
      });
    }
  } else {
    // Extract general patient messages
    if (context.patientMessages) {
      context.patientMessages.forEach(message => {
        history.push(`patient: ${message.content}`);
      });
    }
  }

  return history;
}

/**
 * Calculate session phase based on conversation progress
 */
export function calculateSessionPhase(
  currentTurn: number,
  maxTurns: number
): 'opening' | 'middle' | 'closing' {
  const progress = currentTurn / maxTurns;
  
  if (progress <= 0.2) return 'opening';
  if (progress >= 0.8) return 'closing';
  return 'middle';
}

/**
 * Generate workflow configuration from conversation context
 */
export function generateWorkflowConfig(
  conversationContext: MultiTherapistConversationContext,
  overrides: Partial<TherapeuticWorkflowConfig> = {}
): TherapeuticWorkflowConfig {
  const baseConfig: TherapeuticWorkflowConfig = {
    conversationId: conversationContext.id,
    patientPersonaId: conversationContext.studyMetadata?.patientPersonaId,
    enablePatientAnalysis: true,
    enableApproachSelection: true,
    enableMultiTherapistGeneration: true,
    nodeTimeouts: {
      analysis: 30000,
      approachSelection: 15000,
      responseGeneration: 60000,
      synthesis: 10000
    },
    retryConfig: {
      maxRetries: 2,
      retryDelay: 1000
    },
    streamingEnabled: true,
    streamingMode: 'values'
  };

  return { ...baseConfig, ...overrides };
}

/**
 * Format workflow timing information for logging
 */
export function formatWorkflowTimings(state: TherapeuticState): Record<string, any> {
  if (!state.processingMetadata) {
    return {};
  }

  const timings = state.processingMetadata.stepTimings;
  const totalTime = state.processingMetadata.totalProcessingTime;

  return {
    totalProcessingTime: totalTime,
    stepTimings: timings,
    stepPercentages: totalTime ? Object.entries(timings).reduce((acc, [step, time]) => {
      acc[step] = Math.round((time / totalTime) * 100);
      return acc;
    }, {} as Record<string, number>) : {},
    averageStepTime: Object.values(timings).length > 0 
      ? Math.round(Object.values(timings).reduce((sum, time) => sum + time, 0) / Object.values(timings).length)
      : 0
  };
}

/**
 * Check if workflow state indicates an error condition
 */
export function hasWorkflowError(state: TherapeuticState): boolean {
  return !!state.workflowError || state.workflowStep === 'complete' && !state.therapistResponses;
}

/**
 * Get workflow error details
 */
export function getWorkflowErrorDetails(state: TherapeuticState): {
  hasError: boolean;
  errorStep?: string;
  errorMessage?: string;
  errorTimestamp?: string;
} {
  if (!state.workflowError) {
    return { hasError: false };
  }

  return {
    hasError: true,
    errorStep: state.workflowError.step,
    errorMessage: state.workflowError.error,
    errorTimestamp: state.workflowError.timestamp
  };
}

/**
 * Create a summary of workflow execution
 */
export function createWorkflowSummary(state: TherapeuticState): {
  status: 'completed' | 'error' | 'in_progress';
  currentStep: string;
  stepsCompleted: string[];
  totalTime?: number;
  errorDetails?: any;
} {
  const stepsCompleted: string[] = [];
  
  if (state.patientAnalysis) stepsCompleted.push('analysis');
  if (state.therapeuticApproaches) stepsCompleted.push('approach_selection');
  if (state.therapistResponses) stepsCompleted.push('response_generation');
  if (state.workflowStep === 'complete') stepsCompleted.push('synthesis');

  let status: 'completed' | 'error' | 'in_progress' = 'in_progress';
  
  if (state.workflowError) {
    status = 'error';
  } else if (state.workflowStep === 'complete' && state.therapistResponses) {
    status = 'completed';
  }

  return {
    status,
    currentStep: state.workflowStep || 'unknown',
    stepsCompleted,
    totalTime: state.processingMetadata?.totalProcessingTime,
    errorDetails: state.workflowError ? getWorkflowErrorDetails(state) : undefined
  };
}

/**
 * Merge workflow states (useful for state updates)
 */
export function mergeWorkflowStates(
  baseState: TherapeuticState,
  updateState: Partial<TherapeuticState>
): TherapeuticState {
  return {
    ...baseState,
    ...updateState,
    // Merge processing metadata carefully
    processingMetadata: updateState.processingMetadata ? {
      ...baseState.processingMetadata,
      ...updateState.processingMetadata,
      stepTimings: {
        ...baseState.processingMetadata?.stepTimings,
        ...updateState.processingMetadata.stepTimings
      }
    } : baseState.processingMetadata,
    // Merge therapist responses carefully
    therapistResponses: updateState.therapistResponses ? {
      ...baseState.therapistResponses,
      ...updateState.therapistResponses
    } : baseState.therapistResponses
  };
}

/**
 * Convert conversation context to LangGraph-compatible format
 */
export function convertConversationContext(
  context: ConversationContext
): { messages: Array<{ role: string; content: string }> } {
  const messages = context.messages.map(msg => ({
    role: msg.sender === 'therapist' ? 'assistant' : 'user',
    content: msg.content
  }));

  return { messages };
}

/**
 * Estimate workflow completion percentage
 */
export function estimateWorkflowProgress(state: TherapeuticState): number {
  const stepWeights = {
    analysis: 25,
    approach_selection: 15,
    response_generation: 50,
    synthesis: 10
  };

  let completedWeight = 0;
  const totalWeight = Object.values(stepWeights).reduce((sum, weight) => sum + weight, 0);

  if (state.patientAnalysis) completedWeight += stepWeights.analysis;
  if (state.therapeuticApproaches) completedWeight += stepWeights.approach_selection;
  if (state.therapistResponses) completedWeight += stepWeights.response_generation;
  if (state.workflowStep === 'complete') completedWeight += stepWeights.synthesis;

  return Math.round((completedWeight / totalWeight) * 100);
}

/**
 * Validate workflow configuration
 */
export function validateWorkflowConfig(config: TherapeuticWorkflowConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.conversationId) {
    errors.push('Conversation ID is required');
  }

  if (config.nodeTimeouts.analysis <= 0) {
    errors.push('Analysis timeout must be positive');
  }

  if (config.nodeTimeouts.responseGeneration <= 0) {
    errors.push('Response generation timeout must be positive');
  }

  if (config.retryConfig.maxRetries < 0) {
    errors.push('Max retries cannot be negative');
  }

  if (config.retryConfig.retryDelay < 0) {
    errors.push('Retry delay cannot be negative');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
