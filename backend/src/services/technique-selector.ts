// Therapeutic Technique Selection Service for MiCA Therapy Simulation
import {
  TherapeuticTechnique,
  TherapeuticApproach,
  therapeuticApproaches
} from '../data/therapeutic-approaches.js';
import { PatientAnalysis, TherapeuticApproachInfo } from '../types/index.js';
import {
  openaiLogger,
  logFunctionEntry,
  logFunctionSuccess,
  logFunctionError,
} from '../utils/logger.js';
import { promptConfigService } from './prompt-config-service.js';

export class TechniqueSelector {
  
  /**
   * Select the most appropriate therapeutic approach and technique based on patient analysis
   */
  selectApproachAndTechnique(
    patientAnalysis: PatientAnalysis,
    conversationContext?: {
      messageCount: number;
      previousTechniques: string[];
      sessionPhase: 'opening' | 'middle' | 'closing';
    }
  ): TherapeuticApproachInfo {
    const operation = 'selectApproachAndTechnique';
    
    logFunctionEntry(openaiLogger, operation, {
      readinessScore: patientAnalysis.readinessScore.score,
      recommendedApproach: patientAnalysis.readinessScore.recommendedApproach,
      sentiment: patientAnalysis.sentiment,
      motivation: patientAnalysis.motivationLevel,
      engagement: patientAnalysis.engagementLevel,
    });

    try {
      // Select approach based on readiness score
      const selectedApproach = this.selectApproach(patientAnalysis.readinessScore);
      
      // Select specific technique within the approach
      const selectedTechnique = this.selectTechnique(
        selectedApproach,
        patientAnalysis,
        conversationContext
      );

      const result: TherapeuticApproachInfo = {
        id: selectedApproach.id as 'motivational-interviewing' | 'cognitive-behavioral-therapy',
        name: selectedApproach.name,
        selectedTechnique: {
          id: selectedTechnique.id,
          name: selectedTechnique.name,
          description: selectedTechnique.description
        }
      };

      logFunctionSuccess(openaiLogger, operation, {
        selectedApproach: selectedApproach.name,
        selectedTechnique: selectedTechnique.name,
        readinessScore: patientAnalysis.readinessScore.score,
      });

      return result;

    } catch (error) {
      logFunctionError(openaiLogger, operation, error);
      
      // Return default MI approach on error
      const defaultApproach = therapeuticApproaches.find(a => a.id === 'motivational-interviewing')!;
      const defaultTechnique = defaultApproach.techniques[0];

      if (!defaultTechnique) {
        throw new Error('No default technique available');
      }

      return {
        id: defaultApproach.id as 'motivational-interviewing' | 'cognitive-behavioral-therapy',
        name: defaultApproach.name,
        selectedTechnique: {
          id: defaultTechnique.id,
          name: defaultTechnique.name,
          description: defaultTechnique.description
        }
      };
    }
  }

  /**
   * Select therapeutic approach based on readiness score
   */
  private selectApproach(readinessScore: PatientAnalysis['readinessScore']): TherapeuticApproach {
    const score = readinessScore.score;
    
    // Use readiness threshold to determine approach
    if (score >= 8) {
      return therapeuticApproaches.find(a => a.id === 'cognitive-behavioral-therapy')!;
    } else {
      return therapeuticApproaches.find(a => a.id === 'motivational-interviewing')!;
    }
  }

  /**
   * Select specific technique within the chosen approach
   */
  public selectTechnique(
    approach: TherapeuticApproach,
    patientAnalysis: PatientAnalysis,
    conversationContext?: {
      messageCount: number;
      previousTechniques: string[];
      sessionPhase: 'opening' | 'middle' | 'closing';
    }
  ): TherapeuticTechnique {
    const operation = 'selectTechnique';
    
    logFunctionEntry(openaiLogger, operation, {
      approach: approach.name,
      patientSentiment: patientAnalysis.sentiment,
      patientMotivation: patientAnalysis.motivationLevel,
      patientEngagement: patientAnalysis.engagementLevel,
    });

    // Get patient state indicators
    const patientStates = this.getPatientStates(patientAnalysis);
    
    // Filter techniques based on patient state and context
    let suitableTechniques = approach.techniques.filter(technique => {
      // Check if technique is appropriate for current patient states
      const stateMatch = technique.selectionCriteria.patientStates.some(state => 
        patientStates.includes(state)
      );
      
      // Avoid recently used techniques if possible
      const recentlyUsed = conversationContext?.previousTechniques.includes(technique.id) || false;
      
      return stateMatch && !recentlyUsed;
    });

    // If no suitable techniques found (all recently used), allow any appropriate technique
    if (suitableTechniques.length === 0) {
      suitableTechniques = approach.techniques.filter(technique => 
        technique.selectionCriteria.patientStates.some(state => 
          patientStates.includes(state)
        )
      );
    }

    // If still no suitable techniques, use the first technique as fallback
    if (suitableTechniques.length === 0 && approach.techniques.length > 0) {
      suitableTechniques = [approach.techniques[0]!];
    }

    // Select technique based on session phase and context
    const selectedTechnique = this.prioritizeTechnique(
      suitableTechniques,
      patientAnalysis,
      conversationContext
    );

    logFunctionSuccess(openaiLogger, operation, {
      selectedTechnique: selectedTechnique.name,
      suitableCount: suitableTechniques.length,
      patientStates: patientStates.join(', '),
    });

    return selectedTechnique;
  }

  /**
   * Determine patient states based on analysis
   */
  private getPatientStates(patientAnalysis: PatientAnalysis): string[] {
    const states: string[] = [];
    
    // Add states based on sentiment
    if (patientAnalysis.sentiment === 'negative') {
      states.push('negative thinking', 'emotional', 'discouraged');
    } else if (patientAnalysis.sentiment === 'positive') {
      states.push('hopeful', 'motivated', 'engaged');
    } else {
      states.push('neutral', 'processing');
    }
    
    // Add states based on motivation
    if (patientAnalysis.motivationLevel === 'low') {
      states.push('ambivalent', 'resistant', 'low motivation');
    } else if (patientAnalysis.motivationLevel === 'high') {
      states.push('motivated for change', 'ready for action', 'committed');
    } else {
      states.push('considering change', 'exploring');
    }
    
    // Add states based on engagement
    if (patientAnalysis.engagementLevel === 'low') {
      states.push('withdrawn', 'guarded', 'resistant');
    } else if (patientAnalysis.engagementLevel === 'high') {
      states.push('engaged', 'open', 'sharing');
    } else {
      states.push('moderately engaged', 'cautious');
    }
    
    // Add readiness-based states
    if (patientAnalysis.readinessScore.score >= 8) {
      states.push('ready for change', 'ready for CBT', 'goal-oriented');
    } else {
      states.push('building readiness', 'exploring motivation', 'ambivalent');
    }
    
    return states;
  }

  /**
   * Prioritize technique selection based on context
   */
  private prioritizeTechnique(
    techniques: TherapeuticTechnique[],
    patientAnalysis: PatientAnalysis,
    conversationContext?: {
      messageCount: number;
      previousTechniques: string[];
      sessionPhase: 'opening' | 'middle' | 'closing';
    }
  ): TherapeuticTechnique {
    // If only one technique available, return it
    if (techniques.length === 1) {
      return techniques[0]!;
    }

    // If no techniques available, throw error
    if (techniques.length === 0) {
      throw new Error('No techniques available for selection');
    }

    // Prioritize based on session phase
    if (conversationContext?.sessionPhase === 'opening') {
      // Prefer rapport-building techniques in opening
      const rapportTechniques = techniques.filter(t => 
        t.id.includes('open-questions') || 
        t.id.includes('reflective-listening') ||
        t.id.includes('affirmations')
      );
      if (rapportTechniques.length > 0) {
        return rapportTechniques[0]!;
      }
    }

    // Prioritize based on patient state urgency
    if (patientAnalysis.sentiment === 'negative' && patientAnalysis.engagementLevel === 'low') {
      // Prefer supportive techniques for distressed, disengaged patients
      const supportiveTechniques = techniques.filter(t => 
        t.id.includes('affirmations') || 
        t.id.includes('reflective-listening') ||
        t.id.includes('rolling-resistance')
      );
      if (supportiveTechniques.length > 0) {
        return supportiveTechniques[0]!;
      }
    }

    // Default to first suitable technique
    return techniques[0]!;
  }

  /**
   * Get technique prompt for response generation
   * First tries centralized configuration, then falls back to hardcoded techniques
   */
  getTechniquePrompt(techniqueId: string): string {
    // First try to get from centralized configuration
    this.getTechniquePromptFromConfig(techniqueId)
      .then(prompt => prompt)
      .catch(() => {
        // Fallback to hardcoded techniques
        const allTechniques = [
          ...therapeuticApproaches.find(a => a.id === 'motivational-interviewing')!.techniques,
          ...therapeuticApproaches.find(a => a.id === 'cognitive-behavioral-therapy')!.techniques
        ];

        const technique = allTechniques.find(t => t.id === techniqueId);
        return technique?.prompt || 'Respond therapeutically with empathy and understanding.';
      });

    // For now, return the fallback synchronously (this method needs to remain sync for compatibility)
    const allTechniques = [
      ...therapeuticApproaches.find(a => a.id === 'motivational-interviewing')!.techniques,
      ...therapeuticApproaches.find(a => a.id === 'cognitive-behavioral-therapy')!.techniques
    ];

    const technique = allTechniques.find(t => t.id === techniqueId);
    return technique?.prompt || 'Respond therapeutically with empathy and understanding.';
  }

  /**
   * Get technique prompt from centralized configuration (async version)
   */
  private async getTechniquePromptFromConfig(techniqueId: string): Promise<string> {
    try {
      // Try CBT techniques first
      try {
        return await promptConfigService.getTherapeuticTechniquePrompt('cognitive_behavioral_therapy', techniqueId);
      } catch {
        // Try MI techniques
        return await promptConfigService.getTherapeuticTechniquePrompt('motivational_interviewing', techniqueId);
      }
    } catch (error) {
      throw new Error(`Technique prompt not found in configuration: ${techniqueId}`);
    }
  }
}
