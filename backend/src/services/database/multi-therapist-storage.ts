// Multi-Therapist Session Storage Service
// Specialized storage operations for comparative study sessions

import { query } from '../../config/database.js';
import {
  DatabaseMultiTherapistSession,
  MultiTherapistSessionStorageData,
  TherapistPersonaType
} from '../../types/index.js';

export class MultiTherapistStorageService {

  /**
   * Store multi-therapist session data
   */
  async storeMultiTherapistSession(data: MultiTherapistSessionStorageData): Promise<DatabaseMultiTherapistSession> {
    try {
      const sessionData = {
        conversation_id: data.conversationId,
        persona_type: data.personaType,
        persona_name: data.personaName,
        strategy_state: data.strategyState,
        performance_metrics: data.performanceMetrics
      };

      const insertQuery = `
        INSERT INTO multi_therapist_sessions (conversation_id, persona_type, persona_name, strategy_state, performance_metrics)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `;

      const values = [
        sessionData.conversation_id,
        sessionData.persona_type,
        sessionData.persona_name,
        JSON.stringify(sessionData.strategy_state),
        JSON.stringify(sessionData.performance_metrics)
      ];

      const result = await query(insertQuery, values);

      if (!result.rows || result.rows.length === 0) {
        throw new Error('Failed to store multi-therapist session: No data returned');
      }

      const session = result.rows[0];

      console.log(`✅ Stored multi-therapist session: ${data.personaType} for conversation ${data.conversationId}`);
      return this.mapDatabaseMultiTherapistSession(session);
    } catch (error) {
      console.error('❌ Error in storeMultiTherapistSession:', error);
      throw error;
    }
  }

  /**
   * Update multi-therapist session data
   */
  async updateMultiTherapistSession(
    conversationId: string, 
    personaType: TherapistPersonaType, 
    updates: Partial<MultiTherapistSessionStorageData>
  ): Promise<DatabaseMultiTherapistSession> {
    try {
      const updateData: any = {};
      
      if (updates.personaName) updateData.persona_name = updates.personaName;
      if (updates.strategyState) updateData.strategy_state = updates.strategyState;
      if (updates.performanceMetrics) updateData.performance_metrics = updates.performanceMetrics;

      const updateFields: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (updateData.persona_name) {
        updateFields.push(`persona_name = $${paramIndex++}`);
        values.push(updateData.persona_name);
      }
      if (updateData.strategy_state) {
        updateFields.push(`strategy_state = $${paramIndex++}`);
        values.push(JSON.stringify(updateData.strategy_state));
      }
      if (updateData.performance_metrics) {
        updateFields.push(`performance_metrics = $${paramIndex++}`);
        values.push(JSON.stringify(updateData.performance_metrics));
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateFields.push(`updated_at = NOW()`);
      values.push(conversationId);
      values.push(personaType);

      const updateQuery = `
        UPDATE multi_therapist_sessions
        SET ${updateFields.join(', ')}
        WHERE conversation_id = $${paramIndex++} AND persona_type = $${paramIndex}
        RETURNING *
      `;

      const result = await query(updateQuery, values);

      if (!result.rows || result.rows.length === 0) {
        throw new Error('Failed to update multi-therapist session: No data returned');
      }

      const session = result.rows[0];

      console.log(`✅ Updated multi-therapist session: ${personaType} for conversation ${conversationId}`);
      return this.mapDatabaseMultiTherapistSession(session);
    } catch (error) {
      console.error('❌ Error in updateMultiTherapistSession:', error);
      throw error;
    }
  }

  /**
   * Get all multi-therapist sessions for a conversation
   */
  async getMultiTherapistSessions(conversationId: string): Promise<DatabaseMultiTherapistSession[]> {
    try {
      const selectQuery = `
        SELECT * FROM multi_therapist_sessions
        WHERE conversation_id = $1
        ORDER BY created_at ASC
      `;

      const result = await query(selectQuery, [conversationId]);
      const sessions = result.rows;

      return sessions.map(session => this.mapDatabaseMultiTherapistSession(session));
    } catch (error) {
      console.error('❌ Error in getMultiTherapistSessions:', error);
      throw error;
    }
  }

  /**
   * Get specific persona session for a conversation
   */
  async getPersonaSession(conversationId: string, personaType: TherapistPersonaType): Promise<DatabaseMultiTherapistSession | null> {
    try {
      const selectQuery = `
        SELECT * FROM multi_therapist_sessions
        WHERE conversation_id = $1 AND persona_type = $2
      `;

      const result = await query(selectQuery, [conversationId, personaType]);

      if (!result.rows || result.rows.length === 0) {
        return null;
      }

      const session = result.rows[0];

      return this.mapDatabaseMultiTherapistSession(session);
    } catch (error) {
      console.error('❌ Error in getPersonaSession:', error);
      throw error;
    }
  }

  /**
   * Get performance comparison data for a conversation
   */
  async getPerformanceComparison(conversationId: string): Promise<{
    cbtOnly?: DatabaseMultiTherapistSession;
    miFixedPretreatment?: DatabaseMultiTherapistSession;
    dynamicAdaptive?: DatabaseMultiTherapistSession;
  }> {
    try {
      const sessions = await this.getMultiTherapistSessions(conversationId);
      
      const comparison: any = {};
      sessions.forEach(session => {
        comparison[this.mapPersonaTypeToKey(session.persona_type)] = session;
      });

      return comparison;
    } catch (error) {
      console.error('❌ Error in getPerformanceComparison:', error);
      throw error;
    }
  }

  /**
   * Store performance metrics for all personas in a conversation
   */
  async storeAllPersonaMetrics(
    conversationId: string,
    metricsData: {
      cbtOnly?: Partial<MultiTherapistSessionStorageData>;
      miFixedPretreatment?: Partial<MultiTherapistSessionStorageData>;
      dynamicAdaptive?: Partial<MultiTherapistSessionStorageData>;
    }
  ): Promise<DatabaseMultiTherapistSession[]> {
    try {
      const results: DatabaseMultiTherapistSession[] = [];

      // Process each persona's metrics
      for (const [personaKey, data] of Object.entries(metricsData)) {
        if (data) {
          const personaType = this.mapKeyToPersonaType(personaKey);
          
          // Check if session exists
          const existingSession = await this.getPersonaSession(conversationId, personaType);
          
          if (existingSession) {
            // Update existing session
            const updated = await this.updateMultiTherapistSession(conversationId, personaType, data);
            results.push(updated);
          } else {
            // Create new session
            const newSession = await this.storeMultiTherapistSession({
              conversationId,
              personaType,
              personaName: data.personaName || this.getDefaultPersonaName(personaType),
              strategyState: data.strategyState,
              performanceMetrics: data.performanceMetrics
            });
            results.push(newSession);
          }
        }
      }

      return results;
    } catch (error) {
      console.error('❌ Error in storeAllPersonaMetrics:', error);
      throw error;
    }
  }

  // ==================== HELPER METHODS ====================

  private mapDatabaseMultiTherapistSession(dbSession: any): DatabaseMultiTherapistSession {
    return {
      id: dbSession.id,
      conversation_id: dbSession.conversation_id,
      persona_type: dbSession.persona_type,
      persona_name: dbSession.persona_name,
      strategy_state: typeof dbSession.strategy_state === 'string' ? JSON.parse(dbSession.strategy_state) : dbSession.strategy_state,
      performance_metrics: typeof dbSession.performance_metrics === 'string' ? JSON.parse(dbSession.performance_metrics) : dbSession.performance_metrics,
      created_at: dbSession.created_at,
      updated_at: dbSession.updated_at
    };
  }

  private mapPersonaTypeToKey(personaType: TherapistPersonaType): string {
    const mapping: { [key in TherapistPersonaType]: string } = {
      'cbt-only': 'cbtOnly',
      'mi-fixed-pretreatment': 'miFixedPretreatment',
      'dynamic-adaptive': 'dynamicAdaptive'
    };
    return mapping[personaType];
  }

  private mapKeyToPersonaType(key: string): TherapistPersonaType {
    const mapping: { [key: string]: TherapistPersonaType } = {
      'cbtOnly': 'cbt-only',
      'miFixedPretreatment': 'mi-fixed-pretreatment',
      'dynamicAdaptive': 'dynamic-adaptive'
    };
    return mapping[key];
  }

  private getDefaultPersonaName(personaType: TherapistPersonaType): string {
    const names: { [key in TherapistPersonaType]: string } = {
      'cbt-only': 'CBT-Only Therapist',
      'mi-fixed-pretreatment': 'MI Fixed Pretreatment Therapist',
      'dynamic-adaptive': 'Dynamic Adaptive Therapist'
    };
    return names[personaType];
  }
}
