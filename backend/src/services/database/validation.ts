// Data Consistency Validation for Conversation Storage System
// Ensures stored data maintains consistency with frontend/backend message schema

import {
  ConversationStorageData,
  MessageStorageData,
  AIAnalysisStorageData,
  TherapeuticApproachStorageData,
  SessionAnalyticsStorageData,
  MultiTherapistSessionStorageData,
  PatientAnalysis,
  TherapeuticApproachInfo,
  ModeSpecificAnalysis,
  SessionMetadata
} from '../../types/index.js';

export class DataValidationService {

  /**
   * Validate conversation storage data
   */
  validateConversationData(data: ConversationStorageData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.status) {
      errors.push('Conversation status is required');
    } else if (!['active', 'completed', 'paused'].includes(data.status)) {
      errors.push('Conversation status must be one of: active, completed, paused');
    }

    if (!data.config) {
      errors.push('Conversation config is required');
    }

    // Optional field validation
    if (data.therapistMode && !['single', 'multi-therapist'].includes(data.therapistMode)) {
      errors.push('Therapist mode must be either "single" or "multi-therapist"');
    }

    if (data.sessionMetadata) {
      const sessionErrors = this.validateSessionMetadata(data.sessionMetadata);
      errors.push(...sessionErrors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate message storage data
   */
  validateMessageData(data: MessageStorageData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.conversationId) {
      errors.push('Conversation ID is required');
    }

    if (!data.sender) {
      errors.push('Message sender is required');
    } else if (!['therapist', 'patient'].includes(data.sender)) {
      errors.push('Message sender must be either "therapist" or "patient"');
    }

    if (!data.content || data.content.trim().length === 0) {
      errors.push('Message content is required and cannot be empty');
    }

    // Optional field validation
    if (data.messageType && !['standard', 'greeting', 'closing', 'intervention'].includes(data.messageType)) {
      errors.push('Message type must be one of: standard, greeting, closing, intervention');
    }

    if (data.confidenceScore !== undefined) {
      if (typeof data.confidenceScore !== 'number' || data.confidenceScore < 0 || data.confidenceScore > 1) {
        errors.push('Confidence score must be a number between 0 and 1');
      }
    }

    if (data.processingTime !== undefined) {
      if (typeof data.processingTime !== 'number' || data.processingTime < 0) {
        errors.push('Processing time must be a non-negative number');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate AI analysis data
   */
  validateAIAnalysisData(data: AIAnalysisStorageData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.messageId) {
      errors.push('Message ID is required');
    }

    if (!data.conversationId) {
      errors.push('Conversation ID is required');
    }

    if (!data.analysisType) {
      errors.push('Analysis type is required');
    } else if (!['patient_analysis', 'therapeutic_approach', 'mode_specific'].includes(data.analysisType)) {
      errors.push('Analysis type must be one of: patient_analysis, therapeutic_approach, mode_specific');
    }

    if (!data.analysisData) {
      errors.push('Analysis data is required');
    } else {
      // Validate analysis data based on type
      const analysisErrors = this.validateAnalysisDataByType(data.analysisType, data.analysisData);
      errors.push(...analysisErrors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate therapeutic approach data
   */
  validateTherapeuticApproachData(data: TherapeuticApproachStorageData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.messageId) {
      errors.push('Message ID is required');
    }

    if (!data.conversationId) {
      errors.push('Conversation ID is required');
    }

    if (!data.approachId) {
      errors.push('Approach ID is required');
    }

    if (!data.approachName) {
      errors.push('Approach name is required');
    }

    // Optional field validation
    if (data.effectivenessPrediction !== undefined) {
      if (typeof data.effectivenessPrediction !== 'number' || 
          data.effectivenessPrediction < 0 || 
          data.effectivenessPrediction > 1) {
        errors.push('Effectiveness prediction must be a number between 0 and 1');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate session analytics data
   */
  validateSessionAnalyticsData(data: SessionAnalyticsStorageData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.conversationId) {
      errors.push('Conversation ID is required');
    }

    if (typeof data.totalMessages !== 'number' || data.totalMessages < 0) {
      errors.push('Total messages must be a non-negative number');
    }

    if (typeof data.patientMessages !== 'number' || data.patientMessages < 0) {
      errors.push('Patient messages must be a non-negative number');
    }

    if (typeof data.therapistMessages !== 'number' || data.therapistMessages < 0) {
      errors.push('Therapist messages must be a non-negative number');
    }

    // Logical validation
    if (data.patientMessages + data.therapistMessages !== data.totalMessages) {
      errors.push('Patient messages + therapist messages must equal total messages');
    }

    // Optional field validation
    if (data.sessionDuration !== undefined && (typeof data.sessionDuration !== 'number' || data.sessionDuration < 0)) {
      errors.push('Session duration must be a non-negative number');
    }

    if (data.avgResponseTime !== undefined && (typeof data.avgResponseTime !== 'number' || data.avgResponseTime < 0)) {
      errors.push('Average response time must be a non-negative number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate multi-therapist session data
   */
  validateMultiTherapistSessionData(data: MultiTherapistSessionStorageData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.conversationId) {
      errors.push('Conversation ID is required');
    }

    if (!data.personaType) {
      errors.push('Persona type is required');
    } else if (!['cbt-only', 'mi-fixed-pretreatment', 'dynamic-adaptive'].includes(data.personaType)) {
      errors.push('Persona type must be one of: cbt-only, mi-fixed-pretreatment, dynamic-adaptive');
    }

    if (!data.personaName) {
      errors.push('Persona name is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate session metadata
   */
  private validateSessionMetadata(metadata: SessionMetadata): string[] {
    const errors: string[] = [];

    if (!metadata.startTime) {
      errors.push('Session start time is required');
    } else {
      // Validate ISO date format
      const startDate = new Date(metadata.startTime);
      if (isNaN(startDate.getTime())) {
        errors.push('Session start time must be a valid ISO date string');
      }
    }

    if (metadata.endTime) {
      const endDate = new Date(metadata.endTime);
      if (isNaN(endDate.getTime())) {
        errors.push('Session end time must be a valid ISO date string');
      }

      // Validate end time is after start time
      if (metadata.startTime) {
        const startDate = new Date(metadata.startTime);
        if (endDate <= startDate) {
          errors.push('Session end time must be after start time');
        }
      }
    }

    if (metadata.duration !== undefined && (typeof metadata.duration !== 'number' || metadata.duration < 0)) {
      errors.push('Session duration must be a non-negative number');
    }

    return errors;
  }

  /**
   * Validate analysis data based on type
   */
  private validateAnalysisDataByType(type: string, data: any): string[] {
    const errors: string[] = [];

    switch (type) {
      case 'patient_analysis':
        errors.push(...this.validatePatientAnalysis(data as PatientAnalysis));
        break;
      case 'therapeutic_approach':
        errors.push(...this.validateTherapeuticApproachInfo(data as TherapeuticApproachInfo));
        break;
      case 'mode_specific':
        errors.push(...this.validateModeSpecificAnalysis(data as ModeSpecificAnalysis));
        break;
      default:
        errors.push(`Unknown analysis type: ${type}`);
    }

    return errors;
  }

  /**
   * Validate patient analysis structure
   */
  private validatePatientAnalysis(analysis: PatientAnalysis): string[] {
    const errors: string[] = [];

    const validSentiments = ['positive', 'negative', 'neutral'];
    const validIntensities = ['low', 'medium', 'high'];
    const validMotivationTypes = ['intrinsic', 'extrinsic', 'mixed'];

    if (!validSentiments.includes(analysis.sentiment)) {
      errors.push(`Invalid sentiment: ${analysis.sentiment}`);
    }

    if (!validIntensities.includes(analysis.sentimentIntensity)) {
      errors.push(`Invalid sentiment intensity: ${analysis.sentimentIntensity}`);
    }

    if (!validIntensities.includes(analysis.motivationLevel)) {
      errors.push(`Invalid motivation level: ${analysis.motivationLevel}`);
    }

    if (!validMotivationTypes.includes(analysis.motivationType)) {
      errors.push(`Invalid motivation type: ${analysis.motivationType}`);
    }

    if (!validIntensities.includes(analysis.engagementLevel)) {
      errors.push(`Invalid engagement level: ${analysis.engagementLevel}`);
    }

    if (!Array.isArray(analysis.engagementPatterns)) {
      errors.push('Engagement patterns must be an array');
    }

    if (!analysis.readinessScore || typeof analysis.readinessScore.score !== 'number') {
      errors.push('Readiness score must have a numeric score property');
    }

    return errors;
  }

  /**
   * Validate therapeutic approach info structure
   */
  private validateTherapeuticApproachInfo(info: TherapeuticApproachInfo): string[] {
    const errors: string[] = [];

    if (!info.id || !info.name) {
      errors.push('Therapeutic approach must have id and name properties');
    }

    if (!info.selectedTechnique || !info.selectedTechnique.id || !info.selectedTechnique.name) {
      errors.push('Selected technique must have id and name properties');
    }

    return errors;
  }

  /**
   * Validate mode-specific analysis structure
   */
  private validateModeSpecificAnalysis(analysis: ModeSpecificAnalysis): string[] {
    const errors: string[] = [];

    // Basic structure validation - specific validation would depend on the exact structure
    if (!analysis || typeof analysis !== 'object') {
      errors.push('Mode-specific analysis must be an object');
    }

    return errors;
  }
}

// Export singleton instance
export const dataValidationService = new DataValidationService();
