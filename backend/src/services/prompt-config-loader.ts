// Prompt Configuration Loader Service for MiCA Therapy Simulation
// Centralized service for loading and managing all prompt configurations

import fs from 'fs/promises';
import path from 'path';

export interface PromptMetadata {
  version: string;
  description: string;
  lastUpdated: string;
  categories: string[];
}

export interface TherapistPromptConfig {
  metadata: PromptMetadata;
  system_prompts: Record<string, {
    id: string;
    name: string;
    purpose: string;
    therapeutic_approach: string;
    content: string;
  }>;
  therapeutic_approaches: Record<string, {
    id: string;
    name: string;
    description: string;
    philosophy: string;
    system_prompt_addition: string;
    techniques: Record<string, {
      id: string;
      name: string;
      description: string;
      prompt: string;
    }>;
  }>;
  analysis_prompts: Record<string, {
    id: string;
    purpose: string;
    content: string;
  }>;
  response_templates: Record<string, {
    id: string;
    purpose: string;
    content: string;
  }>;
  persona_specific: Record<string, {
    id: string;
    name: string;
    description: string;
    system_prompt_addition: string;
    approach_rationale: string;
  }>;
  thinking_prompts: Record<string, {
    id: string;
    purpose: string;
    content: string;
  }>;
}

export interface PatientPromptConfig {
  metadata: PromptMetadata;
  persona_templates: Record<string, {
    id: string;
    name: string;
    purpose: string;
    content: string;
  }>;
  personas: Record<string, {
    id: string;
    name: string;
    age: number;
    background: string;
    sessionContext: string;
    relevantHistory: string;
    cognitiveConceptualization: {
      coreBeliefs: string[];
      intermediateBeliefs: string[];
      intermediateBeliefsDepression?: string[];
      copingStrategies: string[];
    };
    currentSituation: string;
    automaticThoughts: string[];
    emotions: {
      primary: string[];
      secondary?: string[];
      intensityLevels?: Record<string, 'low' | 'medium' | 'high'>;
    };
    behaviors: {
      maladaptive: string[];
      copingMechanisms: string[];
      behavioralPatterns: string[];
    };
    conversationalStyle: string;
    presentingConcerns: string[];
  }>;
  response_templates: Record<string, {
    id: string;
    purpose: string;
    content: string;
  }>;
}

export interface SharedPromptConfig {
  metadata: PromptMetadata;
  evaluation_prompts: Record<string, {
    id: string;
    name: string;
    purpose: string;
    source: string;
    content: string;
  }>;
  cbt_evaluation: Record<string, {
    id: string;
    name: string;
    purpose: string;
    source: string;
    content: string;
  }>;
  motivational_interviewing_evaluation: Record<string, {
    id: string;
    name: string;
    purpose: string;
    source: string;
    content: string;
  }>;
  therapeutic_scenarios: {
    session_phases: Record<string, {
      id: string;
      name: string;
      description: string;
      typical_duration: string;
      goals: string[];
      therapist_focus: string;
    }>;
    common_scenarios: Record<string, {
      id: string;
      name: string;
      description: string;
      key_elements: string[];
      therapist_approach: string;
    }>;
  };
  general_prompts: Record<string, {
    id: string;
    name: string;
    purpose: string;
    source: string;
    content: string;
  }>;
}

export class PromptConfigLoader {
  private static instance: PromptConfigLoader;
  private therapistConfig: TherapistPromptConfig | null = null;
  private patientConfig: PatientPromptConfig | null = null;
  private sharedConfig: SharedPromptConfig | null = null;
  
  private readonly configPath = path.resolve(process.cwd(), 'src/config/prompts');

  private constructor() {}

  static getInstance(): PromptConfigLoader {
    if (!PromptConfigLoader.instance) {
      PromptConfigLoader.instance = new PromptConfigLoader();
    }
    return PromptConfigLoader.instance;
  }

  /**
   * Load therapist prompt configuration
   */
  async getTherapistConfig(): Promise<TherapistPromptConfig> {
    if (!this.therapistConfig) {
      const configPath = path.join(this.configPath, 'therapist-prompts.json');
      const configData = await fs.readFile(configPath, 'utf-8');
      this.therapistConfig = JSON.parse(configData);
    }
    return this.therapistConfig!;
  }

  /**
   * Load patient prompt configuration
   */
  async getPatientConfig(): Promise<PatientPromptConfig> {
    if (!this.patientConfig) {
      const configPath = path.join(this.configPath, 'patient-prompts.json');
      const configData = await fs.readFile(configPath, 'utf-8');
      this.patientConfig = JSON.parse(configData);
    }
    return this.patientConfig!;
  }

  /**
   * Load shared prompt configuration
   */
  async getSharedConfig(): Promise<SharedPromptConfig> {
    if (!this.sharedConfig) {
      const configPath = path.join(this.configPath, 'shared-prompts.json');
      const configData = await fs.readFile(configPath, 'utf-8');
      this.sharedConfig = JSON.parse(configData);
    }
    return this.sharedConfig!;
  }

  /**
   * Get specific therapist prompt by category and key
   */
  async getTherapistPrompt(category: keyof TherapistPromptConfig, key: string): Promise<string> {
    const config = await this.getTherapistConfig();
    const categoryData = config[category] as any;
    
    if (!categoryData || !categoryData[key]) {
      throw new Error(`Therapist prompt not found: ${category}.${key}`);
    }

    return categoryData[key].content || categoryData[key];
  }

  /**
   * Get specific patient prompt by category and key
   */
  async getPatientPrompt(category: keyof PatientPromptConfig, key: string): Promise<string> {
    const config = await this.getPatientConfig();
    const categoryData = config[category] as any;
    
    if (!categoryData || !categoryData[key]) {
      throw new Error(`Patient prompt not found: ${category}.${key}`);
    }

    return categoryData[key].content || categoryData[key];
  }

  /**
   * Get specific shared prompt by category and key
   */
  async getSharedPrompt(category: keyof SharedPromptConfig, key: string): Promise<string> {
    const config = await this.getSharedConfig();
    const categoryData = config[category] as any;
    
    if (!categoryData || !categoryData[key]) {
      throw new Error(`Shared prompt not found: ${category}.${key}`);
    }

    return categoryData[key].content || categoryData[key];
  }

  /**
   * Get patient persona data by ID
   */
  async getPatientPersona(personaId: string): Promise<PatientPromptConfig['personas'][string]> {
    const config = await this.getPatientConfig();
    const persona = config.personas[personaId];
    
    if (!persona) {
      throw new Error(`Patient persona not found: ${personaId}`);
    }

    return persona;
  }

  /**
   * Get therapeutic technique prompt
   */
  async getTherapeuticTechniquePrompt(approach: string, technique: string): Promise<string> {
    const config = await this.getTherapistConfig();
    const approachData = config.therapeutic_approaches[approach];
    
    if (!approachData || !approachData.techniques[technique]) {
      throw new Error(`Therapeutic technique not found: ${approach}.${technique}`);
    }

    return approachData.techniques[technique].prompt;
  }

  /**
   * Clear cached configurations (useful for development/testing)
   */
  clearCache(): void {
    this.therapistConfig = null;
    this.patientConfig = null;
    this.sharedConfig = null;
  }

  /**
   * Reload all configurations
   */
  async reloadConfigurations(): Promise<void> {
    this.clearCache();
    await Promise.all([
      this.getTherapistConfig(),
      this.getPatientConfig(),
      this.getSharedConfig()
    ]);
  }
}

// Export singleton instance
export const promptConfigLoader = PromptConfigLoader.getInstance();
