// Prompt Visualization Routes for MiCA Therapy Simulation
// Provides API endpoints for the prompt construction pipeline visualization
import express from 'express';
import { promptConfigLoader } from '../services/prompt-config-loader.js';
import { TemplateRenderer } from '../services/template-renderer.js';
import { PersonaService } from '../services/persona-service.js';

const router = express.Router();

/**
 * GET /api/prompt-visualization/config
 * Get all prompt configuration data for visualization
 */
router.get('/config', async (_req, res) => {
  try {
    // Load all configuration files
    const [therapistConfig, patientConfig, sharedConfig] = await Promise.all([
      promptConfigLoader.getTherapistConfig(),
      promptConfigLoader.getPatientConfig(),
      promptConfigLoader.getSharedConfig()
    ]);

    // Get patient personas summary
    const personaSummaries = PersonaService.getAllPersonaSummaries();

    res.json({
      success: true,
      data: {
        therapist: therapistConfig,
        patient: patientConfig,
        shared: sharedConfig,
        personas: personaSummaries,
        metadata: {
          totalTherapistPrompts: Object.keys(therapistConfig.system_prompts).length,
          totalPatientPersonas: Object.keys(patientConfig.personas).length,
          totalSharedPrompts: Object.keys(sharedConfig.evaluation_prompts).length,
          lastUpdated: new Date().toISOString()
        }
      }
    });
  } catch (error) {
    console.error('Error fetching prompt configuration:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch prompt configuration',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/prompt-visualization/therapist-pipeline
 * Get the therapist prompt construction pipeline
 */
router.get('/therapist-pipeline', async (req, res) => {
  try {
    const { approach = 'cognitive_behavioral_therapy', technique = 'cognitive_restructuring' } = req.query;

    const therapistConfig = await promptConfigLoader.getTherapistConfig();
    
    // Build the pipeline steps
    const pipeline = {
      step1_base_system_prompt: {
        name: 'Base System Prompt',
        description: 'Core therapist identity and behavior',
        template: therapistConfig.system_prompts.default_therapist.content,
        variables: {},
        output: therapistConfig.system_prompts.default_therapist.content
      },
      step2_therapeutic_approach: {
        name: 'Therapeutic Approach Addition',
        description: 'Add specific therapeutic approach context',
        template: therapistConfig.therapeutic_approaches[approach as string]?.system_prompt_addition || '',
        variables: { approach: approach as string },
        output: therapistConfig.therapeutic_approaches[approach as string]?.system_prompt_addition || ''
      },
      step3_technique_specific: {
        name: 'Technique-Specific Prompt',
        description: 'Add specific therapeutic technique guidance',
        template: therapistConfig.therapeutic_approaches[approach as string]?.techniques[technique as string]?.prompt || '',
        variables: { technique: technique as string },
        output: therapistConfig.therapeutic_approaches[approach as string]?.techniques[technique as string]?.prompt || ''
      },
      step4_final_assembly: {
        name: 'Final Prompt Assembly',
        description: 'Complete therapist prompt ready for AI model',
        template: 'Combined prompt from all previous steps',
        variables: {},
        output: await TemplateRenderer.getTherapistSystemPrompt('default_therapist')
      }
    };

    res.json({
      success: true,
      data: {
        pipeline,
        availableApproaches: Object.keys(therapistConfig.therapeutic_approaches),
        availableTechniques: Object.keys(therapistConfig.therapeutic_approaches[approach as string]?.techniques || {}),
        metadata: {
          selectedApproach: approach,
          selectedTechnique: technique,
          totalSteps: Object.keys(pipeline).length
        }
      }
    });
  } catch (error) {
    console.error('Error building therapist pipeline:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to build therapist pipeline',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/prompt-visualization/patient-pipeline/:personaId
 * Get the patient prompt construction pipeline for a specific persona
 */
router.get('/patient-pipeline/:personaId', async (req, res) => {
  try {
    const { personaId } = req.params;
    
    // Get persona data
    const persona = PersonaService.getPersonaById(personaId);
    if (!persona) {
      return res.status(404).json({
        success: false,
        error: 'Persona not found',
        message: `No persona found with ID: ${personaId}`
      });
    }

    const patientConfig = await promptConfigLoader.getPatientConfig();
    const baseTemplate = patientConfig.persona_templates.base_patient_template.content;

    // Prepare template variables
    const variables = {
      name: persona.name,
      age: persona.age.toString(),
      background: persona.background,
      relevantHistory: persona.relevantHistory,
      sessionContext: persona.sessionContext,
      currentSituation: persona.currentSituation,
      coreBeliefs: persona.cognitiveConceptualization.coreBeliefs.join(', '),
      intermediateBeliefs: persona.cognitiveConceptualization.intermediateBeliefs.join(', '),
      copingStrategies: persona.cognitiveConceptualization.copingStrategies.join(', '),
      automaticThoughts: persona.automaticThoughts.join(', '),
      primaryEmotions: persona.emotions.primary.join(', '),
      secondaryEmotions: persona.emotions.secondary?.join(', ') || '',
      maladaptiveBehaviors: persona.behaviors.maladaptive.join(', '),
      copingMechanisms: persona.behaviors.copingMechanisms.join(', '),
      behavioralPatterns: persona.behaviors.behavioralPatterns.join(', '),
      conversationalStyle: persona.conversationalStyle,
      presentingConcerns: persona.presentingConcerns.join(', ')
    };

    // Build the pipeline steps
    const pipeline = {
      step1_base_template: {
        name: 'Base Patient Template',
        description: 'Generic patient persona template with placeholders',
        template: baseTemplate,
        variables: {},
        output: baseTemplate
      },
      step2_persona_data: {
        name: 'Persona Data Injection',
        description: 'Inject specific persona psychological profile data',
        template: 'Template variables populated with persona-specific data',
        variables: variables,
        output: 'Variables ready for template substitution'
      },
      step3_template_rendering: {
        name: 'Template Variable Substitution',
        description: 'Replace all {{variable}} placeholders with actual data',
        template: 'Template with variables substituted',
        variables: variables,
        output: await TemplateRenderer.renderPatientPrompt(persona)
      },
      step4_final_prompt: {
        name: 'Final Patient Prompt',
        description: 'Complete patient persona prompt ready for AI model',
        template: 'Fully rendered patient prompt',
        variables: {},
        output: await TemplateRenderer.renderPatientPrompt(persona)
      }
    };

    res.json({
      success: true,
      data: {
        persona: {
          id: persona.id,
          name: persona.name,
          age: persona.age,
          background: persona.background
        },
        pipeline,
        templateVariables: variables,
        metadata: {
          totalVariables: Object.keys(variables).length,
          totalSteps: Object.keys(pipeline).length,
          templateLength: baseTemplate.length,
          finalPromptLength: pipeline.step4_final_prompt.output.length
        }
      }
    });
  } catch (error) {
    console.error('Error building patient pipeline:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to build patient pipeline',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/prompt-visualization/template-variables/:personaId
 * Get all template variables for a specific persona
 */
router.get('/template-variables/:personaId', async (req, res) => {
  try {
    const { personaId } = req.params;
    
    const persona = PersonaService.getPersonaById(personaId);
    if (!persona) {
      return res.status(404).json({
        success: false,
        error: 'Persona not found',
        message: `No persona found with ID: ${personaId}`
      });
    }

    // Extract all template variables with their values and metadata
    const variables = {
      basic_info: {
        name: { value: persona.name, type: 'string', description: 'Patient name' },
        age: { value: persona.age, type: 'number', description: 'Patient age' },
        background: { value: persona.background, type: 'string', description: 'Patient background and context' }
      },
      psychological_profile: {
        coreBeliefs: { 
          value: persona.cognitiveConceptualization.coreBeliefs, 
          type: 'array', 
          description: 'Deep-seated beliefs about self, world, and future' 
        },
        intermediateBeliefs: { 
          value: persona.cognitiveConceptualization.intermediateBeliefs, 
          type: 'array', 
          description: 'Rules, attitudes, and assumptions' 
        },
        copingStrategies: { 
          value: persona.cognitiveConceptualization.copingStrategies, 
          type: 'array', 
          description: 'How the patient typically copes with stress' 
        }
      },
      emotional_state: {
        primaryEmotions: { 
          value: persona.emotions.primary, 
          type: 'array', 
          description: 'Primary emotional experiences' 
        },
        secondaryEmotions: { 
          value: persona.emotions.secondary || [], 
          type: 'array', 
          description: 'Secondary emotional reactions' 
        },
        automaticThoughts: { 
          value: persona.automaticThoughts, 
          type: 'array', 
          description: 'Spontaneous thoughts that arise in situations' 
        }
      },
      behavioral_patterns: {
        maladaptiveBehaviors: { 
          value: persona.behaviors.maladaptive, 
          type: 'array', 
          description: 'Problematic behavioral patterns' 
        },
        copingMechanisms: { 
          value: persona.behaviors.copingMechanisms, 
          type: 'array', 
          description: 'Behavioral coping strategies' 
        },
        behavioralPatterns: { 
          value: persona.behaviors.behavioralPatterns, 
          type: 'array', 
          description: 'General behavioral tendencies' 
        }
      },
      communication_style: {
        conversationalStyle: {
          value: persona.conversationalStyle,
          type: 'string',
          description: 'How the patient communicates in therapy'
        },
        presentingConcerns: {
          value: persona.presentingConcerns,
          type: 'array',
          description: 'Main concerns the patient brings to therapy'
        }
      }
    };

    res.json({
      success: true,
      data: {
        personaId,
        personaName: persona.name,
        variables,
        metadata: {
          totalCategories: Object.keys(variables).length,
          totalVariables: Object.values(variables).reduce((sum, category) => sum + Object.keys(category).length, 0)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching template variables:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch template variables',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
