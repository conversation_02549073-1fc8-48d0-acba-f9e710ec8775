// CBT Evaluation Routes for MiCA Therapy Simulation
import express from 'express';
import { CBTEvaluationObserverService } from '../services/agents/cbt-evaluation-observer.js';
import { CBTEvaluationRequest } from '../types/index.js';
import { query } from '../config/database.js';

const router = express.Router();
const evaluationService = new CBTEvaluationObserverService();

/**
 * POST /api/evaluations/evaluate
 * Trigger CBT evaluation for a completed conversation
 */
router.post('/evaluate', async (req, res) => {
  try {
    const evaluationRequest: CBTEvaluationRequest = req.body;

    // Validate request
    if (!evaluationRequest.conversationId || !evaluationRequest.messages || evaluationRequest.messages.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid evaluation request',
        message: 'conversationId and messages are required'
      });
    }

    console.log(`🔍 Starting evaluation for conversation ${evaluationRequest.conversationId}`);

    // Check if evaluation already exists
    try {
      const checkQuery = 'SELECT id FROM cbt_evaluations WHERE conversation_id = $1';
      const checkResult = await query(checkQuery, [evaluationRequest.conversationId]);

      if (checkResult.rows && checkResult.rows.length > 0) {
        return res.status(409).json({
          success: false,
          error: 'Evaluation already exists',
          message: `Evaluation already exists for conversation ${evaluationRequest.conversationId}`
        });
      }
    } catch (checkError) {
      console.error('Error checking existing evaluation:', checkError);
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to check existing evaluation'
      });
    }

    // Perform evaluation
    const evaluationResult = await evaluationService.evaluateSession(evaluationRequest);

    // Store evaluation in database
    const insertEvaluationQuery = `
      INSERT INTO cbt_evaluations (conversation_id, evaluation_data, overall_score, overall_assessment)
      VALUES ($1, $2, $3, $4)
      RETURNING *
    `;

    const evaluationResult_result = await query(insertEvaluationQuery, [
      evaluationRequest.conversationId,
      JSON.stringify(evaluationResult),
      evaluationResult.overallScore,
      evaluationResult.overallAssessment
    ]);

    const savedEvaluation = evaluationResult_result.rows[0];

    if (!savedEvaluation) {
      console.error('Error saving evaluation: No rows returned');
      return res.status(500).json({
        success: false,
        error: 'Database error',
        message: 'Failed to save evaluation result'
      });
    }

    // Store individual dimension scores
    const dimensionInserts = Object.entries(evaluationResult.dimensions).map(([dimensionName, dimension]) => ({
      evaluation_id: savedEvaluation.id,
      dimension_name: dimensionName,
      score: dimension.score,
      criteria: dimension.criteria,
      rationale: dimension.rationale
    }));

    // Store dimension scores in PostgreSQL
    if (dimensionInserts.length > 0) {
      try {
        const dimensionValues = dimensionInserts.map((_, index) =>
          `($${index * 5 + 1}, $${index * 5 + 2}, $${index * 5 + 3}, $${index * 5 + 4}, $${index * 5 + 5})`
        ).join(', ');

        const dimensionParams = [];
        dimensionInserts.forEach(insert => {
          dimensionParams.push(
            insert.evaluation_id,
            insert.dimension_name,
            insert.score,
            JSON.stringify(insert.criteria),
            insert.rationale
          );
        });

        const insertDimensionsQuery = `
          INSERT INTO evaluation_dimensions (evaluation_id, dimension_name, score, criteria, rationale)
          VALUES ${dimensionValues}
        `;

        await query(insertDimensionsQuery, dimensionParams);
      } catch (dimensionError) {
        console.error('Error saving dimension scores:', dimensionError);
        // Continue despite dimension save error - main evaluation is saved
      }
    }

    console.log(`✅ Evaluation completed and saved for conversation ${evaluationRequest.conversationId}`);

    res.json({
      success: true,
      data: evaluationResult,
      message: 'Evaluation completed successfully'
    });

  } catch (error) {
    console.error('❌ Error during evaluation:', error);
    return res.status(500).json({
      success: false,
      error: 'Evaluation failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/evaluations/:conversationId
 * Get evaluation result for a specific conversation
 */
router.get('/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const selectEvaluationQuery = `
      SELECT * FROM cbt_evaluations
      WHERE conversation_id = $1
    `;

    const result = await query(selectEvaluationQuery, [conversationId]);

    if (!result.rows || result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Evaluation not found',
        message: `No evaluation found for conversation ${conversationId}`
      });
    }

    const evaluation = result.rows[0];

    res.json({
      success: true,
      data: evaluation.evaluation_data,
      metadata: {
        id: evaluation.id,
        createdAt: evaluation.created_at,
        updatedAt: evaluation.updated_at
      }
    });

  } catch (error) {
    console.error('❌ Error fetching evaluation:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch evaluation',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/evaluations
 * Get all evaluations with optional filtering
 */
router.get('/', async (req, res) => {
  try {
    const { 
      limit = 50, 
      offset = 0, 
      assessment,
      minScore,
      maxScore 
    } = req.query;

    // Build PostgreSQL query with filters
    let selectQuery = `
      SELECT id, conversation_id, overall_score, overall_assessment, created_at, evaluation_data
      FROM cbt_evaluations
      WHERE 1=1
    `;
    const queryParams = [];
    let paramIndex = 1;

    // Apply filters
    if (assessment) {
      selectQuery += ` AND overall_assessment = $${paramIndex}`;
      queryParams.push(assessment);
      paramIndex++;
    }

    if (minScore) {
      selectQuery += ` AND overall_score >= $${paramIndex}`;
      queryParams.push(Number(minScore));
      paramIndex++;
    }

    if (maxScore) {
      selectQuery += ` AND overall_score <= $${paramIndex}`;
      queryParams.push(Number(maxScore));
      paramIndex++;
    }

    selectQuery += ` ORDER BY created_at DESC LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(Number(limit), Number(offset));

    const result = await query(selectQuery, queryParams);
    const evaluations = result.rows;



    res.json({
      success: true,
      data: evaluations,
      metadata: {
        count: evaluations.length,
        limit: Number(limit),
        offset: Number(offset)
      }
    });

  } catch (error) {
    console.error('❌ Error fetching evaluations:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch evaluations',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * DELETE /api/evaluations/:conversationId
 * Delete evaluation for a specific conversation
 */
router.delete('/:conversationId', async (req, res) => {
  try {
    const { conversationId } = req.params;

    const deleteQuery = `
      DELETE FROM cbt_evaluations
      WHERE conversation_id = $1
    `;

    await query(deleteQuery, [conversationId]);

    res.json({
      success: true,
      message: `Evaluation deleted for conversation ${conversationId}`
    });

  } catch (error) {
    console.error('❌ Error deleting evaluation:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to delete evaluation',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

/**
 * GET /api/evaluations/stats/summary
 * Get evaluation statistics summary
 */
router.get('/stats/summary', async (req, res) => {
  try {
    const statsQuery = `
      SELECT overall_score, overall_assessment
      FROM cbt_evaluations
    `;

    const result = await query(statsQuery, []);
    const stats = result.rows;

    const summary = {
      totalEvaluations: stats.length,
      averageScore: stats.length > 0 ? stats.reduce((sum, evaluation) => sum + evaluation.overall_score, 0) / stats.length : 0,
      assessmentDistribution: {
        excellent: stats.filter(s => s.overall_assessment === 'excellent').length,
        good: stats.filter(s => s.overall_assessment === 'good').length,
        fair: stats.filter(s => s.overall_assessment === 'fair').length,
        poor: stats.filter(s => s.overall_assessment === 'poor').length
      }
    };

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('❌ Error fetching evaluation stats:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch evaluation statistics',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

export default router;
