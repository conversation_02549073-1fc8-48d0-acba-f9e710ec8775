// Mode-Specific Multi-Therapist Routes for MiCA Therapy Simulation
import express from 'express';
import { OpenAIService } from '../services/openai.js';
import { ModeSpecificMultiTherapistService } from '../services/mode-specific-multi-therapist.js';
import { ConversationContext, TherapeuticMode } from '../types/index.js';

const router = express.Router();

// Initialize services
const openaiService = new OpenAIService();
const modeSpecificService = new ModeSpecificMultiTherapistService(openaiService);

/**
 * POST /api/mode-therapist/analyze
 * Test endpoint to analyze a patient message using all three therapeutic modes
 */
router.post('/analyze', async (req, res) => {
  try {
    const { message } = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request',
        message: 'Message is required and must be a string'
      });
    }

    // Create mock conversation contexts for testing
    const mockConversationContexts = {
      cbt: createMockConversationContext('cbt'),
      dbt: createMockConversationContext('dbt'),
      psychodynamic: createMockConversationContext('psychodynamic')
    };

    // Generate mode-specific responses
    const response = await modeSpecificService.generateMultiModeResponse(
      message,
      mockConversationContexts
    );

    res.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Error in mode-specific analysis:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to analyze message',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/mode-therapist/modes
 * Get information about available therapeutic modes
 */
router.get('/modes', (req, res) => {
  try {
    const modes = [
      {
        id: 'CBT',
        name: 'Cognitive Behavioral Therapy',
        description: 'Focuses on identifying and changing unhelpful thought patterns and behaviors',
        primaryFocus: [
          'Cognitive patterns and distortions',
          'Behavioral triggers and responses',
          'Mood tracking and regulation',
          'Structured goal-oriented interventions'
        ],
        analysisFramework: 'CBT-specific client state analysis examining thought distortions, automatic thoughts, behavioral patterns, and readiness for structured interventions'
      },
      {
        id: 'DBT',
        name: 'Dialectical Behavior Therapy',
        description: 'Teaches skills for emotional regulation, distress tolerance, interpersonal effectiveness, and mindfulness',
        primaryFocus: [
          'Emotional regulation and intensity',
          'Distress tolerance and crisis skills',
          'Interpersonal effectiveness',
          'Mindfulness and present-moment awareness'
        ],
        analysisFramework: 'DBT-specific client state analysis examining emotional dysregulation, distress levels, interpersonal patterns, and mindfulness capacity'
      },
      {
        id: 'psychodynamic',
        name: 'Psychodynamic Therapy',
        description: 'Explores unconscious patterns, defense mechanisms, and early relational experiences',
        primaryFocus: [
          'Unconscious patterns and conflicts',
          'Defense mechanisms and their functions',
          'Transference and countertransference dynamics',
          'Early relational experiences and their impact'
        ],
        analysisFramework: 'Psychodynamic-specific client state analysis examining unconscious themes, defensive patterns, relational dynamics, and therapeutic alliance quality'
      }
    ];

    res.json({
      success: true,
      data: modes,
      count: modes.length
    });

  } catch (error) {
    console.error('Error fetching therapeutic modes:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch therapeutic modes',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/mode-therapist/compare
 * Compare how different therapeutic modes would analyze and respond to the same patient message
 */
router.post('/compare', async (req, res) => {
  try {
    const { message, conversationHistory } = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Invalid request',
        message: 'Message is required and must be a string'
      });
    }

    // Create conversation contexts with history if provided
    const conversationContexts = {
      cbt: createConversationContextWithHistory('cbt', conversationHistory?.cbt || []),
      dbt: createConversationContextWithHistory('dbt', conversationHistory?.dbt || []),
      psychodynamic: createConversationContextWithHistory('psychodynamic', conversationHistory?.psychodynamic || [])
    };

    // Generate mode-specific responses
    const response = await modeSpecificService.generateMultiModeResponse(
      message,
      conversationContexts
    );

    // Extract analysis comparisons for easier frontend consumption
    const analysisComparison = {
      patientMessage: message,
      timestamp: response.timestamp,
      modes: {
        CBT: {
          analysis: response.cbtMode.metadata.patientAnalysis,
          therapeuticApproach: response.cbtMode.metadata.therapeuticApproach,
          response: response.cbtMode.message,
          thinking: response.cbtMode.thinking
        },
        DBT: {
          analysis: response.dbtMode.metadata.patientAnalysis,
          therapeuticApproach: response.dbtMode.metadata.therapeuticApproach,
          response: response.dbtMode.message,
          thinking: response.dbtMode.thinking
        },
        psychodynamic: {
          analysis: response.psychodynamicMode.metadata.patientAnalysis,
          therapeuticApproach: response.psychodynamicMode.metadata.therapeuticApproach,
          response: response.psychodynamicMode.message,
          thinking: response.psychodynamicMode.thinking
        }
      }
    };

    res.json({
      success: true,
      data: {
        fullResponse: response,
        comparison: analysisComparison
      }
    });

  } catch (error) {
    console.error('Error in mode comparison:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to compare therapeutic modes',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Helper function to create mock conversation context for testing
 */
function createMockConversationContext(mode: string): ConversationContext {
  return {
    id: `mock-${mode}-conversation`,
    messages: [],
    currentTurn: 0,
    maxTurns: 20,
    status: 'active'
  };
}

/**
 * Helper function to create conversation context with history
 */
function createConversationContextWithHistory(mode: string, history: any[]): ConversationContext {
  const messages = history.map((msg, index) => ({
    id: `${mode}-msg-${index}`,
    conversationId: `mock-${mode}-conversation`,
    sender: msg.sender || 'patient',
    content: msg.content || '',
    thinking: msg.thinking || '',
    metadata: {
      confidence: msg.confidence || 0.8,
      processingTime: msg.processingTime || 1000
    },
    timestamp: msg.timestamp || new Date().toISOString()
  }));

  return {
    id: `mock-${mode}-conversation`,
    messages,
    currentTurn: messages.length,
    maxTurns: 20,
    status: 'active'
  };
}

export default router;
