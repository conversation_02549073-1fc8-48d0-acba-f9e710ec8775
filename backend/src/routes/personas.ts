// Persona Routes for MiCA Therapy Simulation
import express from 'express';
import { PersonaService } from '../services/persona-service.js';
import { TemplateRenderer } from '../services/template-renderer.js';

const router = express.Router();

/**
 * GET /api/personas
 * Get all available patient personas (summary view for selection)
 */
router.get('/', (req, res) => {
  try {
    const personaSummaries = PersonaService.getAllPersonaSummaries();
    res.json({
      success: true,
      data: personaSummaries,
      count: personaSummaries.length
    });
  } catch (error) {
    console.error('Error fetching persona summaries:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch persona summaries',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/personas/:id
 * Get detailed information for a specific persona
 */
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const persona = PersonaService.getPersonaById(id);
    
    if (!persona) {
      return res.status(404).json({
        success: false,
        error: 'Persona not found',
        message: `No persona found with ID: ${id}`
      });
    }

    res.json({
      success: true,
      data: persona
    });
  } catch (error) {
    console.error('Error fetching persona details:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch persona details',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/personas/:id/config
 * Get PatientConfig format for a specific persona (used by conversation orchestrator)
 */
router.get('/:id/config', (req, res) => {
  try {
    const { id } = req.params;
    const persona = PersonaService.getPersonaById(id);

    if (!persona) {
      return res.status(404).json({
        success: false,
        error: 'Persona not found',
        message: `No persona found with ID: ${id}`
      });
    }

    const patientConfig = PersonaService.convertToPatientConfig(persona);

    res.json({
      success: true,
      data: patientConfig
    });
  } catch (error) {
    console.error('Error converting persona to config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to convert persona to config',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/personas/:id/prompt
 * Test endpoint to verify template rendering
 */
router.get('/:id/prompt', async (req, res) => {
  try {
    const { id } = req.params;
    const persona = PersonaService.getPersonaById(id);

    if (!persona) {
      return res.status(404).json({
        success: false,
        error: 'Persona not found',
        message: `No persona found with ID: ${id}`
      });
    }

    const renderedPrompt = await TemplateRenderer.renderPatientPrompt(persona);

    res.json({
      success: true,
      data: {
        personaId: id,
        personaName: persona.name,
        renderedPrompt: renderedPrompt
      }
    });
  } catch (error) {
    console.error('Error rendering persona prompt:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to render persona prompt',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
