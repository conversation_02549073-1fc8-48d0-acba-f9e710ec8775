// Therapist Routes for MiCA Therapy Simulation
import express from 'express';
import { defaultConversationConfig } from '../config/conversation.js';

const router = express.Router();

/**
 * GET /api/therapist/profile
 * Get therapist profile information
 */
router.get('/profile', (req, res) => {
  try {
    const therapistConfig = defaultConversationConfig.therapist;
    
    const therapistProfile = {
      name: therapistConfig.persona.name,
      credentials: "PhD, LCSW", // Could be added to config later
      specialization: therapistConfig.persona.specialties.join(', '),
      approach: therapistConfig.persona.approach,
      experience: "12 years", // Could be added to config later
      sessions: "2,400+ completed", // Could be added to config later
      background: therapistConfig.persona.background,
      communicationStyle: therapistConfig.persona.communicationStyle,
      techniques: therapistConfig.techniques,
      behavior: therapistConfig.behavior
    };

    res.json({
      success: true,
      data: therapistProfile
    });
  } catch (error) {
    console.error('Error fetching therapist profile:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch therapist profile',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
