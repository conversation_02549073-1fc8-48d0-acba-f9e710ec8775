{"metadata": {"version": "1.0.0", "description": "Shared prompts used by both therapist and patient components in MiCA therapy simulation system", "lastUpdated": "2025-09-29", "categories": ["evaluation_prompts", "session_structures", "therapeutic_scenarios", "cognitive_distortion_detection", "cbt_evaluation"]}, "evaluation_prompts": {"cognitive_distortion_detection": {"id": "cognitive_distortion_detection", "name": "Cognitive Distortion Detection", "purpose": "Detect and classify cognitive distortions in patient utterances", "source": "COCOA.md", "content": "Types of cognitive distortion is given. Search cognitive distortion just from utterance.\nEven if the given utterance consists of multiple sentences, consider it as one utterance and identify cognitive distortions.\nIf there are multiple types of cognitive distortions, output the most likely type of cognitive distortion.\nAlso, assign a severity score from 1 to 5 on a Likert scale for the cognitive distortion.\nOutput must be JSON format with three keys (type, utterance, score).\nIn JSON, both keys and values should be enclosed in double quotes.\n\nRecent Utterances:\n{latest dialogue}"}, "cbt_technique_selection": {"id": "cbt_technique_selection", "name": "CBT Technique Selection", "purpose": "Select appropriate CBT technique based on cognitive distortion", "source": "COCOA.md", "content": "You are an expert in CBT techniques and a counseling agent.\nType of cognitive distortion to treat: {distortion_to_treat}\nRelevant information about the client associated with that cognitive distortion: {memory}\nGiven the cognitive distortion to treat and the relevant information, decide which CBT technique to utilize from the below.\nChoose only one CBT techniques from given CBT Techniques and print out only the CBT techniques for the answers."}, "cbt_stage_selection": {"id": "cbt_stage_selection", "name": "CBT Stage Selection", "purpose": "Determine appropriate stage of CBT technique to apply", "source": "COCOA.md", "content": "You are going to apply [CBT technique] in counseling using CBT technique.\n[CBT progress] is the sequence of [CBT Technique].\n\nThe following dictionary represents CBT usage log, which is the mapping of CBT techniques to the stage of each technique indicating the number of stage completed.\n[CBT Usage Log]\n\nThe conversation below is a conversation in which [CBT Technique] has been applied.\n[CBT dialogue]\n\nWhat is the stage number you would undertake for [CBT Technique] based on the conversation provided, the sequence of the CBT Technique and current dialogue state?\nPsychological counseling should follow the process.\n\nOutput: {stage number}"}}, "cbt_evaluation": {"validity_evaluation": {"id": "cbt_validity_evaluation", "name": "CBT Technique Validity Evaluation", "purpose": "Evaluate if CBT technique is appropriate for addressing dysfunctional thoughts", "source": "COCOA-CBT-Evaluation.md", "content": "You will be given the conversation between a counselor T and a client P.\nThe counselor T is conducting counseling using Cognitive Behavior Therapy techniques.\nYou are also provided with a evaluation question and criteria to assess the counselor T's responses.\nYour task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.\nGrade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.\nOutput only the score.\n\nConversation:\n{conversation}\n\nEvaluation Question:\nIs the utilized CBT technique appropriate for addressing dysfunctional thoughts?\n\nCriteria:\n - Score 0 : Not appropriate\n - Score 2 : Not highly suitable for addressing the targeted dysfunctional thoughts.\n - Score 4 : The technique is appropriate. However, considering the client's core issues, there may be other optimal techniques available.\n - Score 6 : The optimal technique is selected based on valid rationale, considering the targeted dysfunctional thoughts and the client's core issues."}, "appropriateness_evaluation": {"id": "cbt_appropriateness_evaluation", "name": "CBT Appropriateness Evaluation", "purpose": "Evaluate if counselor maintains facilitative stance when using CBT techniques", "source": "COCOA-CBT-Evaluation.md", "content": "You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.\nYou are also provided with a evaluation question and criteria to assess the counselor T's responses.\nYour task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.\nGrade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.\nOutput only the score.\n\nConversation:\n{conversation}\n\nEvaluation Question:\nDoes the counselor T maintain a facilitative stance and cooperative attitude when using CBT techniques?\n\nCriteria:\n - Score 0 : Significant presence of argumentative, persuasive, or instructional attitude (if the client feels coerced into a particular perspective or experiences discomfort leading to a defensive stance, this applies).\n - Score 2 : Some presence of argumentation or persuasion, but also observed to have a cooperative and supportive attitude (the client does not feel attacked or pressured, nor does it feel overly persistent).\n - Score 4 : Mostly facilitated new perspectives through appropriate questioning (techniques) rather than argumentation or persuasion.\n - Score 6 : Extremely skillful in using appropriate questioning (techniques) to help the client explore issues and come to their own conclusions. Consistently maintains a cooperative attitude."}, "accuracy_evaluation": {"id": "cbt_accuracy_evaluation", "name": "CBT Accuracy Evaluation", "purpose": "Evaluate if CBT techniques are used accurately and proficiently", "source": "COCOA-CBT-Evaluation.md", "content": "You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.\nYou are also provided with a evaluation question and criteria to assess the counselor T's responses.\nYour task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.\nGrade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.\nOutput only the score.\n\nConversation:\n{conversation}\n\nEvaluation Question:\nIs the use of CBT techniques accurate and proficient?\n\nCriteria:\n - Score 0 : The use of techniques is completely incorrect (mismatch between the labeled technique and the actual technique used).\n - Score 2 : The labeled technique is used, but key questions are missing or significant portions of the main procedure are omitted, or all procedures that should be sequentially conducted are included within a single utterance.\n - Score 4 : The labeled technique is used, and over 80% of the main procedure is included.\n - Score 6 : In addition to being coded as 4, the technique is flexibly modified based on the client's situation or immediate reactions, ensuring that the core elements of the technique are not distorted."}, "contextual_appropriateness": {"id": "contextual_appropriateness_evaluation", "name": "Contextual Appropriateness Evaluation", "purpose": "Evaluate if responses stay within conversation context and common sense", "source": "COCOA-CBT-Evaluation.md", "content": "You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.\nYou are also provided with a evaluation question and criteria to assess the counselor T's responses.\nYour task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.\nGrade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.\nOutput only the score.\n\nConversation:\n{conversation}\n\nEvaluation Question:\nDoes the utterance stay within the context of the preceding conversation or general common sense level?\n\nCriteria:\n - Score 0 : The utterance is completely unrelated to the context or beyond the realm of common sense (non sequitur, inappropriate utterance).\n - Score 2 : The utterance does not sufficiently consider the information mentioned in the preceding conversation, the client's situation, perspective, or emotions\n - Score 4 : Generally appropriate.\n - Score 6 : Generally appropriate, with sufficient consideration of the client's emotional distress and attempts at empathy and comfort. However, excessive empathy, consideration, or comforting beyond what the client expressed should be avoided"}, "stability_evaluation": {"id": "stability_evaluation", "name": "Long-term Performance Stability Evaluation", "purpose": "Evaluate if counselor maintains good performance over long interactions", "source": "COCOA-CBT-Evaluation.md", "content": "You will be given the conversation between a counselor T and a client P. The counselor T is conducting counseling using Cognitive Behavior Therapy techniques.\nYou are also provided with a evaluation question and criteria to assess the counselor T's responses.\nYour task is to give a score based on criteria. Do not give a full score of 6 points whenever possible.\nGrade very strictly and assign a score of 4 or lower if there is any deficiency, no matter how minor.\nOutput only the score.\n\nConversation:\n{conversation}\n\nEvaluation Question:\nIs the counselor T maintain a good performance over the long interactions?\n\nCriteria:\n - Score 0 : Counselor T shows minimal to no performance during long interactions. They fail to maintain conversation, struggle to express empathy and understanding, and lack proficiency in problem-solving.\n - Score 2 : Counselor T fails to consistently demonstrate performance during long interactions. They may lack consistency or efficiency in maintaining conversation and struggle to express empathy and understanding.\n - Score 4 : Counselor T shows satisfactory performance in most long interactions.\n - Score 6 : Counselor T demonstrates consistent high-quality counseling performance during long interactions."}}, "motivational_interviewing_evaluation": {"counselor_utterance_classification": {"id": "mi_counselor_classification", "name": "MI Counselor Utterance Classification", "purpose": "Classify counselor utterances in Motivational Interviewing sessions", "source": "AutoMISC.md", "content": "You are a highly accurate Motivational Interviewing (MI) counselling session annotator.\nYour task is to analyze an excerpt from a counselling session of up to five volleys and categorize the counsellor's final utterance.\n\nBased on the following excerpt, determine which category the counsellor's last utterance falls into and respond accordingly.\nAfter you're done, go back over the RQ category and assign a subcategory of \"R\" for reflection or \"Q\" for question."}, "client_utterance_classification": {"id": "mi_client_classification", "name": "MI Client Utterance Classification", "purpose": "Classify client utterances in Motivational Interviewing sessions", "source": "AutoMISC.md", "content": "You are a highly accurate Motivational Interviewing (MI) counselling session annotator.\nYour task is to analyze client utterances and classify them according to MI principles."}}, "therapeutic_scenarios": {"session_phases": {"opening": {"id": "session_opening", "name": "Session Opening Phase", "description": "Initial phase of therapy session focused on rapport building and assessment", "typical_duration": "First 4-6 exchanges", "goals": ["Build rapport", "Assess current state", "Establish session agenda"], "therapist_focus": "Warm greeting, active listening, initial assessment"}, "middle": {"id": "session_middle", "name": "Session Middle Phase", "description": "Working phase of therapy session focused on exploration and intervention", "typical_duration": "Exchanges 7-16", "goals": ["Explore issues", "Apply techniques", "Work toward insights"], "therapist_focus": "Therapeutic techniques, deeper exploration, skill building"}, "closing": {"id": "session_closing", "name": "Session Closing Phase", "description": "Final phase focused on summarization and planning", "typical_duration": "Final 4+ exchanges", "goals": ["Summarize session", "Plan next steps", "Assign homework"], "therapist_focus": "Summary, planning, encouragement, closure"}}, "common_scenarios": {"first_session": {"id": "first_session_scenario", "name": "First Therapy Session", "description": "Initial intake and rapport building session", "key_elements": ["Introductions", "Presenting concerns", "History taking", "Goal setting"], "therapist_approach": "Warm, welcoming, assessment-focused"}, "crisis_intervention": {"id": "crisis_intervention_scenario", "name": "Crisis Intervention", "description": "Session addressing immediate crisis or safety concerns", "key_elements": ["Safety assessment", "Crisis stabilization", "Resource connection"], "therapist_approach": "Direct, supportive, solution-focused"}, "resistance_handling": {"id": "resistance_handling_scenario", "name": "Handling Patient Resistance", "description": "Session where patient shows resistance to therapy or change", "key_elements": ["Acknowledge resistance", "Explore ambivalence", "Roll with resistance"], "therapist_approach": "Non-confrontational, curious, validating"}}}, "general_prompts": {"final_cbt_prompt": {"id": "final_cbt_prompt", "name": "Final CBT Response Generation", "purpose": "Generate CBT-informed therapeutic responses", "source": "COCOA.md", "content": "You are a psychotherapist who uses Cognitive Behavioral Therapy to treat patients of all types.\nYour task is to generate a response following the below instructions.\n 1. Generate response based on given informations: recent utterances, CBT technique to employ, the description of CBT technique, stage of CBT technique you should go on, utterance example of the stage you should go on.\n 2. If CBT technique to employ and the description of CBT technique is None, don't use the CBT technique.\n 3. Select one of the given ESC techniques and generate a supportive response in the client's dialogue providing emotional support.\n 4. Do not mention specific CBT techniques or steps you are looking to apply concretely.\n\nRecent Utterances:\n{latest dialogue}\n\nCBT Technique to Employ:\n{CBT technique}\n\nDescription of CBT Technique:\n{CBT documentation}\n\nCBT Stage to Employ:\n{CBT stage}\n\nUtterance Example of the Stage:\n{CBT stage example}"}}}