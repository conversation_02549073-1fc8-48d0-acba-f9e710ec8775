# Centralized Prompt Configuration System

This directory contains the centralized JSON configuration files for all prompts used in the MiCA therapy simulation system. This system provides a single source of truth for all AI prompts, making it easier to maintain, update, and expand therapeutic content.

## Overview

The centralized prompt system consists of three main configuration files:

- **`therapist-prompts.json`** - All prompts related to therapist AI behavior
- **`patient-prompts.json`** - All prompts and data for patient personas
- **`shared-prompts.json`** - Common prompts used by both therapist and patient components

## Configuration Files

### therapist-prompts.json

Contains all therapist-related prompts organized into categories:

- **system_prompts** - Base system prompts for different therapist configurations
- **therapeutic_approaches** - CBT and MI approach definitions with technique-specific prompts
- **analysis_prompts** - Prompts for sentiment, motivation, engagement, and risk assessment
- **response_templates** - Templates for initial greetings and general responses
- **persona_specific** - Prompts for CBT-only, MI-fixed, and dynamic-adaptive personas
- **thinking_prompts** - Internal reasoning prompts for therapist responses

### patient-prompts.json

Contains all patient persona data and prompts:

- **persona_templates** - Base template structure for patient personas
- **personas** - Complete psychological profiles for all 5 patient personas:
  - `emma_anxiety` - Graduate student with anxiety and perfectionism
  - `marcus_depression` - Divorced father struggling with depression
  - `sarah_trauma` - Nurse with PTSD from car accident
  - `david_social_anxiety` - Software developer with social anxiety
  - `maya_bipolar` - Graphic designer with bipolar disorder
- **response_templates** - Templates for patient responses and emotional states

### shared-prompts.json

Contains prompts used by both therapist and patient systems:

- **evaluation_prompts** - Cognitive distortion detection and CBT technique selection
- **cbt_evaluation** - CBT validity, appropriateness, accuracy, and stability evaluations
- **motivational_interviewing_evaluation** - MI counselor and client utterance classification
- **therapeutic_scenarios** - Session phases and common therapeutic scenarios
- **general_prompts** - Final CBT response generation and other shared prompts

## Usage

### Loading Configurations

Use the `PromptConfigLoader` service to access configurations:

```typescript
import { promptConfigLoader } from '../services/prompt-config-loader.js';

// Load therapist configuration
const therapistConfig = await promptConfigLoader.getTherapistConfig();

// Load patient configuration  
const patientConfig = await promptConfigLoader.getPatientConfig();

// Load shared configuration
const sharedConfig = await promptConfigLoader.getSharedConfig();
```

### Using the Prompt Service

The `PromptConfigService` provides high-level methods for accessing prompts:

```typescript
import { promptConfigService } from '../services/prompt-config-service.js';

// Get therapist system prompt
const systemPrompt = await promptConfigService.getTherapistSystemPrompt();

// Get analysis prompts
const analysisPrompts = await promptConfigService.getAnalysisPrompts();

// Get patient persona data
const emmaPersona = await promptConfigService.getPatientPersonaData('emma_anxiety');

// Get therapeutic technique prompt
const techniquePrompt = await promptConfigService.getTherapeuticTechniquePrompt('cognitive_behavioral_therapy', 'cognitive_restructuring');
```

### Template Rendering

The `TemplateRenderer` has been updated to use centralized configurations:

```typescript
import { TemplateRenderer } from '../services/template-renderer.js';

// Render patient prompt with persona data
const patientPrompt = await TemplateRenderer.renderPatientPrompt(persona);

// Get therapist system prompt
const systemPrompt = await TemplateRenderer.getTherapistSystemPrompt();
```

## Configuration Structure

Each configuration file follows this structure:

```json
{
  "metadata": {
    "version": "1.0.0",
    "description": "Description of the configuration file",
    "lastUpdated": "2025-09-29",
    "categories": ["list", "of", "categories"]
  },
  "category_name": {
    "item_key": {
      "id": "unique_identifier",
      "name": "Human readable name",
      "purpose": "Description of purpose",
      "content": "The actual prompt text"
    }
  }
}
```

## Benefits

1. **Centralized Management** - All prompts in one place, easy to find and update
2. **Version Control** - Track changes to therapeutic content over time
3. **Consistency** - Ensure consistent prompt structure and quality
4. **Metadata** - Rich metadata for each prompt including purpose and source
5. **Fallback Support** - Graceful degradation if configuration files are unavailable
6. **Easy Expansion** - Simple to add new therapeutic approaches or patient personas

## Testing

Run the prompt configuration test to verify everything is working:

```bash
cd backend
npm run build
node test-prompts.js
```

This will test all configuration loading, prompt retrieval, and integration points.

## Maintenance

### Adding New Prompts

1. Add the prompt to the appropriate JSON file
2. Update the TypeScript interfaces if needed
3. Add methods to access the new prompt in the service layer
4. Test the integration

### Updating Existing Prompts

1. Modify the prompt content in the JSON file
2. Update the version number and lastUpdated date
3. Test that existing functionality still works

### Adding New Patient Personas

1. Add the persona data to `patient-prompts.json` under the `personas` section
2. Follow the existing structure with complete psychological profiles
3. Update the persona service to handle the new persona
4. Test with the template renderer

## Migration Notes

The system maintains backward compatibility with existing hardcoded prompts through fallback mechanisms. If a centralized prompt cannot be loaded, the system will fall back to the original hardcoded values and log a warning.

This allows for gradual migration and ensures the system remains functional even if configuration files are missing or corrupted.
