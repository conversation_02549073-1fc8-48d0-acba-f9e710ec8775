// Database Configuration for MiCA Therapy Simulation
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { Pool, Client, PoolClient } from 'pg';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

// Validate required environment variables
const databaseUrl = process.env['DATABASE_URL'];

if (!databaseUrl) {
  throw new Error('DATABASE_URL environment variable is required');
}

// Create PostgreSQL connection pool for efficient connection management
export const pool = new Pool({
  connectionString: databaseUrl,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// Handle pool errors
pool.on('error', (err) => {
  console.error('❌ Unexpected error on idle client', err);
  process.exit(-1);
});

// Export a function to get a client from the pool
export async function getClient(): Promise<PoolClient> {
  return await pool.connect();
}

// Export a function to execute queries directly
export async function query(text: string, params?: any[]): Promise<any> {
  const start = Date.now();
  const res = await pool.query(text, params);
  const duration = Date.now() - start;
  console.log('🔍 Executed query', { text, duration, rows: res.rowCount });
  return res;
}

// Database connection test
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const result = await query('SELECT NOW() as current_time, version() as pg_version');

    if (result.rows.length > 0) {
      console.log('✅ Database connection successful');
      console.log(`   Current time: ${result.rows[0].current_time}`);
      console.log(`   PostgreSQL version: ${result.rows[0].pg_version.split(' ')[0]} ${result.rows[0].pg_version.split(' ')[1]}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    return false;
  }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('🔄 Gracefully shutting down database connections...');
  await pool.end();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔄 Gracefully shutting down database connections...');
  await pool.end();
  process.exit(0);
});

// Initialize database connection on module load
testDatabaseConnection();
