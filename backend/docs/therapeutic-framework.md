# Enhanced Therapeutic Framework Documentation

## Overview

The MiCA Therapy Simulation system implements a dual-approach therapeutic framework that dynamically selects between Cognitive Behavioral Therapy (CBT) and Motivational Interviewing (MI) based on real-time patient assessment.

## Core Components

### 1. Patient State Analysis

The system performs comprehensive analysis of each patient message to assess:

#### Basic Analysis
- **Sentiment**: positive, negative, or neutral emotional tone
- **Sentiment Intensity**: low, medium, or high emotional intensity
- **Motivation Level**: low, medium, or high willingness to change
- **Motivation Type**: intrinsic, extrinsic, or mixed motivation sources
- **Engagement Level**: low, medium, or high participation in therapy
- **Engagement Patterns**: specific behavioral patterns (e.g., openness, resistance, curiosity)

#### Analysis Methods
- `analyzeSentiment()`: Determines emotional tone using AI analysis
- `analyzeSentimentIntensity()`: Measures emotional intensity
- `analyzeMotivation()`: Assesses willingness to change
- `analyzeMotivationType()`: Identifies motivation sources
- `analyzeEngagement()`: Evaluates participation level
- `analyzeEngagementPatterns()`: Identifies specific behavioral patterns

### 2. Readiness Assessment

The system calculates a readiness score (1-10 scale) using weighted factors:

#### Scoring Factors
- **Sentiment** (weight: 0.2): Emotional state contribution
- **Sentiment Intensity** (weight: 0.1): Emotional intensity contribution
- **Motivation** (weight: 0.25): Motivation level contribution
- **Motivation Type** (weight: 0.15): Motivation source contribution
- **Engagement** (weight: 0.2): Engagement level contribution
- **Engagement Patterns** (weight: 0.1): Behavioral pattern contribution

#### Readiness Thresholds
- **Score 1-7**: Patient not ready for CBT → Use Motivational Interviewing
- **Score 8-10**: Patient ready for CBT → Use Cognitive Behavioral Therapy

#### Enhanced Features
- **Transparent Calculation**: Detailed breakdown of factor contributions
- **Reasoning**: Natural language explanation of the score
- **Indicators**: Positive and negative indicators for clinical insight

### 3. Therapeutic Approaches

#### Motivational Interviewing (MI)
**Philosophy**: Respects patient autonomy and uses their own motivations to facilitate change.

**Techniques**:
- Open-Ended Questions
- Reflective Listening
- Affirmations
- Summarizing
- Eliciting Change Talk
- Rolling with Resistance
- Developing Discrepancy
- Expressing Empathy
- Supporting Self-Efficacy

**Selection Criteria**: Readiness scores 1-7, ambivalent patients, resistance to change

#### Cognitive Behavioral Therapy (CBT)
**Philosophy**: Problems are maintained by unhelpful thinking patterns and behaviors.

**Techniques**:
- Thought Challenging
- Behavioral Activation
- Problem-Solving
- Cognitive Restructuring
- Exposure Planning
- Homework Assignment
- Thought Records
- Activity Scheduling
- Grounding Techniques
- Cost-Benefit Analysis
- Behavioral Experiments

**Selection Criteria**: Readiness scores 8-10, motivated patients, specific problems identified

### 4. Technique Selection

The system uses intelligent technique selection based on:

#### Patient States
- Current emotional state
- Behavioral patterns
- Readiness level
- Session context

#### Selection Logic
1. **Approach Selection**: Based on readiness score threshold
2. **Technique Filtering**: Match techniques to patient states
3. **Context Consideration**: Account for session phase and history
4. **Technique Prioritization**: Select most appropriate technique

#### Session Phases
- **Opening**: Rapport building, assessment
- **Middle**: Active intervention, skill building
- **Closing**: Consolidation, planning

### 5. Frontend Display

The enhanced frontend displays:

#### Patient Analysis
- Sentiment with intensity indicators
- Motivation level with type classification
- Engagement level with behavioral patterns
- Visual badges for quick assessment

#### Readiness Score
- Score with color-coded indicators
- Positive indicators (strengths)
- Areas for focus (challenges)
- Detailed reasoning explanation

#### Therapeutic Approach
- Selected approach (CBT or MI)
- Specific technique being used
- Technique description and rationale

## Implementation Details

### Data Flow
1. Patient message received
2. Comprehensive analysis performed
3. Readiness score calculated
4. Therapeutic approach selected
5. Specific technique chosen
6. Response generated with therapeutic context
7. Analysis displayed in frontend

### Key Files
- `backend/src/data/therapeutic-approaches.ts`: Approach and technique definitions
- `backend/src/services/openai.ts`: Patient analysis and readiness calculation
- `backend/src/services/technique-selector.ts`: Technique selection logic
- `backend/src/services/agents/therapist.ts`: Therapist agent implementation
- `frontend/src/routes/+page.svelte`: Enhanced display components

### Configuration
The system uses configurable weights for readiness calculation, allowing fine-tuning of the assessment algorithm based on clinical requirements.

## Clinical Benefits

### For Therapists
- Real-time patient assessment
- Evidence-based approach selection
- Transparent decision-making process
- Comprehensive session tracking

### For Training
- Demonstrates therapeutic decision-making
- Shows approach selection rationale
- Provides technique-specific guidance
- Tracks patient progress indicators

### For Research
- Quantifiable readiness metrics
- Detailed interaction analysis
- Approach effectiveness tracking
- Pattern recognition capabilities

## Future Enhancements

### Planned Features
- Machine learning-based pattern recognition
- Longitudinal progress tracking
- Outcome prediction models
- Personalized technique recommendations

### Integration Opportunities
- Electronic health records
- Clinical assessment tools
- Supervision platforms
- Training curricula

## Technical Specifications

### API Endpoints
- Patient analysis: Real-time message analysis
- Readiness calculation: Weighted scoring algorithm
- Technique selection: Context-aware selection
- Session tracking: Comprehensive logging

### Performance
- Analysis latency: <2 seconds per message
- Accuracy: Based on validated therapeutic frameworks
- Scalability: Supports multiple concurrent sessions
- Reliability: Fallback mechanisms for error handling

This enhanced therapeutic framework provides a sophisticated, evidence-based approach to therapy simulation that adapts dynamically to patient needs while maintaining transparency and clinical validity.
