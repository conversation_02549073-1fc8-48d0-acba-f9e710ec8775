# MiCA Backend Logging System

This document describes the comprehensive logging system implemented for the MiCA backend, with a focus on OpenAI service operations.

## Overview

The logging system uses Winston as the primary logging library and provides:

- **Structured JSON logging** to files for analysis and monitoring
- **Colorized console output** with emojis for development
- **Service-specific log files** for focused debugging
- **Automatic log rotation** to prevent disk space issues
- **Sensitive data sanitization** to protect API keys and secrets

## Log Files

The system creates the following log files in the `backend/logs/` directory:

### `combined.log`
- Contains all log entries from all services
- JSON format for easy parsing and analysis
- Rotated when it reaches 5MB (keeps 5 files)

### `error.log`
- Contains only error-level log entries
- Useful for monitoring system failures
- Rotated when it reaches 5MB (keeps 5 files)

### `openai.log`
- Contains only OpenAI service-specific log entries
- Includes all API calls, responses, and usage metrics
- Rotated when it reaches 5MB (keeps 3 files)

## Log Levels

The system supports the following log levels (in order of severity):

1. **error** - System errors, API failures, exceptions
2. **warn** - Warnings, invalid responses, fallback scenarios
3. **info** - General operations, API calls, successful completions
4. **debug** - Detailed information, response analysis, confidence calculations

## OpenAI Service Logging

The OpenAI service (`backend/src/services/openai.ts`) includes comprehensive logging for:

### Function Entry/Exit
- Logs when each function starts with input parameters
- Logs successful completion with results and duration
- Logs errors with full error details and duration

### API Call Details
- Model used, temperature, max tokens
- Number of messages in request
- Processing time for each API call

### Usage Metrics
- Token usage (prompt, completion, total)
- API response metadata
- Confidence calculations

### Error Handling
- Specific error types (rate limits, quota exceeded, invalid API key)
- Fallback responses when errors occur
- Detailed error context for debugging

## Usage Examples

### Using the OpenAI Logger

```typescript
import { openaiLogger, logFunctionEntry, logFunctionSuccess, logFunctionError } from '../utils/logger.js';

async function myFunction(param1: string, param2: number) {
  const startTime = Date.now();
  const operation = 'myFunction';
  
  // Log function entry
  logFunctionEntry(openaiLogger, operation, { param1, param2 });
  
  try {
    // Your code here
    const result = await someOperation();
    
    // Log success
    logFunctionSuccess(openaiLogger, operation, result, Date.now() - startTime);
    
    return result;
  } catch (error) {
    // Log error
    logFunctionError(openaiLogger, operation, error, Date.now() - startTime);
    throw error;
  }
}
```

### Using Other Service Loggers

```typescript
import { conversationLogger, therapistLogger, patientLogger } from '../utils/logger.js';

// For conversation orchestrator
conversationLogger.info('Starting new conversation', { conversationId: 'abc123' });

// For therapist agent
therapistLogger.info('Generating therapist response', { patientMessage: 'Hello' });

// For patient agent
patientLogger.info('Analyzing patient emotional state', { mood: 'anxious' });
```

## Configuration

### Environment Variables

- `LOG_LEVEL`: Set the minimum log level (default: 'info')
  - Options: 'error', 'warn', 'info', 'debug'

### Example `.env` Configuration

```bash
LOG_LEVEL=debug  # For development
# LOG_LEVEL=info   # For production
```

## Log Format

### Console Output
```
2025-09-22 20:17:00 ℹ️ info[openai-service]: Starting generateResponse {"metadata":{"operation":"generateResponse","phase":"entry","parameters":{"messagesCount":3}}}
```

### File Output (JSON)
```json
{
  "level": "info",
  "message": "Starting generateResponse",
  "metadata": {
    "operation": "generateResponse",
    "phase": "entry",
    "parameters": {
      "messagesCount": 3,
      "temperature": 0.7,
      "maxTokens": 500
    }
  },
  "service": "openai-service",
  "timestamp": "2025-09-22 20:17:00"
}
```

## Security Features

### Data Sanitization
The logging system automatically redacts sensitive information:
- API keys
- Passwords
- Tokens
- Secrets
- Any field containing these keywords

### Example
```typescript
// Input: { apiKey: "sk-1234567890", message: "Hello" }
// Logged: { apiKey: "[REDACTED]", message: "Hello" }
```

## Monitoring and Analysis

### Log Analysis
Use tools like `jq` to analyze JSON logs:

```bash
# Count API calls by operation
cat logs/openai.log | jq -r '.metadata.operation' | sort | uniq -c

# Find errors in the last hour
cat logs/error.log | jq 'select(.timestamp > "2025-09-22 19:00:00")'

# Calculate average processing time
cat logs/openai.log | jq -r '.metadata.processingTime' | grep -o '[0-9]*' | awk '{sum+=$1; count++} END {print sum/count}'
```

### Performance Monitoring
The logs include timing information for all operations:
- API call duration
- Processing time
- Token usage metrics

## Best Practices

1. **Use appropriate log levels**
   - `error` for failures that need immediate attention
   - `warn` for issues that don't break functionality
   - `info` for normal operations
   - `debug` for detailed troubleshooting

2. **Include context in log messages**
   - Operation names
   - Relevant parameters
   - Processing phases (entry, api-call, success, error)

3. **Use structured logging**
   - Include metadata objects with relevant information
   - Use consistent field names across services

4. **Monitor log file sizes**
   - Logs rotate automatically, but monitor disk usage
   - Adjust rotation settings if needed

## Troubleshooting

### Common Issues

1. **Logs not appearing in service-specific files**
   - Check that you're using the correct logger (e.g., `openaiLogger`)
   - Verify the service name in the log entry

2. **Log files not created**
   - Ensure the `logs/` directory exists
   - Check file permissions

3. **Console output not colorized**
   - Ensure your terminal supports colors
   - Check the `NODE_ENV` environment variable

### Debug Mode
Set `LOG_LEVEL=debug` to see detailed information including:
- Confidence calculations
- Response analysis
- Detailed API response metadata
