{"name": "mica-backend", "version": "1.0.0", "description": "Backend API for MiCA therapy simulation", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "typecheck": "tsc --noEmit", "clean": "rm -rf dist", "db:migrate": "tsx ../scripts/migrate-postgres.ts migrate", "db:migrate:status": "tsx ../scripts/migrate-postgres.ts status", "db:migrate:test": "tsx ../scripts/migrate-postgres.ts test"}, "keywords": ["api", "backend", "therapy", "ai", "websocket"], "author": "MiCA Development Team", "license": "MIT", "devDependencies": {"@types/cors": "^2.8.14", "@types/express": "^4.17.18", "@types/jest": "^29.5.5", "@types/morgan": "^1.9.10", "@types/node": "^20.6.3", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.6", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "ts-jest": "^29.1.1", "tsx": "^3.13.0", "typescript": "^5.2.2"}, "dependencies": {"@langchain/core": "^0.3.77", "@langchain/langgraph": "^0.4.9", "@langchain/openai": "^0.6.13", "@types/pg": "^8.15.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "morgan": "^1.10.0", "openai": "^4.11.1", "pg": "^8.16.3", "uuid": "^9.0.1", "winston": "^3.10.0", "ws": "^8.14.2", "zod": "^3.22.4"}, "engines": {"node": ">=18.0.0"}}